# Agenti Web Application

This is a web application for managing agents (agenti) with Python FastAPI backend and TypeScript frontend.

## Setup Instructions

1. Create a PostgreSQL database named `agenti_db`

2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

3. Initialize the database:
```bash
python init_db.py
```

4. Start the backend server:
```bash
uvicorn main:app --reload
```

## Database Structure

The `agenti` table has the following structure:
- id_user (Integer, Primary Key)
- nome (String)
- cognome (String)
- email (String, Unique)
- password (String)
- reparto (String)
- bloccato (Boolean)

## Environment Variables

Make sure to update the database connection string in `config.py` according to your PostgreSQL setup. 