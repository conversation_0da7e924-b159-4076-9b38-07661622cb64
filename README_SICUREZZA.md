# 🔐 Sistema di Sicurezza SNIP

## 📋 Panoramica

Il sistema di sicurezza SNIP è stato progettato per proteggere l'applicazione da riavvii non autorizzati. Ogni volta che il servizio viene riavviato, viene generato un codice di sicurezza di 6 cifre che viene inviato via email a `<EMAIL>`. Il servizio rimane bloccato fino a quando il codice non viene verificato correttamente.

## 🚀 Funzionalità Principali

- ✅ **Generazione automatica codici**: Codici di 6 cifre generati automaticamente
- ✅ **Invio email automatico**: Codici <NAME_EMAIL>
- ✅ **Interfaccia web**: Pagina dedicata per l'inserimento del codice
- ✅ **Scadenza temporale**: I codici scadono dopo 10 minuti
- ✅ **Servizio Windows**: Integrazione completa con il servizio FastAPIService
- ✅ **Logging completo**: Tracciamento di tutte le operazioni di sicurezza

## 📁 File del Sistema

| File | Descrizione |
|------|-------------|
| `security_manager.py` | Modulo principale del sistema di sicurezza |
| `snip_service.py` | Servizio Windows con sicurezza integrata |
| `start_snip_secure.bat` | Script di avvio sicuro per Windows |
| `start_snip_secure.sh` | Script di avvio sicuro per Linux/Mac |
| `manage_service.bat` | Gestione completa del servizio Windows |
| `templates/security_verification.html` | Interfaccia web per verifica codice |
| `security_state.json` | File di stato del sistema (generato automaticamente) |

## 🔧 Installazione e Configurazione

### 1. Installazione Servizio Windows

```bash
# Esegui come Amministratore
manage_service.bat
```

Oppure manualmente:

```bash
# Attiva virtual environment
venv\Scripts\activate.bat

# Installa dipendenze servizio
pip install pywin32

# Installa il servizio
python snip_service.py install
```

### 2. Configurazione Email

Il sistema utilizza le configurazioni email esistenti di SNIP. Assicurati che siano configurate correttamente:

- **SMTP Host**: smtp.gmail.com
- **SMTP Port**: 587
- **Username**: Il tuo account Gmail
- **Password**: Password app Gmail (non la password normale)
- **Email destinatario**: <EMAIL> (hardcoded per sicurezza)

### 3. Avvio del Servizio

```bash
# Tramite script di gestione
manage_service.bat

# Oppure manualmente
python snip_service.py start
```

## 🔐 Come Funziona

### Processo di Sicurezza

1. **Riavvio Servizio**: Il servizio FastAPIService viene riavviato
2. **Controllo Sicurezza**: Il sistema verifica se l'avvio è autorizzato
3. **Generazione Codice**: Se non autorizzato, genera un codice di 6 cifre
4. **Invio Email**: Il codice viene <NAME_EMAIL>
5. **Blocco Servizio**: L'applicazione rimane accessibile solo per la verifica
6. **Verifica Codice**: L'utente inserisce il codice nella pagina web
7. **Sblocco**: Se corretto, il servizio viene sbloccato completamente

### Flusso di Verifica

```
Riavvio → Controllo → Codice → Email → Verifica → Sblocco
```

## 🌐 Interfaccia Web

### Pagina di Verifica

- **URL**: `http://localhost:8002/security/verify`
- **Funzionalità**:
  - Inserimento codice di 6 cifre
  - Countdown tempo rimanente
  - Messaggi di stato in tempo reale
  - Richiesta nuovo codice

### API Endpoints

| Endpoint | Metodo | Descrizione |
|----------|--------|-------------|
| `/security/status` | GET | Stato del sistema di sicurezza |
| `/security/verify` | GET | Pagina di verifica |
| `/security/verify` | POST | Verifica codice inserito |
| `/security/initiate` | POST | Avvia nuovo processo di sicurezza |

## 📧 Email di Sicurezza

### Formato Email

```
🔐 Codice di Sicurezza SNIP - Riavvio Servizio

Gentile Ettore,

Il servizio SNIP è stato riavviato e richiede la tua autorizzazione per continuare.

🔐 CODICE DI SICUREZZA: 123456

⏰ Il codice scadrà alle: 14:30:00 del 23/07/2025

Per autorizzare il riavvio del servizio:
1. Accedi al sistema SNIP
2. Vai alla pagina di verifica sicurezza
3. Inserisci il codice: 123456

🚨 IMPORTANTE:
- Questo codice è valido solo per 10 minuti
- Il servizio rimarrà bloccato fino alla verifica
- Se non hai richiesto questo riavvio, contatta immediatamente l'amministratore di sistema
```

## 🛠️ Gestione del Servizio

### Comandi Principali

```bash
# Installa servizio
python snip_service.py install

# Avvia servizio
python snip_service.py start

# Ferma servizio
python snip_service.py stop

# Rimuovi servizio
python snip_service.py remove

# Debug mode
python snip_service.py debug
```

### Script di Gestione

Il file `manage_service.bat` fornisce un menu interattivo per:

- 📦 Installare il servizio
- 🚀 Avviare/fermare il servizio
- 📊 Controllare lo stato
- 🔐 Testare il sistema di sicurezza
- 🌐 Aprire la pagina di verifica
- 📝 Visualizzare i log

## 🔍 Risoluzione Problemi

### Problemi Comuni

#### 1. Email non ricevuta

**Cause possibili**:
- Configurazione SMTP errata
- Password app Gmail non configurata
- Firewall che blocca le connessioni

**Soluzioni**:
```bash
# Test configurazione email
curl -X POST http://localhost:8002/debug/test-email-simple

# Verifica configurazioni SMTP
curl http://localhost:8002/debug/smtp-config
```

#### 2. Servizio non si avvia

**Cause possibili**:
- Virtual environment non trovato
- Dipendenze mancanti
- Porta 8002 già in uso

**Soluzioni**:
```bash
# Verifica virtual environment
dir venv\Scripts\python.exe

# Installa dipendenze
pip install -r requirements.txt

# Verifica porta
netstat -an | findstr :8002
```

#### 3. Codice non accettato

**Cause possibili**:
- Codice scaduto (>10 minuti)
- Errore di digitazione
- Problema di sincronizzazione

**Soluzioni**:
- Richiedi un nuovo codice
- Verifica l'orario di sistema
- Controlla i log del servizio

### Log e Debug

#### File di Log

- `snip_service.log`: Log del servizio Windows
- `snip.log`: Log dell'applicazione principale
- `security_state.json`: Stato corrente del sistema di sicurezza

#### Comandi Debug

```bash
# Stato sicurezza
curl http://localhost:8002/security/status

# Test manuale sicurezza
curl -X POST http://localhost:8002/security/initiate

# Verifica codice (esempio)
curl -X POST -H "Content-Type: application/json" -d '{"code":"123456"}' http://localhost:8002/security/verify
```

## ⚙️ Configurazioni Avanzate

### Personalizzazione Tempi

Nel file `security_manager.py`:

```python
# Scadenza codice (default: 10 minuti)
self.code_expiry = datetime.now() + timedelta(minutes=10)

# Email destinatario (hardcoded per sicurezza)
self.admin_email = "<EMAIL>"
```

### Esclusioni Middleware

Nel file `main.py`, sezione middleware:

```python
# Percorsi esclusi dal controllo di sicurezza
excluded_paths = [
    "/security/",
    "/static/",
    "/debug/",
    "/favicon.ico"
]
```

## 🔒 Sicurezza

### Misure di Protezione

- ✅ **Codici temporanei**: Scadenza automatica dopo 10 minuti
- ✅ **Email hardcoded**: Destinatario fisso per evitare manomissioni
- ✅ **Validazione input**: Solo codici numerici di 6 cifre
- ✅ **Logging completo**: Tracciamento di tutti i tentativi
- ✅ **Fallback sicuro**: In caso di errori, permette l'avvio normale

### Best Practices

1. **Monitoraggio**: Controlla regolarmente i log per tentativi sospetti
2. **Email sicura**: Usa password app Gmail, non la password principale
3. **Backup configurazioni**: Mantieni backup delle configurazioni email
4. **Test periodici**: Testa il sistema di sicurezza regolarmente

## 📞 Supporto

### In caso di problemi:

1. **Controlla i log** del servizio e dell'applicazione
2. **Verifica la configurazione** email nelle impostazioni SNIP
3. **Testa la connettività** SMTP
4. **Consulta** questo documento per la risoluzione problemi

### Contatti:

- **Email**: <EMAIL>
- **Sistema**: SNIP - Sistema Navale Integrato Portuale
- **Azienda**: Michele Autuori Srl

---

**🎯 Il sistema di sicurezza SNIP garantisce che solo riavvii autorizzati possano attivare il servizio, proteggendo l'integrità del sistema aziendale.**
