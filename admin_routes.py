#!/usr/bin/env python3
"""
Route API per il sistema di amministrazione SNIP
Endpoint per gestione utenti, configurazioni, audit e statistiche
"""

from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, Form
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from pydantic import BaseModel

from database import get_db
from models import Agente, RuoloEnum, RepartoEnum, SystemConfig, AuditLog, UserSession, SystemStats
from session_manager import require_min_role, get_current_user
from admin_manager import admin_manager
import logging

# Setup logging
logger = logging.getLogger(__name__)

# Router per le API admin
admin_router = APIRouter(prefix="/admin", tags=["Amministrazione"])

# ===== MODELLI PYDANTIC =====

class UserCreate(BaseModel):
    nome: str
    cognome: str
    email: str
    password: str
    reparto: str
    ruolo: str = "USER"
    visibile: str = "no"

class UserUpdate(BaseModel):
    nome: Optional[str] = None
    cognome: Optional[str] = None
    email: Optional[str] = None
    password: Optional[str] = None
    reparto: Optional[str] = None
    ruolo: Optional[str] = None
    visibile: Optional[str] = None

class ConfigCreate(BaseModel):
    config_key: str
    config_value: str
    description: Optional[str] = None
    config_type: str = "string"

class UserResponse(BaseModel):
    id_user: int
    Nome: str
    Cognome: str
    email: str
    reparto: str
    ruolo: str
    visibile: str

    class Config:
        from_attributes = True

# ===== ENDPOINT GESTIONE UTENTI =====

@admin_router.get("/users", response_model=List[UserResponse])
def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene lista utenti con paginazione e ricerca"""
    try:
        if search:
            users = admin_manager.search_users(db, search, skip, limit)
        else:
            users = admin_manager.get_all_users(db, skip, limit)
        
        return users
        
    except Exception as e:
        logger.error(f"Errore get users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/users/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene un utente specifico"""
    user = admin_manager.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="Utente non trovato")
    return user

@admin_router.post("/users", response_model=UserResponse)
def create_user(
    user_data: UserCreate,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Crea un nuovo utente"""
    try:
        # Type hint per il type checker
        user_id: int = current_user.id_user  # type: ignore
        new_user = admin_manager.create_user(
            db=db,
            user_data=user_data.dict(),
            created_by=user_id
        )
        return new_user
        
    except Exception as e:
        logger.error(f"Errore create user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.put("/users/{user_id}", response_model=UserResponse)
def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Aggiorna un utente esistente"""
    try:
        # Filtra solo i campi non None
        update_data = {k: v for k, v in user_data.dict().items() if v is not None}

        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        updated_user = admin_manager.update_user(
            db=db,
            user_id=user_id,
            user_data=update_data,
            updated_by=user_id_int
        )
        return updated_user
        
    except Exception as e:
        logger.error(f"Errore update user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.delete("/users/{user_id}")
def delete_user(
    user_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Elimina un utente (soft delete)"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        success = admin_manager.delete_user(
            db=db,
            user_id=user_id,
            deleted_by=user_id_int
        )
        
        if success:
            return {"message": "Utente eliminato con successo"}
        else:
            raise HTTPException(status_code=404, detail="Utente non trovato")
            
    except Exception as e:
        logger.error(f"Errore delete user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT CONFIGURAZIONI =====

@admin_router.get("/api/configurations")
def get_all_configurations(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene tutte le configurazioni organizzate per sezioni"""
    try:
        # Ottieni tutte le configurazioni
        configs = admin_manager.get_all_configs(db)

        # Organizza per sezioni
        organized_configs = {
            "database": {},
            "security": {},
            "sof": {},
            "email": {},
            "ports": {"salerno": {}, "gioia": {}},
            "interface": {},
            "reporting": {},
            "system": {}
        }

        # Mappa le configurazioni alle sezioni appropriate
        for config in configs:
            key = str(config.config_key or "")
            value = str(config.config_value or "")

            # Configurazioni database/backup
            if key.startswith('backup_') or key in ['log_cleanup', 'archive_months', 'optimize', 'auto_vacuum', 'analyze', 'disk_threshold', 'connection_threshold', 'monitor_performance', 'alert_email']:
                if key == 'backup_time':
                    organized_configs["database"]["backup_time"] = value
                elif key == 'backup_retention':
                    organized_configs["database"]["backup_retention"] = int(value) if value and value.isdigit() else 30
                elif key == 'backup_frequency':
                    organized_configs["database"]["backup_schedule"] = value
                elif key == 'backup_compress':
                    organized_configs["database"]["compress_backup"] = value.lower() in ['true', '1', 'yes'] if value else True
                elif key == 'backup_path':
                    organized_configs["database"]["backup_path"] = value
                else:
                    # Altri campi database
                    field_name = key.replace('_', '_')
                    if value.lower() in ['true', 'false']:
                        organized_configs["database"][field_name] = value.lower() == 'true'
                    elif value.isdigit():
                        organized_configs["database"][field_name] = int(value)
                    else:
                        organized_configs["database"][field_name] = value

            # Configurazioni email
            elif key.startswith('email_'):
                field_name = key.replace('email_', '')
                if field_name == 'smtp_port':
                    organized_configs["email"][field_name] = int(value) if value.isdigit() else 587
                elif field_name == 'smtp_ssl':
                    organized_configs["email"][field_name] = value.lower() in ['true', '1', 'yes']
                else:
                    organized_configs["email"][field_name] = value

            # Configurazioni porti
            elif key.startswith('port_'):
                if 'salerno' in key:
                    field_name = key.replace('port_salerno_', '')
                    organized_configs["ports"]["salerno"][field_name] = value
                elif 'gioia' in key:
                    field_name = key.replace('port_gioia_', '')
                    organized_configs["ports"]["gioia"][field_name] = value

            # Configurazioni sicurezza
            elif key.startswith('security_'):
                field_name = key.replace('security_', '')
                if field_name in ['password_min_length', 'password_expiry_days', 'session_timeout', 'max_login_attempts', 'account_lockout_minutes', 'log_retention']:
                    organized_configs["security"][field_name] = int(value) if value.isdigit() else 0
                elif field_name in ['password_uppercase', 'password_numbers', 'password_special', 'two_factor_auth', 'ip_whitelist', 'log_login', 'log_actions', 'log_errors', 'encrypt_backups', 'ssl_only']:
                    organized_configs["security"][field_name] = value.lower() in ['true', '1', 'yes']
                else:
                    organized_configs["security"][field_name] = value

            # Configurazioni SOF
            elif key.startswith('sof_'):
                field_name = key.replace('sof_', '')
                if field_name in ['logo_top', 'logo_bottom']:
                    organized_configs["sof"][field_name] = value.lower() in ['true', '1', 'yes']
                else:
                    organized_configs["sof"][field_name] = value

            # Configurazioni interfaccia
            elif key.startswith('interface_'):
                field_name = key.replace('interface_', '')
                if field_name in ['responsive_design', 'dark_mode', 'glassmorphism']:
                    organized_configs["interface"][field_name] = value.lower() in ['true', '1', 'yes']
                else:
                    organized_configs["interface"][field_name] = value

            # Configurazioni reporting
            elif key.startswith('reporting_'):
                field_name = key.replace('reporting_', '')
                if field_name in ['kpi_voyages', 'kpi_sof', 'kpi_users', 'kpi_ports', 'kpi_performance', 'kpi_revenue', 'auto_export', 'compress_exports', 'include_charts', 'detailed_reports']:
                    organized_configs["reporting"][field_name] = value.lower() in ['true', '1', 'yes']
                else:
                    organized_configs["reporting"][field_name] = value

            # Configurazioni sistema
            elif key.startswith('system_'):
                field_name = key.replace('system_', '')
                if field_name in ['max_upload_size', 'request_timeout', 'max_connections']:
                    organized_configs["system"][field_name] = int(value) if value.isdigit() else 0
                elif field_name in ['cache_enabled', 'compression_enabled', 'auto_updates']:
                    organized_configs["system"][field_name] = value.lower() in ['true', '1', 'yes']
                else:
                    organized_configs["system"][field_name] = value

        # Valori predefiniti per tutte le sezioni
        # Database
        organized_configs["database"].setdefault("backup_time", "02:00")
        organized_configs["database"].setdefault("backup_retention", 30)
        organized_configs["database"].setdefault("backup_schedule", "daily")
        organized_configs["database"].setdefault("compress_backup", True)
        organized_configs["database"].setdefault("backup_path", "/backups/snip/")
        organized_configs["database"].setdefault("log_cleanup", "weekly")
        organized_configs["database"].setdefault("archive_months", 12)
        organized_configs["database"].setdefault("optimize", "weekly")
        organized_configs["database"].setdefault("auto_vacuum", True)
        organized_configs["database"].setdefault("analyze", True)
        organized_configs["database"].setdefault("disk_threshold", 85)
        organized_configs["database"].setdefault("connection_threshold", 80)
        organized_configs["database"].setdefault("monitor_performance", True)
        organized_configs["database"].setdefault("alert_email", True)

        # Email - Gestione speciale per la password
        organized_configs["email"].setdefault("smtp_host", "smtp.gmail.com")
        organized_configs["email"].setdefault("smtp_port", 587)
        organized_configs["email"].setdefault("smtp_username", "")

        # NON sovrascrivere la password se esiste già (è stata caricata dal database)
        if "smtp_password" not in organized_configs["email"]:
            organized_configs["email"]["smtp_password"] = ""

        organized_configs["email"].setdefault("sender_email", "<EMAIL>")
        organized_configs["email"].setdefault("sender_name", "Michele Autuori Srl")
        organized_configs["email"].setdefault("admin_email", "<EMAIL>")
        organized_configs["email"].setdefault("support_email", "<EMAIL>")
        organized_configs["email"].setdefault("smtp_ssl", True)

        # Porti
        organized_configs["ports"]["salerno"].setdefault("name", "Porto di Salerno")
        organized_configs["ports"]["salerno"].setdefault("code", "ITSAL")
        organized_configs["ports"]["salerno"].setdefault("harbor", "+39 089 123456")
        organized_configs["ports"]["salerno"].setdefault("customs", "+39 089 654321")
        organized_configs["ports"]["salerno"].setdefault("email", "<EMAIL>")

        organized_configs["ports"]["gioia"].setdefault("name", "Porto di Gioia Tauro")
        organized_configs["ports"]["gioia"].setdefault("code", "ITGIT")
        organized_configs["ports"]["gioia"].setdefault("harbor", "+39 0966 123456")
        organized_configs["ports"]["gioia"].setdefault("customs", "+39 0966 654321")
        organized_configs["ports"]["gioia"].setdefault("email", "<EMAIL>")

        # Sicurezza
        organized_configs["security"].setdefault("password_min_length", 6)
        organized_configs["security"].setdefault("password_expiry_days", 90)
        organized_configs["security"].setdefault("session_timeout", 60)
        organized_configs["security"].setdefault("max_login_attempts", 5)
        organized_configs["security"].setdefault("account_lockout_minutes", 15)
        organized_configs["security"].setdefault("log_retention", 90)
        organized_configs["security"].setdefault("password_uppercase", True)
        organized_configs["security"].setdefault("password_numbers", True)
        organized_configs["security"].setdefault("password_special", True)
        organized_configs["security"].setdefault("two_factor_auth", False)
        organized_configs["security"].setdefault("ip_whitelist", False)
        organized_configs["security"].setdefault("log_login", True)
        organized_configs["security"].setdefault("log_actions", True)
        organized_configs["security"].setdefault("log_errors", True)
        organized_configs["security"].setdefault("encrypt_backups", True)
        organized_configs["security"].setdefault("ssl_only", True)

        # SOF
        organized_configs["sof"].setdefault("title", "STATEMENT OF FACTS")
        organized_configs["sof"].setdefault("subtitle", "shipping and forwarding agency")
        organized_configs["sof"].setdefault("logo_size", "medium")
        organized_configs["sof"].setdefault("numbering", "auto")
        organized_configs["sof"].setdefault("logo_top", True)
        organized_configs["sof"].setdefault("logo_bottom", False)

        # Interfaccia
        organized_configs["interface"].setdefault("theme", "maritime")
        organized_configs["interface"].setdefault("primary_color", "#0066cc")
        organized_configs["interface"].setdefault("secondary_color", "#004499")
        organized_configs["interface"].setdefault("accent_color", "#ff6600")
        organized_configs["interface"].setdefault("language", "it")
        organized_configs["interface"].setdefault("timezone", "Europe/Rome")
        organized_configs["interface"].setdefault("date_format", "dd/mm/yyyy")
        organized_configs["interface"].setdefault("sidebar_default", "expanded")
        organized_configs["interface"].setdefault("responsive_design", True)
        organized_configs["interface"].setdefault("dark_mode", False)
        organized_configs["interface"].setdefault("glassmorphism", False)

        # Reporting
        organized_configs["reporting"].setdefault("daily_report", "disabled")
        organized_configs["reporting"].setdefault("daily_report_time", "08:00")
        organized_configs["reporting"].setdefault("weekly_report", "disabled")
        organized_configs["reporting"].setdefault("monthly_report", "disabled")
        organized_configs["reporting"].setdefault("kpi_voyages", True)
        organized_configs["reporting"].setdefault("kpi_sof", True)
        organized_configs["reporting"].setdefault("kpi_users", True)
        organized_configs["reporting"].setdefault("kpi_ports", True)
        organized_configs["reporting"].setdefault("kpi_performance", True)
        organized_configs["reporting"].setdefault("kpi_revenue", False)
        organized_configs["reporting"].setdefault("export_format", "pdf")
        organized_configs["reporting"].setdefault("auto_export", False)
        organized_configs["reporting"].setdefault("compress_exports", True)
        organized_configs["reporting"].setdefault("report_recipients", "")
        organized_configs["reporting"].setdefault("include_charts", True)
        organized_configs["reporting"].setdefault("detailed_reports", False)

        # Sistema
        organized_configs["system"].setdefault("app_version", "1.0.0")
        organized_configs["system"].setdefault("debug_mode", "disabled")
        organized_configs["system"].setdefault("log_level", "INFO")
        organized_configs["system"].setdefault("max_upload_size", 10)
        organized_configs["system"].setdefault("request_timeout", 30)
        organized_configs["system"].setdefault("max_connections", 100)
        organized_configs["system"].setdefault("cache_enabled", True)
        organized_configs["system"].setdefault("compression_enabled", True)
        organized_configs["system"].setdefault("maintenance_mode", "disabled")
        organized_configs["system"].setdefault("maintenance_message", "Sistema in manutenzione")
        organized_configs["system"].setdefault("auto_updates", False)

        return {
            "success": True,
            "configurations": organized_configs
        }

    except Exception as e:
        logger.error(f"Errore get configurations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/api/configurations")
def save_all_configurations(
    configurations: Dict[str, Any],
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Salva tutte le configurazioni dal form admin dashboard"""
    try:
        user_id_int: int = current_user.id_user  # type: ignore
        saved_configs = []

        # Salva configurazioni database/backup
        if "database" in configurations:
            db_config = configurations["database"]
            config_mappings = [
                ("backup_time", db_config.get("backup_time", "02:00")),
                ("backup_retention", str(db_config.get("backup_retention", 30))),
                ("backup_frequency", db_config.get("backup_schedule", "daily")),
                ("backup_compress", str(db_config.get("compress_backup", True)).lower()),
                ("backup_path", db_config.get("backup_path", "/backups/snip/")),
                ("log_cleanup", db_config.get("log_cleanup", "weekly")),
                ("archive_months", str(db_config.get("archive_months", 12))),
                ("optimize", db_config.get("optimize", "weekly")),
                ("auto_vacuum", str(db_config.get("auto_vacuum", True)).lower()),
                ("analyze", str(db_config.get("analyze", True)).lower()),
                ("disk_threshold", str(db_config.get("disk_threshold", 85))),
                ("connection_threshold", str(db_config.get("connection_threshold", 80))),
                ("monitor_performance", str(db_config.get("monitor_performance", True)).lower()),
                ("alert_email", str(db_config.get("alert_email", True)).lower())
            ]
            for key, value in config_mappings:
                config = admin_manager.set_config(
                    db=db, key=key, value=value, description=f"Configurazione database: {key}",
                    config_type="string", user_id=user_id_int
                )
                saved_configs.append(f"{key}={value}")

        # Salva configurazioni email
        if "email" in configurations:
            email_config = configurations["email"]
            email_mappings = [
                ("email_smtp_host", email_config.get("smtp_host", "")),
                ("email_smtp_port", str(email_config.get("smtp_port", 587))),
                ("email_smtp_username", email_config.get("smtp_username", "")),
                ("email_smtp_password", email_config.get("smtp_password", "")),
                ("email_sender_email", email_config.get("sender_email", "")),
                ("email_sender_name", email_config.get("sender_name", "")),
                ("email_admin_email", email_config.get("admin_email", "")),
                ("email_support_email", email_config.get("support_email", "")),
                ("email_smtp_ssl", str(email_config.get("smtp_ssl", True)).lower())
            ]
            for key, value in email_mappings:
                # Gestione speciale per la password SMTP
                if key == "email_smtp_password":
                    # Se la password è vuota o è il token speciale, salta
                    if value == "__KEEP_EXISTING__":
                        continue
                    elif not value or value in ["", "••••••••", "********"]:
                        continue

                # Salva la configurazione
                try:
                    config = admin_manager.set_config(
                        db=db, key=key, value=value, description=f"Configurazione email: {key}",
                        config_type="string", user_id=user_id_int
                    )
                    logger.info(f"✅ Configurazione {key} salvata con successo, ID: {config.id}")
                except Exception as e:
                    logger.error(f"❌ ERRORE salvataggio {key}: {str(e)}")
                    logger.error(f"   Tipo errore: {type(e)}")
                    logger.error(f"   Valore: {'***HIDDEN***' if 'password' in key else value}")
                    raise

                # Per la password, non loggare il valore reale
                if key == "email_smtp_password":
                    saved_configs.append(f"{key}=***HIDDEN***")
                else:
                    saved_configs.append(f"{key}={value}")

        # Salva configurazioni porti
        if "ports" in configurations:
            ports_config = configurations["ports"]
            if "salerno" in ports_config:
                salerno = ports_config["salerno"]
                port_mappings = [
                    ("port_salerno_name", salerno.get("name", "")),
                    ("port_salerno_code", salerno.get("code", "")),
                    ("port_salerno_harbor", salerno.get("harbor", "")),
                    ("port_salerno_customs", salerno.get("customs", "")),
                    ("port_salerno_email", salerno.get("email", ""))
                ]
                for key, value in port_mappings:
                    if value:
                        config = admin_manager.set_config(
                            db=db, key=key, value=value, description=f"Configurazione porto Salerno: {key}",
                            config_type="string", user_id=user_id_int
                        )
                        saved_configs.append(f"{key}={value}")

            if "gioia" in ports_config:
                gioia = ports_config["gioia"]
                port_mappings = [
                    ("port_gioia_name", gioia.get("name", "")),
                    ("port_gioia_code", gioia.get("code", "")),
                    ("port_gioia_harbor", gioia.get("harbor", "")),
                    ("port_gioia_customs", gioia.get("customs", "")),
                    ("port_gioia_email", gioia.get("email", ""))
                ]
                for key, value in port_mappings:
                    if value:
                        config = admin_manager.set_config(
                            db=db, key=key, value=value, description=f"Configurazione porto Gioia: {key}",
                            config_type="string", user_id=user_id_int
                        )
                        saved_configs.append(f"{key}={value}")

        # Salva configurazioni sicurezza
        if "security" in configurations:
            security_config = configurations["security"]
            security_mappings = [
                ("security_password_min_length", str(security_config.get("password_min_length", 6))),
                ("security_password_expiry_days", str(security_config.get("password_expiry_days", 90))),
                ("security_session_timeout", str(security_config.get("session_timeout", 60))),
                ("security_max_login_attempts", str(security_config.get("max_login_attempts", 5))),
                ("security_account_lockout_minutes", str(security_config.get("account_lockout_minutes", 15))),
                ("security_log_retention", str(security_config.get("log_retention", 90))),
                ("security_password_uppercase", str(security_config.get("password_uppercase", True)).lower()),
                ("security_password_numbers", str(security_config.get("password_numbers", True)).lower()),
                ("security_password_special", str(security_config.get("password_special", True)).lower()),
                ("security_two_factor_auth", str(security_config.get("two_factor_auth", False)).lower()),
                ("security_ip_whitelist", str(security_config.get("ip_whitelist", False)).lower()),
                ("security_log_login", str(security_config.get("log_login", True)).lower()),
                ("security_log_actions", str(security_config.get("log_actions", True)).lower()),
                ("security_log_errors", str(security_config.get("log_errors", True)).lower()),
                ("security_encrypt_backups", str(security_config.get("encrypt_backups", True)).lower()),
                ("security_ssl_only", str(security_config.get("ssl_only", True)).lower())
            ]
            for key, value in security_mappings:
                config = admin_manager.set_config(
                    db=db, key=key, value=value, description=f"Configurazione sicurezza: {key}",
                    config_type="string", user_id=user_id_int
                )
                saved_configs.append(f"{key}={value}")

        # Salva configurazioni SOF
        if "sof" in configurations:
            sof_config = configurations["sof"]
            sof_mappings = [
                ("sof_title", sof_config.get("title", "STATEMENT OF FACTS")),
                ("sof_subtitle", sof_config.get("subtitle", "shipping and forwarding agency")),
                ("sof_logo_size", sof_config.get("logo_size", "medium")),
                ("sof_numbering", sof_config.get("numbering", "auto")),
                ("sof_logo_top", str(sof_config.get("logo_top", True)).lower()),
                ("sof_logo_bottom", str(sof_config.get("logo_bottom", False)).lower())
            ]
            for key, value in sof_mappings:
                if value:
                    config = admin_manager.set_config(
                        db=db, key=key, value=value, description=f"Configurazione SOF: {key}",
                        config_type="string", user_id=user_id_int
                    )
                    saved_configs.append(f"{key}={value}")

        # Salva configurazioni interfaccia
        if "interface" in configurations:
            interface_config = configurations["interface"]
            interface_mappings = [
                ("interface_theme", interface_config.get("theme", "maritime")),
                ("interface_primary_color", interface_config.get("primary_color", "#0066cc")),
                ("interface_secondary_color", interface_config.get("secondary_color", "#004499")),
                ("interface_accent_color", interface_config.get("accent_color", "#ff6600")),
                ("interface_language", interface_config.get("language", "it")),
                ("interface_timezone", interface_config.get("timezone", "Europe/Rome")),
                ("interface_date_format", interface_config.get("date_format", "dd/mm/yyyy")),
                ("interface_sidebar_default", interface_config.get("sidebar_default", "expanded")),
                ("interface_responsive_design", str(interface_config.get("responsive_design", True)).lower()),
                ("interface_dark_mode", str(interface_config.get("dark_mode", False)).lower()),
                ("interface_glassmorphism", str(interface_config.get("glassmorphism", False)).lower())
            ]
            for key, value in interface_mappings:
                if value:
                    config = admin_manager.set_config(
                        db=db, key=key, value=value, description=f"Configurazione interfaccia: {key}",
                        config_type="string", user_id=user_id_int
                    )
                    saved_configs.append(f"{key}={value}")

        # Salva configurazioni reporting
        if "reporting" in configurations:
            reporting_config = configurations["reporting"]
            reporting_mappings = [
                ("reporting_daily_report", reporting_config.get("daily_report", "disabled")),
                ("reporting_daily_report_time", reporting_config.get("daily_report_time", "08:00")),
                ("reporting_weekly_report", reporting_config.get("weekly_report", "disabled")),
                ("reporting_monthly_report", reporting_config.get("monthly_report", "disabled")),
                ("reporting_kpi_voyages", str(reporting_config.get("kpi_voyages", True)).lower()),
                ("reporting_kpi_sof", str(reporting_config.get("kpi_sof", True)).lower()),
                ("reporting_kpi_users", str(reporting_config.get("kpi_users", True)).lower()),
                ("reporting_kpi_ports", str(reporting_config.get("kpi_ports", True)).lower()),
                ("reporting_kpi_performance", str(reporting_config.get("kpi_performance", True)).lower()),
                ("reporting_kpi_revenue", str(reporting_config.get("kpi_revenue", False)).lower()),
                ("reporting_export_format", reporting_config.get("export_format", "pdf")),
                ("reporting_auto_export", str(reporting_config.get("auto_export", False)).lower()),
                ("reporting_compress_exports", str(reporting_config.get("compress_exports", True)).lower()),
                ("reporting_report_recipients", reporting_config.get("report_recipients", "")),
                ("reporting_include_charts", str(reporting_config.get("include_charts", True)).lower()),
                ("reporting_detailed_reports", str(reporting_config.get("detailed_reports", False)).lower())
            ]
            for key, value in reporting_mappings:
                if value:
                    admin_manager.set_config(
                        db=db, key=key, value=value, description=f"Configurazione reporting: {key}",
                        config_type="string", user_id=user_id_int
                    )
                    saved_configs.append(f"{key}={value}")

        # Salva configurazioni sistema
        if "system" in configurations:
            system_config = configurations["system"]
            system_mappings = [
                ("system_app_version", system_config.get("app_version", "1.0.0")),
                ("system_debug_mode", system_config.get("debug_mode", "disabled")),
                ("system_log_level", system_config.get("log_level", "INFO")),
                ("system_max_upload_size", str(system_config.get("max_upload_size", 10))),
                ("system_request_timeout", str(system_config.get("request_timeout", 30))),
                ("system_max_connections", str(system_config.get("max_connections", 100))),
                ("system_cache_enabled", str(system_config.get("cache_enabled", True)).lower()),
                ("system_compression_enabled", str(system_config.get("compression_enabled", True)).lower()),
                ("system_maintenance_mode", system_config.get("maintenance_mode", "disabled")),
                ("system_maintenance_message", system_config.get("maintenance_message", "Sistema in manutenzione")),
                ("system_auto_updates", str(system_config.get("auto_updates", False)).lower())
            ]
            for key, value in system_mappings:
                if value:
                    admin_manager.set_config(
                        db=db, key=key, value=value, description=f"Configurazione sistema: {key}",
                        config_type="string", user_id=user_id_int
                    )
                    saved_configs.append(f"{key}={value}")

        logger.info(f"Configurazioni salvate da {current_user.email}: {saved_configs}")

        return {
            "success": True,
            "message": f"Configurazioni salvate con successo: {len(saved_configs)} elementi",
            "saved_configs": saved_configs
        }

    except Exception as e:
        logger.error(f"Errore save configurations: {str(e)}")
        logger.error(f"Configurazioni ricevute: {configurations}")
        return {
            "success": False,
            "message": f"Errore durante il salvataggio delle configurazioni: {str(e)}",
            "error_type": type(e).__name__
        }

@admin_router.get("/config")
def get_all_configs(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene tutte le configurazioni di sistema"""
    try:
        configs = admin_manager.get_all_configs(db)
        return [
            {
                "id": config.id,
                "config_key": config.config_key,
                "config_value": config.config_value,
                "description": config.description,
                "config_type": config.config_type,
                "is_active": config.is_active,
                "created_at": config.created_at.isoformat(),
                "updated_at": config.updated_at.isoformat()
            }
            for config in configs
        ]

    except Exception as e:
        logger.error(f"Errore get configs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/config/{key}")
def get_config(
    key: str,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene una configurazione specifica"""
    config = admin_manager.get_config(db, key)
    if not config:
        raise HTTPException(status_code=404, detail="Configurazione non trovata")
    
    return {
        "id": config.id,
        "config_key": config.config_key,
        "config_value": config.config_value,
        "description": config.description,
        "config_type": config.config_type,
        "is_active": config.is_active,
        "created_at": config.created_at.isoformat(),
        "updated_at": config.updated_at.isoformat()
    }

@admin_router.post("/config")
def set_config(
    config_data: ConfigCreate,
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Imposta o aggiorna una configurazione"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        config = admin_manager.set_config(
            db=db,
            key=config_data.config_key,
            value=config_data.config_value,
            description=config_data.description,
            config_type=config_data.config_type,
            user_id=user_id_int
        )

        return {
            "id": config.id,
            "config_key": config.config_key,
            "config_value": config.config_value,
            "description": config.description,
            "config_type": config.config_type,
            "message": "Configurazione salvata con successo"
        }

    except Exception as e:
        logger.error(f"Errore set config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/test-password-save")
def test_password_save(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Endpoint di test per salvare direttamente la password SMTP"""
    try:
        test_password = "test_password_direct_123"

        logger.info(f"🧪 TEST: Tentativo salvataggio password SMTP diretto")
        logger.info(f"🧪 TEST: Password da salvare: {test_password}")

        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore

        # Prova salvataggio diretto
        config = admin_manager.set_config(
            db=db,
            key="email_smtp_password",
            value=test_password,
            description="Password SMTP per invio email (test diretto)",
            config_type="string",
            user_id=user_id_int
        )

        logger.info(f"🧪 TEST: Configurazione salvata con ID: {config.id}")

        # Verifica immediata
        saved_config = admin_manager.get_config(db, "email_smtp_password")

        if saved_config:
            logger.info(f"🧪 TEST: Verifica OK - Password trovata nel database")
            config_value = getattr(saved_config, 'config_value', '') or ""
            logger.info(f"🧪 TEST: ID: {saved_config.id}, Valore: {'***PRESENTE***' if config_value else 'VUOTO'}")

            return {
                "success": True,
                "message": "Password salvata e verificata con successo",
                "config_id": config.id,
                "verification": {
                    "found": True,
                    "id": saved_config.id,
                    "has_value": bool(config_value),
                    "value_length": len(config_value)
                }
            }
        else:
            logger.error(f"🧪 TEST: ERRORE - Password non trovata dopo salvataggio!")
            return {
                "success": False,
                "message": "Password salvata ma non trovata nella verifica",
                "config_id": config.id,
                "verification": {
                    "found": False
                }
            }

    except Exception as e:
        logger.error(f"🧪 TEST: ERRORE durante test: {str(e)}")
        logger.error(f"🧪 TEST: Tipo errore: {type(e)}")
        return {
            "success": False,
            "message": f"Errore durante test: {str(e)}",
            "error_type": str(type(e))
        }

# ===== ENDPOINT AUDIT LOG =====

@admin_router.get("/audit")
def get_audit_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    user_id: Optional[int] = Query(None),
    action: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene i log di audit con filtri"""
    try:
        logs = admin_manager.get_audit_logs(
            db=db,
            skip=skip,
            limit=limit,
            user_id=user_id,
            action=action,
            start_date=start_date,
            end_date=end_date
        )
        
        return [
            {
                "id": log.id,
                "user_id": log.user_id,
                "user_email": log.user.email if log.user else None,
                "action": log.action,
                "table_name": log.table_name,
                "record_id": log.record_id,
                "old_values": log.old_values,
                "new_values": log.new_values,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "timestamp": log.timestamp.isoformat()
            }
            for log in logs
        ]
        
    except Exception as e:
        logger.error(f"Errore get audit logs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT STATISTICHE =====

@admin_router.get("/stats/dashboard")
def get_dashboard_stats(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene statistiche per la dashboard admin"""
    try:
        return admin_manager.get_dashboard_summary(db)
        
    except Exception as e:
        logger.error(f"Errore dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/stats/daily")
def get_daily_stats(
    stat_date: Optional[date] = Query(None),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene statistiche giornaliere"""
    try:
        if not stat_date:
            stat_date = date.today()
        
        stats = admin_manager.get_system_stats(db, stat_date)
        if not stats:
            # Genera statistiche se non esistono
            stats = admin_manager.update_daily_stats(db, stat_date)
        
        return {
            "stat_date": stats.stat_date.isoformat(),
            "total_users": stats.total_users,
            "active_users": stats.active_users,
            "total_viaggi": stats.total_viaggi,
            "sof_generated": stats.sof_generated,
            "login_count": stats.login_count,
            "error_count": stats.error_count,
            "created_at": stats.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Errore daily stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/stats/update")
def update_stats(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Aggiorna le statistiche giornaliere"""
    try:
        stats = admin_manager.update_daily_stats(db)
        
        return {
            "message": "Statistiche aggiornate con successo",
            "stat_date": stats.stat_date.isoformat(),
            "total_users": stats.total_users,
            "active_users": stats.active_users,
            "total_viaggi": stats.total_viaggi
        }
        
    except Exception as e:
        logger.error(f"Errore update stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT GESTIONE SESSIONI =====

@admin_router.get("/sessions")
def get_active_sessions(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene tutte le sessioni attive"""
    try:
        sessions = admin_manager.get_active_sessions(db)

        return [
            {
                "id": session.id,
                "user_id": session.user_id,
                "user_email": session.user.email if session.user else None,
                "user_name": f"{session.user.Nome} {session.user.Cognome}" if session.user else None,
                "session_token": getattr(session, 'session_token', '')[:20] + "..." if getattr(session, 'session_token', None) else None,
                "ip_address": session.ip_address,
                "user_agent": session.user_agent,
                "is_active": session.is_active,
                "last_activity": (lambda dt: dt.isoformat() if dt else None)(getattr(session, 'last_activity', None)),
                "created_at": (lambda dt: dt.isoformat() if dt else None)(getattr(session, 'created_at', None)),
                "expires_at": (lambda dt: dt.isoformat() if dt else None)(getattr(session, 'expires_at', None))
            }
            for session in sessions
        ]

    except Exception as e:
        logger.error(f"Errore get sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/sessions/{session_id}/invalidate")
def invalidate_session(
    session_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Invalida una sessione specifica"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        success = admin_manager.invalidate_session(db, session_id, user_id_int)

        if success:
            return {"message": f"Sessione {session_id} invalidata con successo"}
        else:
            raise HTTPException(status_code=404, detail="Sessione non trovata")

    except Exception as e:
        logger.error(f"Errore invalidate session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.post("/users/{user_id}/invalidate-sessions")
def invalidate_user_sessions(
    user_id: int,
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Invalida tutte le sessioni di un utente"""
    try:
        # Type hint per il type checker
        user_id_int: int = current_user.id_user  # type: ignore
        count = admin_manager.invalidate_user_sessions(db, user_id, user_id_int)

        return {
            "message": f"{count} sessioni invalidate per utente {user_id}",
            "sessions_invalidated": count
        }

    except Exception as e:
        logger.error(f"Errore invalidate user sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT UTILITÀ =====

@admin_router.get("/cleanup")
def cleanup_page(
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN))
):
    """Pagina cleanup sistema"""
    return {
        "message": "Pagina cleanup sistema",
        "description": "Utilizza POST /admin/cleanup?days_to_keep=90 per eseguire la pulizia",
        "available_actions": [
            "Pulizia log audit vecchi",
            "Rimozione sessioni scadute",
            "Cleanup dati temporanei"
        ]
    }

@admin_router.post("/cleanup")
def cleanup_old_data(
    days_to_keep: int = Query(90, ge=1, le=365),
    current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN)),
    db: Session = Depends(get_db)
):
    """Pulisce dati vecchi dal sistema"""
    try:
        result = admin_manager.cleanup_old_data(db, days_to_keep)

        return {
            "message": "Cleanup completato con successo",
            "days_kept": days_to_keep,
            "results": result
        }

    except Exception as e:
        logger.error(f"Errore cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/system/health")
def system_health(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Controlla lo stato di salute del sistema"""
    try:
        # Test connessione database
        db.execute(text("SELECT 1"))
        db_status = "OK"

        # Conta sessioni attive
        active_sessions = len(admin_manager.get_active_sessions(db))

        # Statistiche base
        summary = admin_manager.get_dashboard_summary(db)

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": db_status,
            "active_sessions": active_sessions,
            "system_info": {
                "total_users": summary["totals"]["users"],
                "total_viaggi": summary["totals"]["viaggi"],
                "recent_activity": summary["activity"]["recent_actions"]
            }
        }

    except Exception as e:
        logger.error(f"Errore system health: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== ENDPOINT BACKUP =====

@admin_router.post("/backup/manual")
def create_manual_backup(
    format_type: str = Query("custom", regex="^(sql|custom)$"),
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Crea backup manuale del database"""
    try:
        from backup_manager import BackupManager
        from config import settings

        # Inizializza backup manager
        db_url = settings.DATABASE_URL
        backup_manager = BackupManager(db_url)

        logger.info(f"[BACKUP MANUALE] Richiesto da utente {current_user.Nome} {current_user.Cognome} (ID: {current_user.id_user})")

        # Crea backup
        backup_path = backup_manager.create_backup(format_type=format_type)

        if backup_path:
            # Log audit
            user_id_int: int = current_user.id_user  # type: ignore
            admin_manager.log_action(
                db=db,
                user_id=user_id_int,
                action="BACKUP_MANUAL",
                table_name="SYSTEM",
                new_values=f"Backup manuale creato: {backup_path}",
                ip_address="127.0.0.1"
            )

            from pathlib import Path
            backup_file = Path(backup_path)
            file_size = backup_file.stat().st_size if backup_file.exists() else 0

            return {
                "success": True,
                "message": "Backup creato con successo",
                "backup_path": str(backup_path),
                "backup_file": backup_file.name,
                "file_size": file_size,
                "format": format_type,
                "created_by": f"{current_user.Nome} {current_user.Cognome}",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Errore durante la creazione del backup")

    except Exception as e:
        logger.error(f"Errore backup manuale: {str(e)}")

        # Log errore
        try:
            user_id_int: int = current_user.id_user  # type: ignore
            admin_manager.log_action(
                db=db,
                user_id=user_id_int,
                action="BACKUP_MANUAL_ERROR",
                table_name="SYSTEM",
                new_values=f"Errore backup manuale: {str(e)}",
                ip_address="127.0.0.1"
            )
        except:
            pass

        raise HTTPException(status_code=500, detail=f"Errore durante backup: {str(e)}")

@admin_router.get("/backup/status")
def get_backup_status(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN)),
    db: Session = Depends(get_db)
):
    """Ottiene stato del sistema di backup"""
    try:
        from backup_manager import BackupManager
        from config import settings
        from pathlib import Path

        # Inizializza backup manager
        db_url = settings.DATABASE_URL
        backup_manager = BackupManager(db_url)

        # Ottieni configurazioni backup
        config = backup_manager.get_backup_config()

        # Lista backup esistenti
        backup_dir = Path("backups")
        backup_files = []

        if backup_dir.exists():
            for backup_file in backup_dir.glob("snip_backup_*"):
                file_stat = backup_file.stat()
                backup_files.append({
                    "name": backup_file.name,
                    "size": file_stat.st_size,
                    "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "format": "custom" if ".dump" in backup_file.name else "sql"
                })

        # Ordina per data (più recenti prima)
        backup_files.sort(key=lambda x: x["created"], reverse=True)

        return {
            "backup_config": config,
            "recent_backups": backup_files[:10],  # Ultimi 10 backup
            "backup_directory": str(backup_dir.absolute()),
            "total_backups": len(backup_files)
        }

    except Exception as e:
        logger.error(f"Errore backup status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@admin_router.get("/system/info")
def system_info(
    current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN))
):
    """Ottiene informazioni di sistema"""
    import platform
    import sys

    return {
        "system": {
            "platform": platform.platform(),
            "python_version": sys.version,
            "architecture": platform.architecture()[0]
        },
        "application": {
            "name": "SNIP - Sistema Navale Integrato Portuale",
            "version": "2.0.0",
            "environment": "development"  # TODO: da configurazione
        },
        "timestamp": datetime.now().isoformat()
    }
