@echo off
REM ===== SNIP - Gestione Servizio Windows =====
REM Script per gestire il servizio FastAPIService con sistema di sicurezza
REM Generato il 2025-07-23

echo.
echo ========================================
echo 🚢 SNIP - Gestione Servizio Windows
echo 🔐 Con Sistema di Sicurezza Integrato
echo ========================================
echo.

REM Controlla se lo script è eseguito come amministratore
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ❌ ERRORE: Questo script deve essere eseguito come Amministratore
    echo.
    echo 💡 Fai clic destro su questo file e seleziona "Esegui come amministratore"
    echo.
    pause
    exit /b 1
)

REM Controlla se esiste il virtual environment
if not exist "venv\" (
    echo ❌ Virtual environment non trovato!
    echo 💡 Esegui prima: python install.py
    echo.
    pause
    exit /b 1
)

REM Controlla se esistono i file necessari
if not exist "snip_service.py" (
    echo ❌ File snip_service.py non trovato!
    echo 💡 Assicurati che tutti i file siano presenti
    echo.
    pause
    exit /b 1
)

if not exist "security_manager.py" (
    echo ❌ File security_manager.py non trovato!
    echo 💡 Assicurati che tutti i file siano presenti
    echo.
    pause
    exit /b 1
)

REM Attiva virtual environment
echo 🔧 Attivazione virtual environment...
call venv\Scripts\activate.bat

REM Installa dipendenze per il servizio Windows se necessario
echo 🔧 Verifica dipendenze servizio Windows...
pip install pywin32 >nul 2>&1

REM Menu principale
:MENU
echo.
echo ==================== MENU SERVIZIO ====================
echo.
echo 1. 📦 Installa servizio FastAPIService
echo 2. 🚀 Avvia servizio
echo 3. 🛑 Ferma servizio
echo 4. 📊 Stato servizio
echo 5. 🗑️  Rimuovi servizio
echo 6. 🔐 Test sistema di sicurezza
echo 7. 🌐 Apri pagina di verifica sicurezza
echo 8. 🔄 Riavvia servizio
echo 9. 📝 Visualizza log servizio
echo 0. ❌ Esci
echo.
set /p choice="Seleziona un'opzione (0-9): "

if "%choice%"=="1" goto INSTALL
if "%choice%"=="2" goto START
if "%choice%"=="3" goto STOP
if "%choice%"=="4" goto STATUS
if "%choice%"=="5" goto REMOVE
if "%choice%"=="6" goto TEST_SECURITY
if "%choice%"=="7" goto OPEN_VERIFY
if "%choice%"=="8" goto RESTART
if "%choice%"=="9" goto VIEW_LOG
if "%choice%"=="0" goto EXIT

echo ❌ Opzione non valida!
goto MENU

:INSTALL
echo.
echo 📦 Installazione servizio FastAPIService...
python snip_service.py install
if %errorLevel% EQU 0 (
    echo ✅ Servizio installato con successo!
    echo 💡 Ora puoi avviarlo dal menu o dalle impostazioni di Windows
) else (
    echo ❌ Errore durante l'installazione
)
pause
goto MENU

:START
echo.
echo 🚀 Avvio servizio FastAPIService...
python snip_service.py start
if %errorLevel% EQU 0 (
    echo ✅ Servizio avviato con successo!
    echo 🌐 Applicazione disponibile su: http://localhost:8002
    echo 🔐 Verifica sicurezza: http://localhost:8002/security/verify
) else (
    echo ❌ Errore durante l'avvio
)
pause
goto MENU

:STOP
echo.
echo 🛑 Arresto servizio FastAPIService...
python snip_service.py stop
if %errorLevel% EQU 0 (
    echo ✅ Servizio fermato con successo!
) else (
    echo ❌ Errore durante l'arresto
)
pause
goto MENU

:STATUS
echo.
echo 📊 Controllo stato servizio...
sc query FastAPIService
echo.
echo 🌐 Test connessione applicazione...
curl -s -o nul -w "Status: %%{http_code}" http://localhost:8002/security/status 2>nul
if %errorLevel% EQU 0 (
    echo - Applicazione risponde ✅
) else (
    echo - Applicazione non risponde ❌
)
pause
goto MENU

:REMOVE
echo.
echo 🗑️ Rimozione servizio FastAPIService...
echo ⚠️ ATTENZIONE: Questa operazione rimuoverà completamente il servizio!
set /p confirm="Sei sicuro? (s/N): "
if /i "%confirm%"=="s" (
    python snip_service.py remove
    if %errorLevel% EQU 0 (
        echo ✅ Servizio rimosso con successo!
    ) else (
        echo ❌ Errore durante la rimozione
    )
) else (
    echo ❌ Operazione annullata
)
pause
goto MENU

:TEST_SECURITY
echo.
echo 🔐 Test sistema di sicurezza...
echo 📧 Invio codice di <NAME_EMAIL>...
curl -X POST -H "Content-Type: application/json" http://localhost:8002/security/initiate 2>nul
if %errorLevel% EQU 0 (
    echo ✅ Test completato - controlla l'email!
    echo 🌐 Vai su http://localhost:8002/security/verify per inserire il codice
) else (
    echo ❌ Errore durante il test - assicurati che il servizio sia in esecuzione
)
pause
goto MENU

:OPEN_VERIFY
echo.
echo 🌐 Apertura pagina di verifica sicurezza...
start http://localhost:8002/security/verify
pause
goto MENU

:RESTART
echo.
echo 🔄 Riavvio servizio FastAPIService...
echo 🛑 Arresto...
python snip_service.py stop >nul 2>&1
timeout /t 3 /nobreak >nul
echo 🚀 Avvio...
python snip_service.py start
if %errorLevel% EQU 0 (
    echo ✅ Servizio riavviato con successo!
    echo 🔐 Se configurato, il sistema di sicurezza invierà un nuovo codice
) else (
    echo ❌ Errore durante il riavvio
)
pause
goto MENU

:VIEW_LOG
echo.
echo 📝 Visualizzazione log servizio...
if exist "snip_service.log" (
    echo Ultimi 20 righe del log:
    echo ================================
    powershell "Get-Content snip_service.log -Tail 20"
    echo ================================
) else (
    echo ❌ File di log non trovato
)
pause
goto MENU

:EXIT
echo.
echo 👋 Arrivederci!
echo.
exit /b 0
