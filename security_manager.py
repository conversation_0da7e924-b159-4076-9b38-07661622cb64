#!/usr/bin/env python3
"""
Sistema di Sicurezza SNIP
Gestisce la generazione di codici di sicurezza per il riavvio del servizio
"""

import secrets
import string
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path
from sqlalchemy.orm import Session
from database import get_db, SessionLocal
import threading
import time

# Setup logging
logger = logging.getLogger(__name__)

class SecurityManager:
    """Gestisce la sicurezza del riavvio del servizio"""
    
    def __init__(self):
        self.security_file = Path("security_state.json")
        self.current_code: Optional[str] = None
        self.code_expiry: Optional[datetime] = None
        self.is_verified: bool = False
        self.startup_blocked: bool = False
        self.admin_email = "<EMAIL>"
        self.smtp_password = "sxfv xkjf afur vuhr"  # Password app Gmail fornita
        
    def generate_security_code(self) -> str:
        """Genera un codice di sicurezza di 6 cifre"""
        code = ''.join(secrets.choice(string.digits) for _ in range(6))
        self.current_code = code
        self.code_expiry = datetime.now() + timedelta(minutes=10)  # Scade in 10 minuti
        self.is_verified = False
        
        # Salva lo stato
        self._save_security_state()
        
        logger.info(f"🔐 Codice di sicurezza generato: {code} (scade alle {self.code_expiry.strftime('%H:%M:%S')})")
        return code
    
    def verify_code(self, input_code: str) -> bool:
        """Verifica se il codice inserito è corretto e non scaduto"""
        if not self.current_code or not self.code_expiry:
            logger.warning("🚫 Nessun codice attivo da verificare")
            return False
            
        if datetime.now() > self.code_expiry:
            logger.warning("⏰ Codice di sicurezza scaduto")
            self._clear_security_state()
            return False
            
        if input_code == self.current_code:
            logger.info("✅ Codice di sicurezza verificato con successo")
            self.is_verified = True
            self.startup_blocked = False
            self._save_security_state()
            return True
        else:
            logger.warning(f"❌ Codice errato: {input_code} (atteso: {self.current_code})")
            return False
    
    def send_security_email(self) -> bool:
        """Invia il codice di sicurezza via email"""
        if not self.current_code:
            logger.error("❌ Nessun codice da inviare")
            return False
            
        try:
            # Importa la funzione send_email dal main
            import sys
            sys.path.append('.')
            from main import send_email
            
            subject = "🔐 Codice di Sicurezza SNIP - Riavvio Servizio"
            
            body = f"""
Gentile Ettore,

Il servizio SNIP è stato riavviato e richiede la tua autorizzazione per continuare.

🔐 CODICE DI SICUREZZA: {self.current_code}

⏰ Il codice scadrà alle: {self.code_expiry.strftime('%H:%M:%S')} del {self.code_expiry.strftime('%d/%m/%Y')}

Per autorizzare il riavvio del servizio:
1. Accedi al sistema SNIP
2. Vai alla pagina di verifica sicurezza
3. Inserisci il codice: {self.current_code}

🚨 IMPORTANTE:
- Questo codice è valido solo per 10 minuti
- Il servizio rimarrà bloccato fino alla verifica
- Se non hai richiesto questo riavvio, contatta immediatamente l'amministratore di sistema

---
Sistema di Sicurezza SNIP
Michele Autuori Srl - shipping and forwarding agency
Generato il {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}
            """
            
            # Usa una sessione database temporanea per l'invio email
            with SessionLocal() as db:
                success = send_email(
                    to_email=self.admin_email,
                    subject=subject,
                    body=body,
                    db=db
                )
                
                if success:
                    logger.info(f"📧 Codice di sicurezza inviato a {self.admin_email}")
                    return True
                else:
                    logger.error("❌ Errore invio email codice di sicurezza")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Errore invio email sicurezza: {e}")
            return False
    
    def block_startup(self) -> None:
        """Blocca l'avvio del servizio fino alla verifica"""
        self.startup_blocked = True
        self._save_security_state()
        logger.warning("🚫 Avvio servizio bloccato - in attesa di verifica sicurezza")
    
    def is_startup_allowed(self) -> bool:
        """Controlla se l'avvio del servizio è autorizzato"""
        # Carica lo stato corrente
        self._load_security_state()
        
        # Se non c'è blocco attivo, permetti l'avvio
        if not self.startup_blocked:
            return True
            
        # Se c'è un blocco ma è verificato, permetti l'avvio
        if self.is_verified:
            return True
            
        # Se il codice è scaduto, sblocca automaticamente
        if self.code_expiry and datetime.now() > self.code_expiry:
            logger.warning("⏰ Codice scaduto - sblocco automatico del servizio")
            self._clear_security_state()
            return True
            
        return False
    
    def initiate_security_check(self) -> bool:
        """Avvia il processo di verifica sicurezza"""
        logger.info("🔐 Avvio processo di verifica sicurezza...")
        
        # Genera nuovo codice
        code = self.generate_security_code()
        
        # Blocca l'avvio
        self.block_startup()
        
        # Invia email
        email_sent = self.send_security_email()
        
        if email_sent:
            logger.info("✅ Processo di sicurezza avviato con successo")
            return True
        else:
            logger.error("❌ Errore nell'avvio del processo di sicurezza")
            # In caso di errore email, sblocca il servizio per evitare blocchi permanenti
            self._clear_security_state()
            return False
    
    def _save_security_state(self) -> None:
        """Salva lo stato di sicurezza su file"""
        state = {
            "current_code": self.current_code,
            "code_expiry": self.code_expiry.isoformat() if self.code_expiry else None,
            "is_verified": self.is_verified,
            "startup_blocked": self.startup_blocked,
            "last_update": datetime.now().isoformat()
        }
        
        try:
            with open(self.security_file, 'w') as f:
                json.dump(state, f, indent=2)
            logger.debug("💾 Stato di sicurezza salvato")
        except Exception as e:
            logger.error(f"❌ Errore salvataggio stato sicurezza: {e}")
    
    def _load_security_state(self) -> None:
        """Carica lo stato di sicurezza da file"""
        if not self.security_file.exists():
            return
            
        try:
            with open(self.security_file, 'r') as f:
                state = json.load(f)
                
            self.current_code = state.get("current_code")
            self.is_verified = state.get("is_verified", False)
            self.startup_blocked = state.get("startup_blocked", False)
            
            expiry_str = state.get("code_expiry")
            if expiry_str:
                self.code_expiry = datetime.fromisoformat(expiry_str)
                
            logger.debug("📂 Stato di sicurezza caricato")
        except Exception as e:
            logger.error(f"❌ Errore caricamento stato sicurezza: {e}")
            self._clear_security_state()
    
    def _clear_security_state(self) -> None:
        """Pulisce lo stato di sicurezza"""
        self.current_code = None
        self.code_expiry = None
        self.is_verified = False
        self.startup_blocked = False
        
        # Rimuovi il file di stato
        if self.security_file.exists():
            try:
                self.security_file.unlink()
                logger.debug("🗑️ File stato sicurezza rimosso")
            except Exception as e:
                logger.error(f"❌ Errore rimozione file stato: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Ottiene lo stato corrente del sistema di sicurezza"""
        self._load_security_state()
        
        return {
            "has_active_code": bool(self.current_code),
            "code_expiry": self.code_expiry.isoformat() if self.code_expiry else None,
            "is_verified": self.is_verified,
            "startup_blocked": self.startup_blocked,
            "time_remaining": (self.code_expiry - datetime.now()).total_seconds() if self.code_expiry else 0
        }

# Istanza globale del security manager
security_manager = SecurityManager()

def check_startup_security() -> bool:
    """Funzione helper per controllare la sicurezza all'avvio"""
    return security_manager.is_startup_allowed()

def initiate_security_on_restart() -> bool:
    """Funzione helper per avviare la sicurezza al riavvio"""
    return security_manager.initiate_security_check()
