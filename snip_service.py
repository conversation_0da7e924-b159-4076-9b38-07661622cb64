#!/usr/bin/env python3
"""
Servizio Windows per SNIP
Gestisce l'avvio automatico con sistema di sicurezza integrato
"""

import sys
import os
import time
import logging
import subprocess
from pathlib import Path
import win32serviceutil
import win32service
import win32event
import servicemanager

# Configurazione logging per il servizio
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('snip_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('SNIPService')

class SNIPService(win32serviceutil.ServiceFramework):
    """Servizio Windows per SNIP con sistema di sicurezza"""
    
    _svc_name_ = "FastAPIService"
    _svc_display_name_ = "SNIP - Sistema Navale Integrato Portuale"
    _svc_description_ = "Servizio per l'applicazione SNIP con sistema di sicurezza integrato"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_running = True
        self.process = None
        
        # Directory del servizio (dove si trova questo script)
        self.service_dir = Path(__file__).parent.absolute()
        logger.info(f"Directory servizio: {self.service_dir}")
        
    def SvcStop(self):
        """Ferma il servizio"""
        logger.info("🛑 Richiesta di stop del servizio SNIP")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        
        # Termina il processo dell'applicazione se in esecuzione
        if self.process and self.process.poll() is None:
            logger.info("🔄 Terminazione processo applicazione...")
            self.process.terminate()
            try:
                self.process.wait(timeout=10)
                logger.info("✅ Processo terminato correttamente")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Timeout terminazione - forzando kill")
                self.process.kill()
                
    def SvcDoRun(self):
        """Esegue il servizio"""
        logger.info("🚀 Avvio servizio SNIP")
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        # Cambia directory di lavoro
        os.chdir(self.service_dir)
        logger.info(f"📂 Directory di lavoro: {os.getcwd()}")
        
        while self.is_running:
            try:
                # Controlla il sistema di sicurezza
                if self._check_security():
                    logger.info("✅ Controllo sicurezza superato - avvio applicazione")
                    self._start_application()
                else:
                    logger.warning("🚫 Avvio bloccato dal sistema di sicurezza")
                    self._initiate_security_process()
                    
                # Attendi che il processo termini o che il servizio venga fermato
                self._wait_for_process_or_stop()
                
                if self.is_running:
                    logger.info("🔄 Processo terminato - riavvio in 5 secondi...")
                    time.sleep(5)
                    
            except Exception as e:
                logger.error(f"❌ Errore nel servizio: {e}")
                if self.is_running:
                    logger.info("🔄 Riavvio in 30 secondi dopo errore...")
                    time.sleep(30)
        
        logger.info("🛑 Servizio SNIP fermato")
        
    def _check_security(self):
        """Controlla il sistema di sicurezza"""
        try:
            # Aggiungi la directory corrente al path Python
            if str(self.service_dir) not in sys.path:
                sys.path.insert(0, str(self.service_dir))
                
            from security_manager import security_manager
            return security_manager.is_startup_allowed()
            
        except Exception as e:
            logger.error(f"❌ Errore controllo sicurezza: {e}")
            # In caso di errore, permetti l'avvio
            return True
            
    def _initiate_security_process(self):
        """Avvia il processo di sicurezza"""
        try:
            from security_manager import security_manager
            
            logger.info("🔐 Avvio processo di sicurezza...")
            success = security_manager.initiate_security_check()
            
            if success:
                logger.info("📧 Codice di sicurezza <NAME_EMAIL>")
                logger.info("🌐 Accedi a http://localhost:8002/security/verify per inserire il codice")
                
                # Avvia comunque l'applicazione per permettere l'accesso alla pagina di verifica
                self._start_application()
            else:
                logger.error("❌ Errore nel processo di sicurezza - avvio normale")
                
        except Exception as e:
            logger.error(f"❌ Errore avvio processo sicurezza: {e}")
            
    def _start_application(self):
        """Avvia l'applicazione SNIP"""
        try:
            # Percorso del virtual environment
            venv_python = self.service_dir / "venv" / "Scripts" / "python.exe"
            
            if not venv_python.exists():
                logger.error(f"❌ Python virtual environment non trovato: {venv_python}")
                return False
                
            # Comando per avviare l'applicazione
            cmd = [
                str(venv_python),
                "-m", "uvicorn",
                "main:app",
                "--host", "0.0.0.0",
                "--port", "8002",
                "--log-level", "info"
            ]
            
            logger.info(f"🔄 Avvio comando: {' '.join(cmd)}")
            
            # Avvia il processo
            self.process = subprocess.Popen(
                cmd,
                cwd=self.service_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            logger.info(f"✅ Applicazione avviata con PID: {self.process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore avvio applicazione: {e}")
            return False
            
    def _wait_for_process_or_stop(self):
        """Attende che il processo termini o che il servizio venga fermato"""
        while self.is_running and self.process and self.process.poll() is None:
            # Attendi 1 secondo o fino al segnale di stop
            result = win32event.WaitForSingleObject(self.hWaitStop, 1000)
            if result == win32event.WAIT_OBJECT_0:
                # Segnale di stop ricevuto
                break
                
        # Se il processo è ancora in esecuzione, terminalo
        if self.process and self.process.poll() is None:
            logger.info("🔄 Terminazione processo applicazione...")
            self.process.terminate()

def install_service():
    """Installa il servizio Windows"""
    try:
        win32serviceutil.InstallService(
            SNIPService,
            SNIPService._svc_name_,
            SNIPService._svc_display_name_,
            description=SNIPService._svc_description_
        )
        print(f"✅ Servizio '{SNIPService._svc_display_name_}' installato con successo")
        return True
    except Exception as e:
        print(f"❌ Errore installazione servizio: {e}")
        return False

def remove_service():
    """Rimuove il servizio Windows"""
    try:
        win32serviceutil.RemoveService(SNIPService._svc_name_)
        print(f"✅ Servizio '{SNIPService._svc_display_name_}' rimosso con successo")
        return True
    except Exception as e:
        print(f"❌ Errore rimozione servizio: {e}")
        return False

def start_service():
    """Avvia il servizio Windows"""
    try:
        win32serviceutil.StartService(SNIPService._svc_name_)
        print(f"✅ Servizio '{SNIPService._svc_display_name_}' avviato con successo")
        return True
    except Exception as e:
        print(f"❌ Errore avvio servizio: {e}")
        return False

def stop_service():
    """Ferma il servizio Windows"""
    try:
        win32serviceutil.StopService(SNIPService._svc_name_)
        print(f"✅ Servizio '{SNIPService._svc_display_name_}' fermato con successo")
        return True
    except Exception as e:
        print(f"❌ Errore stop servizio: {e}")
        return False

if __name__ == '__main__':
    if len(sys.argv) == 1:
        # Avvia il servizio in modalità debug
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(SNIPService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # Gestisci i comandi da linea di comando
        command = sys.argv[1].lower()
        
        if command == 'install':
            install_service()
        elif command == 'remove':
            remove_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'debug':
            # Modalità debug - esegui come applicazione normale
            service = SNIPService([])
            service.SvcDoRun()
        else:
            print("Comandi disponibili:")
            print("  install - Installa il servizio")
            print("  remove  - Rimuove il servizio")
            print("  start   - Avvia il servizio")
            print("  stop    - Ferma il servizio")
            print("  debug   - Esegui in modalità debug")
