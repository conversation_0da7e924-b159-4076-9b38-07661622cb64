@echo off
REM ===== SNIP - Sistema Navale Integrato Portuale =====
REM Script di avvio per Windows
REM Generato il 2025-06-17

echo.
echo ========================================
echo 🚢 SNIP - Sistema Navale Integrato Portuale
echo 🚀 Avvio Applicazione
echo ========================================
echo.

REM Controlla se esiste il virtual environment
if not exist "venv\" (
    echo ❌ Virtual environment non trovato!
    echo 💡 Esegui prima: python install.py
    echo.
    pause
    exit /b 1
)

REM Attiva virtual environment
echo 🔧 Attivazione virtual environment...
call venv\Scripts\activate.bat

REM Controlla se main.py esiste
if not exist "main.py" (
    echo ❌ File main.py non trovato!
    echo 💡 Assicurati di essere nella directory corretta
    echo.
    pause
    exit /b 1
)

REM Mostra informazioni di avvio
echo.
echo ✅ Virtual environment attivato
echo 📂 Directory: %CD%
echo 🌐 Server: http://localhost:8002
echo.
echo 🔄 Avvio server FastAPI...
echo ⏹️  Premi Ctrl+C per fermare il server
echo.

REM Avvia il server
uvicorn main:app --reload --host 0.0.0.0 --port 8002

REM Se il server si ferma, mostra messaggio
echo.
echo 🛑 Server fermato
echo.
pause
