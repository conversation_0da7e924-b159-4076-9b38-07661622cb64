#!/bin/bash
# ===== SNIP - Sistema Navale Integrato Portuale =====
# Script di avvio per Linux/Mac
# Generato il 2025-06-17

echo ""
echo "========================================"
echo "🚢 SNIP - Sistema Navale Integrato Portuale"
echo "🚀 Avvio Applicazione"
echo "========================================"
echo ""

# Controlla se esiste il virtual environment
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment non trovato!"
    echo "💡 Esegui prima: python3 install.py"
    echo ""
    exit 1
fi

# Attiva virtual environment
echo "🔧 Attivazione virtual environment..."
source venv/bin/activate

# Controlla se main.py esiste
if [ ! -f "main.py" ]; then
    echo "❌ File main.py non trovato!"
    echo "💡 Assicurati di essere nella directory corretta"
    echo ""
    exit 1
fi

# Mostra informazioni di avvio
echo ""
echo "✅ Virtual environment attivato"
echo "📂 Directory: $(pwd)"
echo "🌐 Server: http://localhost:8002"
echo ""
echo "🔄 Avvio server FastAPI..."
echo "⏹️  Premi Ctrl+C per fermare il server"
echo ""

# Avvia il server
uvicorn main:app --reload --host 0.0.0.0 --port 8002

# Se il server si ferma, mostra messaggio
echo ""
echo "🛑 Server fermato"
echo ""
