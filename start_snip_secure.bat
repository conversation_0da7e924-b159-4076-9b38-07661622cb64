@echo off
REM ===== SNIP - Sistema Navale Integrato Portuale =====
REM Script di avvio SICURO per Windows
REM Con sistema di verifica codice di sicurezza
REM Generato il 2025-07-23

echo.
echo ========================================
echo 🚢 SNIP - Sistema Navale Integrato Portuale
echo 🔐 Avvio Sicuro con Verifica Codice
echo ========================================
echo.

REM Controlla se esiste il virtual environment
if not exist "venv\" (
    echo ❌ Virtual environment non trovato!
    echo 💡 Esegui prima: python install.py
    echo.
    pause
    exit /b 1
)

REM Attiva virtual environment
echo 🔧 Attivazione virtual environment...
call venv\Scripts\activate.bat

REM Controlla se main.py esiste
if not exist "main.py" (
    echo ❌ File main.py non trovato!
    echo 💡 Assicurati di essere nella directory corretta
    echo.
    pause
    exit /b 1
)

REM Controlla se security_manager.py esiste
if not exist "security_manager.py" (
    echo ❌ Modulo di sicurezza non trovato!
    echo 💡 Assicurati che security_manager.py sia presente
    echo.
    pause
    exit /b 1
)

echo.
echo 🔐 Controllo sistema di sicurezza...

REM Esegui il controllo di sicurezza
python -c "
import sys
sys.path.append('.')
from security_manager import security_manager, initiate_security_on_restart

print('🔍 Verifica stato sicurezza...')

# Controlla se l'avvio è permesso
if security_manager.is_startup_allowed():
    print('✅ Avvio autorizzato - procedendo...')
    sys.exit(0)
else:
    print('🚫 Avvio bloccato - avvio processo di sicurezza...')
    
    # Avvia il processo di sicurezza
    success = initiate_security_on_restart()
    
    if success:
        print('📧 Codice di sicurezza <NAME_EMAIL>')
        print('🔐 Accedi a http://localhost:8002/security/verify per inserire il codice')
        print('⏰ Il codice scadrà in 10 minuti')
        print('')
        print('🚫 SERVIZIO BLOCCATO - In attesa di verifica sicurezza')
        print('   Per sbloccare il servizio:')
        print('   1. Controlla la tua email')
        print('   2. Vai su http://localhost:8002/security/verify')
        print('   3. Inserisci il codice di 6 cifre ricevuto')
        print('')
        sys.exit(1)
    else:
        print('❌ Errore nel sistema di sicurezza - avvio normale')
        sys.exit(0)
"

REM Controlla il risultato del controllo sicurezza
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 🚫 AVVIO BLOCCATO DAL SISTEMA DI SICUREZZA
    echo.
    echo 📧 Un codice di sicurezza è stato <NAME_EMAIL>
    echo 🌐 Vai su http://localhost:8002/security/verify per inserire il codice
    echo ⏰ Il codice scadrà in 10 minuti
    echo.
    echo 💡 Dopo aver inserito il codice corretto, riavvia questo script
    echo.
    pause
    exit /b 1
)

REM Se arriviamo qui, l'avvio è autorizzato
echo ✅ Sicurezza verificata - avvio del server...
echo.

REM Mostra informazioni di avvio
echo ✅ Virtual environment attivato
echo 📂 Directory: %CD%
echo 🌐 Server: http://localhost:8002
echo 🔐 Verifica sicurezza: http://localhost:8002/security/verify
echo.
echo 🔄 Avvio server FastAPI...
echo ⏹️  Premi Ctrl+C per fermare il server
echo.

REM Avvia il server
uvicorn main:app --reload --host 0.0.0.0 --port 8002

REM Se il server si ferma, mostra messaggio
echo.
echo 🛑 Server fermato
echo.
pause
