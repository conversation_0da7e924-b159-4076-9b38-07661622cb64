#!/bin/bash
# ===== SNIP - Sistema Navale Integrato Portuale =====
# Script di avvio SICURO per Linux/Mac
# Con sistema di verifica codice di sicurezza
# Generato il 2025-07-23

echo ""
echo "========================================"
echo "🚢 SNIP - Sistema Navale Integrato Portuale"
echo "🔐 Avvio Sicuro con Verifica Codice"
echo "========================================"
echo ""

# Controlla se esiste il virtual environment
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment non trovato!"
    echo "💡 Esegui prima: python3 install.py"
    echo ""
    exit 1
fi

# Attiva virtual environment
echo "🔧 Attivazione virtual environment..."
source venv/bin/activate

# Controlla se main.py esiste
if [ ! -f "main.py" ]; then
    echo "❌ File main.py non trovato!"
    echo "💡 Assicurati di essere nella directory corretta"
    echo ""
    exit 1
fi

# Controlla se security_manager.py esiste
if [ ! -f "security_manager.py" ]; then
    echo "❌ Modulo di sicurezza non trovato!"
    echo "💡 Assicurati che security_manager.py sia presente"
    echo ""
    exit 1
fi

echo ""
echo "🔐 Controllo sistema di sicurezza..."

# Esegui il controllo di sicurezza
python3 -c "
import sys
sys.path.append('.')
from security_manager import security_manager, initiate_security_on_restart

print('🔍 Verifica stato sicurezza...')

# Controlla se l'avvio è permesso
if security_manager.is_startup_allowed():
    print('✅ Avvio autorizzato - procedendo...')
    sys.exit(0)
else:
    print('🚫 Avvio bloccato - avvio processo di sicurezza...')
    
    # Avvia il processo di sicurezza
    success = initiate_security_on_restart()
    
    if success:
        print('📧 Codice di sicurezza <NAME_EMAIL>')
        print('🔐 Accedi a http://localhost:8002/security/verify per inserire il codice')
        print('⏰ Il codice scadrà in 10 minuti')
        print('')
        print('🚫 SERVIZIO BLOCCATO - In attesa di verifica sicurezza')
        print('   Per sbloccare il servizio:')
        print('   1. Controlla la tua email')
        print('   2. Vai su http://localhost:8002/security/verify')
        print('   3. Inserisci il codice di 6 cifre ricevuto')
        print('')
        sys.exit(1)
    else:
        print('❌ Errore nel sistema di sicurezza - avvio normale')
        sys.exit(0)
"

# Controlla il risultato del controllo sicurezza
if [ $? -ne 0 ]; then
    echo ""
    echo "🚫 AVVIO BLOCCATO DAL SISTEMA DI SICUREZZA"
    echo ""
    echo "📧 Un codice di sicurezza è stato <NAME_EMAIL>"
    echo "🌐 Vai su http://localhost:8002/security/verify per inserire il codice"
    echo "⏰ Il codice scadrà in 10 minuti"
    echo ""
    echo "💡 Dopo aver inserito il codice corretto, riavvia questo script"
    echo ""
    read -p "Premi Enter per continuare..."
    exit 1
fi

# Se arriviamo qui, l'avvio è autorizzato
echo "✅ Sicurezza verificata - avvio del server..."
echo ""

# Mostra informazioni di avvio
echo "✅ Virtual environment attivato"
echo "📂 Directory: $(pwd)"
echo "🌐 Server: http://localhost:8002"
echo "🔐 Verifica sicurezza: http://localhost:8002/security/verify"
echo ""
echo "🔄 Avvio server FastAPI..."
echo "⏹️  Premi Ctrl+C per fermare il server"
echo ""

# Avvia il server
uvicorn main:app --reload --host 0.0.0.0 --port 8002

# Se il server si ferma, mostra messaggio
echo ""
echo "🛑 Server fermato"
echo ""
