/* ===== FIX CONTRASTO ICONE SNIP ===== */

/* ===== PROBLEMI GENERALI ===== */

/* Fix icone bianche su sfondo bianco */
.text-white i,
.bg-white i,
.card-body i.text-white,
.dropdown-item i.text-white {
    color: #2c3e50 !important; /* Grigio scuro invece di bianco */
}

/* Fix icone con colori troppo chiari */
i.text-light,
.text-light i {
    color: #6c757d !important; /* Grigio medio invece di grigio chiaro */
}

/* ===== FIX SPECIFICI DROPDOWN ===== */

/* Assicura che le icone nei dropdown abbiano sempre contrasto */
.dropdown-menu .dropdown-item i {
    color: #5a67d8 !important; /* Colore di default con buon contrasto */
}

/* Fix icone specifiche per colore */
.dropdown-menu .dropdown-item[href*="viaggi"] i {
    color: #2980b9 !important; /* Blu scuro */
}

.dropdown-menu .dropdown-item[href*="navi"] i {
    color: #27ae60 !important; /* Verde scuro */
}

.dropdown-menu .dropdown-item[href*="armatori"] i {
    color: #8e44ad !important; /* Viola scuro */
}

.dropdown-menu .dropdown-item[href*="porti"] i {
    color: #d35400 !important; /* Arancione scuro */
}

.dropdown-menu .dropdown-item[href*="sof"] i {
    color: #f39c12 !important; /* Giallo scuro */
}

.dropdown-menu .dropdown-item[href*="realizzati"] i {
    color: #27ae60 !important; /* Verde scuro */
}

.dropdown-menu .dropdown-item[href*="amministrazione"] i {
    color: #c0392b !important; /* Rosso scuro */
}

/* ===== FIX NAVBAR ===== */

/* Fix icone navbar che potrebbero essere troppo chiare */
.navbar .nav-link i {
    color: rgba(255, 255, 255, 0.9) !important; /* Bianco quasi opaco su navbar scura */
}

.navbar-brand i {
    color: white !important; /* Bianco su navbar scura */
}

/* ===== FIX DASHBOARD ===== */

/* Fix icone nelle card statistiche */
.stat-card .stat-icon {
    /* I colori sono già OK nel CSS custom */
}

/* Fix icone nei badge */
.badge i {
    color: inherit !important; /* Eredita il colore del badge */
}

/* Fix icone nei pulsanti */
.btn i {
    color: inherit !important; /* Eredita il colore del pulsante */
}

/* ===== FIX MODALI ===== */

/* Fix icone nei modal header */
.modal-header i {
    color: inherit !important; /* Eredita il colore del header */
}

/* Fix icone nei modal body su sfondo bianco */
.modal-body i {
    color: #495057 !important; /* Grigio scuro per buon contrasto */
}

/* ===== FIX FORM ===== */

/* Fix icone nelle label dei form */
.form-label i {
    color: #495057 !important; /* Grigio scuro */
}

/* Fix icone negli input group */
.input-group-text i {
    color: #6c757d !important; /* Grigio medio */
}

/* ===== FIX TABELLE ===== */

/* Fix icone nelle tabelle */
.table i {
    color: #495057 !important; /* Grigio scuro */
}

/* Fix icone nei thead */
.table thead th i {
    color: white !important; /* Bianco su sfondo scuro del thead */
}

/* ===== FIX ALERT ===== */

/* Fix icone negli alert */
.alert i {
    color: inherit !important; /* Eredita il colore dell'alert */
}

.alert-info i {
    color: #0c5460 !important; /* Blu scuro per alert info */
}

.alert-success i {
    color: #0f5132 !important; /* Verde scuro per alert success */
}

.alert-warning i {
    color: #664d03 !important; /* Giallo scuro per alert warning */
}

.alert-danger i {
    color: #842029 !important; /* Rosso scuro per alert danger */
}

/* ===== FIX BREADCRUMB ===== */

/* Fix icone nei breadcrumb */
.breadcrumb i {
    color: #6c757d !important; /* Grigio medio */
}

/* ===== FIX SIDEBAR ===== */

/* Fix icone nella sidebar se presente */
.sidebar i,
.offcanvas i {
    color: #495057 !important; /* Grigio scuro */
}

/* ===== FIX CARD ===== */

/* Fix icone nei card header */
.card-header i {
    color: inherit !important; /* Eredita il colore del header */
}

/* Fix icone nei card body su sfondo bianco */
.card-body i {
    color: #495057 !important; /* Grigio scuro di default */
}

/* Eccezioni per card con sfondo colorato */
.card-header.bg-primary i,
.card-header.bg-info i,
.card-header.bg-success i,
.card-header.bg-warning i,
.card-header.bg-danger i,
.card-header.bg-dark i {
    color: white !important; /* Bianco su sfondi colorati */
}

/* ===== FIX PAGINATION ===== */

/* Fix icone nella paginazione */
.pagination i {
    color: #495057 !important; /* Grigio scuro */
}

/* ===== FIX TOAST ===== */

/* Fix icone nei toast */
.toast i {
    color: #495057 !important; /* Grigio scuro */
}

/* ===== FIX LIST GROUP ===== */

/* Fix icone nei list group */
.list-group-item i {
    color: #495057 !important; /* Grigio scuro */
}

/* ===== FIX SPECIFICI SNIP ===== */

/* Fix icone specifiche dell'app SNIP */
.snip-navbar i {
    color: white !important; /* Bianco su navbar SNIP */
}

.porto-detail-card i {
    color: #2980b9 !important; /* Blu per icone porto */
}

.user-menu i {
    color: #5a67d8 !important; /* Colore di default per menu utente */
}

/* ===== OVERRIDE FINALI ===== */

/* Assicura che nessuna icona sia completamente bianca su sfondo bianco */
.bg-white i:not(.text-primary):not(.text-success):not(.text-warning):not(.text-danger):not(.text-info):not(.text-dark) {
    color: #495057 !important;
}

/* Assicura che le icone su sfondi chiari abbiano contrasto */
.bg-light i:not(.text-primary):not(.text-success):not(.text-warning):not(.text-danger):not(.text-info):not(.text-dark) {
    color: #343a40 !important;
}

/* ===== MEDIA QUERIES PER RESPONSIVE ===== */

@media (max-width: 768px) {
    /* Su mobile, assicura che le icone siano sempre visibili */
    .dropdown-menu .dropdown-item i {
        color: #495057 !important;
        background: rgba(0, 0, 0, 0.05) !important;
    }
}

/* ===== DEBUG (rimuovi in produzione) ===== */
/*
.text-white {
    border: 1px solid red !important;
}
*/
