/* ===== FIX CONTRASTI TEMA SCURO GLOBALI SNIP ===== */

/* ===== FIX TESTI PROBLEMATICI ===== */

/* Fix testi bianchi inline che potrebbero non essere visibili */
body.theme-dark [style*="color: white"],
body.theme-dark [style*="color:#fff"],
body.theme-dark [style*="color: #ffffff"] {
    color: #ecf0f1 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* Fix classi Bootstrap problematiche */
body.theme-dark .text-white {
    color: #ecf0f1 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

body.theme-dark .text-light {
    color: #bdc3c7 !important;
    font-weight: 500 !important;
}

body.theme-dark .text-muted {
    color: #95a5a6 !important;
    font-weight: 500 !important;
}

body.theme-dark .text-secondary {
    color: #95a5a6 !important;
    font-weight: 500 !important;
}

/* ===== FIX SFONDI PROBLEMATICI ===== */

body.theme-dark .bg-light {
    background-color: rgba(52, 73, 94, 0.8) !important;
}

body.theme-dark .bg-white {
    background-color: rgba(44, 62, 80, 0.9) !important;
}

/* ===== FIX FORM CONTROLS ===== */

/* Fix placeholder */
body.theme-dark .form-control::placeholder,
body.theme-dark .form-select::placeholder,
body.theme-dark input::placeholder,
body.theme-dark textarea::placeholder {
    color: #95a5a6 !important;
    opacity: 0.8 !important;
}

/* Fix form controls generali */
body.theme-dark .form-control,
body.theme-dark .form-select {
    background-color: rgba(52, 73, 94, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .form-control:focus,
body.theme-dark .form-select:focus {
    background-color: rgba(52, 73, 94, 1) !important;
    border-color: #3498db !important;
    color: #ecf0f1 !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

/* ===== FIX ELEMENTI DISABILITATI ===== */

body.theme-dark .disabled,
body.theme-dark :disabled,
body.theme-dark .btn:disabled {
    color: #7f8c8d !important;
    opacity: 0.6 !important;
    background-color: rgba(127, 140, 141, 0.2) !important;
}

/* ===== FIX GRADIENTI PROBLEMATICI ===== */

/* Fix generale per tutti i gradienti */
body.theme-dark .card[style*="linear-gradient"] .card-body,
body.theme-dark .card[style*="linear-gradient"] .card-body *,
body.theme-dark .card[style*="linear-gradient"] .card-header,
body.theme-dark .card[style*="linear-gradient"] .card-header * {
    color: #ecf0f1 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* Fix specifici per gradienti comuni */
body.theme-dark [style*="background: linear-gradient"] {
    color: #ecf0f1 !important;
}

body.theme-dark [style*="background: linear-gradient"] * {
    color: #ecf0f1 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* ===== FIX NAVBAR ===== */

body.theme-dark .navbar .nav-link {
    color: #ecf0f1 !important;
    font-weight: 500 !important;
}

body.theme-dark .navbar .nav-link:hover {
    color: #ffffff !important;
}

body.theme-dark .navbar-brand {
    color: #ecf0f1 !important;
    font-weight: 600 !important;
}

/* ===== FIX DROPDOWN ===== */

body.theme-dark .dropdown-menu {
    background-color: rgba(52, 73, 94, 0.95) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .dropdown-item {
    color: #ecf0f1 !important;
}

body.theme-dark .dropdown-item:hover {
    background-color: rgba(52, 152, 219, 0.3) !important;
    color: #ffffff !important;
}

/* ===== FIX TABELLE ===== */

body.theme-dark .table {
    color: #ecf0f1 !important;
}

body.theme-dark .table thead th {
    background-color: rgba(44, 62, 80, 0.95) !important;
    color: #ecf0f1 !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .table tbody td {
    color: #ecf0f1 !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

body.theme-dark .table tbody tr:hover td {
    background-color: rgba(52, 152, 219, 0.2) !important;
    color: #ffffff !important;
}

/* ===== FIX ALERT ===== */

body.theme-dark .alert {
    color: #ecf0f1 !important;
    font-weight: 500 !important;
}

body.theme-dark .alert-info {
    background-color: rgba(52, 152, 219, 0.2) !important;
    border-color: rgba(52, 152, 219, 0.4) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .alert-warning {
    background-color: rgba(241, 196, 15, 0.2) !important;
    border-color: rgba(241, 196, 15, 0.4) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .alert-success {
    background-color: rgba(46, 204, 113, 0.2) !important;
    border-color: rgba(46, 204, 113, 0.4) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .alert-danger {
    background-color: rgba(231, 76, 60, 0.2) !important;
    border-color: rgba(231, 76, 60, 0.4) !important;
    color: #ecf0f1 !important;
}

/* ===== FIX BADGE ===== */

body.theme-dark .badge {
    color: #ecf0f1 !important;
    font-weight: 600 !important;
}

body.theme-dark .badge.bg-light {
    background-color: rgba(52, 73, 94, 0.8) !important;
    color: #ecf0f1 !important;
}

/* ===== FIX PULSANTI ===== */

body.theme-dark .btn-outline-light {
    color: #ecf0f1 !important;
    border-color: #ecf0f1 !important;
}

body.theme-dark .btn-outline-light:hover {
    background-color: #ecf0f1 !important;
    color: #2c3e50 !important;
}

/* ===== FIX MODAL ===== */

body.theme-dark .modal-content {
    background-color: rgba(52, 73, 94, 0.95) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .modal-header {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .modal-footer {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

/* ===== FIX BREADCRUMB ===== */

body.theme-dark .breadcrumb {
    background-color: rgba(52, 73, 94, 0.8) !important;
}

body.theme-dark .breadcrumb-item {
    color: #bdc3c7 !important;
}

body.theme-dark .breadcrumb-item.active {
    color: #ecf0f1 !important;
}

/* ===== FIX PAGINATION ===== */

body.theme-dark .page-link {
    background-color: rgba(52, 73, 94, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .page-link:hover {
    background-color: rgba(52, 152, 219, 0.3) !important;
    color: #ffffff !important;
}

/* ===== FIX SPECIFICI PER ELEMENTI COMUNI ===== */

/* Fix per testi piccoli */
body.theme-dark small,
body.theme-dark .small {
    color: #bdc3c7 !important;
    font-weight: 500 !important;
}

/* Fix per link */
body.theme-dark a {
    color: #3498db !important;
}

body.theme-dark a:hover {
    color: #5dade2 !important;
}

/* Fix per icone */
body.theme-dark .fas,
body.theme-dark .far,
body.theme-dark .fab {
    color: inherit !important;
}

/* ===== FIX SPECIFICI PER COMPONENTI SNIP ===== */

/* Fix per avatar circle */
body.theme-dark .avatar-circle {
    background-color: rgba(52, 152, 219, 0.8) !important;
    color: #ffffff !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

/* Fix per status indicator */
body.theme-dark .status-indicator {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* ===== FIX SPECIFICI INPUT GROUP ===== */

/* Fix per icone negli input-group-text */
body.theme-dark .input-group-text {
    background-color: rgba(52, 73, 94, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .input-group-text i {
    color: #ecf0f1 !important;
    font-size: 1.1em !important;
}

/* Fix specifico per icona lente di ricerca */
body.theme-dark .input-group-text .fa-search {
    color: #3498db !important;
    font-weight: 600 !important;
}

/* Fix per input group nel tema scuro */
body.theme-dark .input-group .form-control {
    background-color: rgba(52, 73, 94, 0.95) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: #ecf0f1 !important;
    font-weight: 500 !important;
}

body.theme-dark .input-group .form-control:focus {
    background-color: rgba(44, 62, 80, 1) !important;
    border-color: #3498db !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

/* Fix specifico per campo ricerca navi */
body.theme-dark input[name="search"] {
    background-color: rgba(52, 73, 94, 0.95) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: #ecf0f1 !important;
    font-weight: 500 !important;
}

body.theme-dark input[name="search"]:focus {
    background-color: rgba(44, 62, 80, 1) !important;
    border-color: #3498db !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

body.theme-dark input[name="search"]::placeholder {
    color: #95a5a6 !important;
    opacity: 0.8 !important;
    font-weight: 400 !important;
}

body.theme-dark .input-group .btn {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Fix per pulsanti nell'input group */
body.theme-dark .input-group .btn-outline-secondary {
    background-color: rgba(52, 73, 94, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .input-group .btn-outline-secondary:hover {
    background-color: rgba(231, 76, 60, 0.8) !important;
    border-color: #e74c3c !important;
    color: #ffffff !important;
}
