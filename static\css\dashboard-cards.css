/* ===== CARD DASHBOARD PROFESSIONALI SNIP ===== */

/* ===== BASE CARD STYLING ===== */
.stat-card {
    border-radius: 20px !important;
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.1),
        0 4px 15px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    margin-bottom: 2rem !important;
    overflow: hidden !important;
    border: none !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 30px rgba(0, 0, 0, 0.1) !important;
}

.stat-card .card-body {
    padding: 2rem !important;
    position: relative !important;
    z-index: 2 !important;
}

/* ===== ICONE MODERNE ===== */
.stat-card .stat-icon {
    font-size: 3rem !important;
    margin-bottom: 1.5rem !important;
    display: inline-block !important;
    padding: 20px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15) !important;
}

/* ===== NUMERI E TESTO ===== */
.stat-card .stat-number {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 0.5rem !important;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    line-height: 1.2 !important;
}

.stat-card .stat-label {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1.5px !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.stat-card .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* ===== VARIANTI COLORE PROFESSIONALI CON TEMI ===== */

/* ===== CARD PRIMARY (Navi Totali) ===== */
/* Tema Marittimo */
body.theme-maritime .stat-card.primary,
body:not([class*="theme-"]) .stat-card.primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    border-left: 5px solid #1e3c72 !important;
}

/* Tema Scuro */
body.theme-dark .stat-card.primary {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
    border-left: 5px solid #34495e !important;
}

/* Tema Chiaro */
body.theme-light .stat-card.primary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    border-left: 5px solid #6c757d !important;
}

/* Icone e elementi comuni per primary */
.stat-card.primary .stat-icon {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.stat-card.primary::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
}

/* ===== CARD SUCCESS (Navi Schedulate) ===== */
/* Tema Marittimo */
body.theme-maritime .stat-card.success,
body:not([class*="theme-"]) .stat-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
    border-left: 5px solid #10ac84 !important;
}

/* Tema Scuro */
body.theme-dark .stat-card.success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
    border-left: 5px solid #27ae60 !important;
}

/* Tema Chiaro */
body.theme-light .stat-card.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border-left: 5px solid #28a745 !important;
}

/* Icone e elementi comuni per success */
.stat-card.success .stat-icon {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.stat-card.success::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
}

/* ===== CARD WARNING (SOF da Completare) ===== */
/* Tema Marittimo */
body.theme-maritime .stat-card.warning,
body:not([class*="theme-"]) .stat-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border-left: 5px solid #ff6b6b !important;
}

/* Tema Scuro */
body.theme-dark .stat-card.warning {
    background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%) !important;
    border-left: 5px solid #e67e22 !important;
}

/* Tema Chiaro */
body.theme-light .stat-card.warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    border-left: 5px solid #ffc107 !important;
}

/* Icone e elementi comuni per warning */
.stat-card.warning .stat-icon {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.stat-card.warning::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
}

/* ===== CARD INFO (Utenti Online) ===== */
/* Tema Marittimo */
body.theme-maritime .stat-card.info,
body:not([class*="theme-"]) .stat-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border-left: 5px solid #17a2b8 !important;
}

/* Tema Scuro */
body.theme-dark .stat-card.info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
    border-left: 5px solid #3498db !important;
}

/* Tema Chiaro */
body.theme-light .stat-card.info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    border-left: 5px solid #17a2b8 !important;
}

/* Icone e elementi comuni per info */
.stat-card.info .stat-icon {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.stat-card.info::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
}

/* ===== CARD DANGER (Pericolo) ===== */
/* Tema Marittimo */
body.theme-maritime .stat-card.danger,
body:not([class*="theme-"]) .stat-card.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    border-left: 5px solid #e74c3c !important;
}

/* Tema Scuro */
body.theme-dark .stat-card.danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    border-left: 5px solid #e74c3c !important;
}

/* Tema Chiaro */
body.theme-light .stat-card.danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border-left: 5px solid #dc3545 !important;
}

/* Icone e elementi comuni per danger */
.stat-card.danger .stat-icon {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.stat-card.danger::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
}

/* ===== EFFETTI SPECIALI ===== */

/* Shimmer effect per le card */
.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.8s ease;
    z-index: 1;
}

.stat-card:hover::after {
    left: 100%;
}

/* Pulse animation per i numeri */
@keyframes statPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.stat-card .stat-number.animate {
    animation: statPulse 2s ease-in-out infinite;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1200px) {
    .stat-card .stat-icon {
        font-size: 2.5rem !important;
        padding: 15px !important;
    }
    
    .stat-card .stat-number {
        font-size: 2.2rem !important;
    }
}

@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1.5rem !important;
    }
    
    .stat-card .card-body {
        padding: 1.5rem !important;
    }
    
    .stat-card .stat-icon {
        font-size: 2rem !important;
        padding: 12px !important;
        margin-bottom: 1rem !important;
    }
    
    .stat-card .stat-number {
        font-size: 2rem !important;
    }
    
    .stat-card .stat-label {
        font-size: 0.9rem !important;
        letter-spacing: 1px !important;
    }
}

@media (max-width: 576px) {
    .stat-card:hover {
        transform: translateY(-4px) scale(1.01) !important;
    }
    
    .stat-card .stat-icon {
        font-size: 1.8rem !important;
        padding: 10px !important;
    }
    
    .stat-card .stat-number {
        font-size: 1.8rem !important;
    }
}

/* ===== ANIMAZIONI AVANZATE ===== */

/* Floating animation */
@keyframes floating {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.stat-card.floating {
    animation: floating 3s ease-in-out infinite;
}

/* Glow effect */
.stat-card.glow {
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.1),
        0 0 50px rgba(102, 126, 234, 0.3) !important;
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    .stat-card {
        backdrop-filter: blur(15px) !important;
    }
    
    .stat-card::before {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
    }
}
