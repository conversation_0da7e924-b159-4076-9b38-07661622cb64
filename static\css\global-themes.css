/* ===== SISTEMA TEMI GLOBALE SNIP ===== */

/* ===== TEMA MARITTIMO (DEFAULT) ===== */
body.theme-maritime,
body:not([class*="theme-"]) {
    /* Background principale */
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #212529;
}

/* Navbar tema marittimo */
body.theme-maritime .snip-navbar,
body:not([class*="theme-"]) .snip-navbar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
}

/* Card tema marittimo */
body.theme-maritime .card,
body:not([class*="theme-"]) .card {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #212529 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Form controls tema marittimo */
body.theme-maritime .form-control,
body:not([class*="theme-"]) .form-control {
    background: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(0, 0, 0, 0.125) !important;
    color: #212529 !important;
}

/* ===== TEMA SCURO ===== */
body.theme-dark {
    /* Background principale scuro */
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    color: #ecf0f1 !important;
}

/* Navbar tema scuro */
body.theme-dark .snip-navbar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
}

/* Card tema scuro */
body.theme-dark .card {
    background: rgba(52, 73, 94, 0.95) !important;
    color: #ecf0f1 !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.theme-dark .card-header {
    background: rgba(44, 62, 80, 0.9) !important;
    color: #ecf0f1 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.theme-dark .card-body {
    background: rgba(52, 73, 94, 0.95) !important;
    color: #ecf0f1 !important;
}

/* Form controls tema scuro */
body.theme-dark .form-control {
    background: rgba(44, 62, 80, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .form-control:focus {
    background: rgba(44, 62, 80, 0.9) !important;
    border-color: #667eea !important;
    color: #ecf0f1 !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

body.theme-dark .form-label {
    color: #bdc3c7 !important;
}

body.theme-dark .form-select {
    background: rgba(44, 62, 80, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

/* Tabelle tema scuro */
body.theme-dark .table {
    color: #ecf0f1 !important;
}

body.theme-dark .table th {
    background: rgba(44, 62, 80, 0.9) !important;
    color: #ecf0f1 !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

body.theme-dark .table td {
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Dropdown tema scuro */
body.theme-dark .dropdown-menu {
    background: rgba(52, 73, 94, 0.98) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .dropdown-item {
    color: #ecf0f1 !important;
}

body.theme-dark .dropdown-item:hover {
    background: rgba(102, 126, 234, 0.8) !important;
    color: white !important;
}

/* Modal tema scuro */
body.theme-dark .modal-content {
    background: rgba(52, 73, 94, 0.98) !important;
    color: #ecf0f1 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .modal-header {
    background: rgba(44, 62, 80, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.theme-dark .modal-footer {
    background: rgba(44, 62, 80, 0.9) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Alert tema scuro */
body.theme-dark .alert {
    background: rgba(44, 62, 80, 0.9) !important;
    color: #ecf0f1 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Badge tema scuro */
body.theme-dark .badge {
    background: rgba(102, 126, 234, 0.8) !important;
    color: white !important;
}

/* Badge tema chiaro */
body.theme-light .badge.bg-primary {
    background: #495057 !important;
    color: white !important;
}

body.theme-light .badge.bg-secondary {
    background: #6c757d !important;
    color: white !important;
}

body.theme-light .badge.bg-info {
    background: #17a2b8 !important;
    color: white !important;
}

body.theme-light .badge.bg-warning {
    background: #ffc107 !important;
    color: #212529 !important;
}

body.theme-light .badge.bg-success {
    background: #28a745 !important;
    color: white !important;
}

body.theme-light .badge.bg-danger {
    background: #dc3545 !important;
    color: white !important;
}

/* ===== TEMA CHIARO ===== */
body.theme-light {
    /* Background principale chiaro */
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #212529 !important;
}

/* Navbar tema chiaro */
body.theme-light .snip-navbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

body.theme-light .navbar .nav-link {
    color: #343a40 !important;
    font-weight: 500 !important;
}

body.theme-light .navbar .nav-link:hover {
    color: #212529 !important;
}

body.theme-light .navbar-brand {
    color: #212529 !important;
    font-weight: 600 !important;
}

/* Card tema chiaro */
body.theme-light .card {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
}

body.theme-light .card-header {
    background: rgba(248, 249, 250, 0.95) !important;
    color: #212529 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light .card-header h5 {
    color: #212529 !important;
    font-weight: 600 !important;
}

body.theme-light .card-body {
    color: #212529 !important;
}

/* Form controls tema chiaro */
body.theme-light .form-control {
    background: rgba(255, 255, 255, 0.98) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    color: #212529 !important;
}

body.theme-light .form-control:focus {
    background: rgba(255, 255, 255, 1) !important;
    border-color: #495057 !important;
    color: #212529 !important;
    box-shadow: 0 0 0 0.2rem rgba(73, 80, 87, 0.25) !important;
}

body.theme-light .form-label {
    color: #343a40 !important;
    font-weight: 500 !important;
}

body.theme-light .form-select {
    background: rgba(255, 255, 255, 0.98) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    color: #212529 !important;
}

/* Tabelle tema chiaro */
body.theme-light .table {
    color: #212529 !important;
}

body.theme-light .table th {
    background: rgba(248, 249, 250, 0.95) !important;
    color: #212529 !important;
    font-weight: 600 !important;
    border-color: rgba(0, 0, 0, 0.15) !important;
}

body.theme-light .table td {
    color: #212529 !important;
    border-color: rgba(0, 0, 0, 0.1) !important;
}

body.theme-light .table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Dropdown tema chiaro */
body.theme-light .dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

body.theme-light .dropdown-item {
    color: #212529 !important;
    font-weight: 500 !important;
}

body.theme-light .dropdown-item:hover {
    background: rgba(73, 80, 87, 0.1) !important;
    color: #212529 !important;
}

/* ===== COMPONENTI SPECIFICI ===== */

/* Stat Cards - Mantengono i loro gradienti ma si adattano al tema */
body.theme-dark .stat-card {
    backdrop-filter: blur(15px) !important;
}

body.theme-light .stat-card {
    backdrop-filter: blur(5px) !important;
}

/* Date Filter - Filtro per data SOF Archiviati */
body.theme-dark .date-filter {
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.9) 0%, rgba(44, 62, 80, 0.9) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ecf0f1 !important;
}

body.theme-light .date-filter {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: #212529 !important;
}

body.theme-maritime .date-filter {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #212529 !important;
}

/* Date Filter Labels */
body.theme-dark .date-filter .form-label {
    color: #ecf0f1 !important;
    font-weight: 600 !important;
}

body.theme-light .date-filter .form-label {
    color: #212529 !important;
    font-weight: 600 !important;
}

body.theme-maritime .date-filter .form-label {
    color: #212529 !important;
    font-weight: 600 !important;
}

/* Date Filter Inputs */
body.theme-dark .date-filter .form-control {
    background: rgba(44, 62, 80, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .date-filter .form-control:focus {
    background: rgba(44, 62, 80, 0.9) !important;
    border-color: #3498db !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
    color: #ecf0f1 !important;
}

/* Date Filter Text Muted */
body.theme-dark .date-filter .text-muted {
    color: #bdc3c7 !important;
}

body.theme-light .date-filter .text-muted {
    color: #6c757d !important;
}

body.theme-maritime .date-filter .text-muted {
    color: #6c757d !important;
}

/* Archive Cards - Card degli archivi SOF */
body.theme-dark .archive-card {
    background: rgba(52, 73, 94, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .archive-card:hover {
    background: rgba(52, 73, 94, 1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

body.theme-dark .archive-card .card-header {
    background: rgba(44, 62, 80, 0.8) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .archive-card .file-info {
    color: #bdc3c7 !important;
}

/* Stats Card per SOF Archiviati */
body.theme-dark .stats-card {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.8) 0%, rgba(41, 128, 185, 0.8) 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ecf0f1 !important;
}

body.theme-light .stats-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    border: none !important;
    color: #212529 !important;
}

body.theme-maritime .stats-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    border: none !important;
    color: #212529 !important;
}

/* Porto Cards */
body.theme-dark .porto-detail-card {
    background: rgba(52, 73, 94, 0.95) !important;
    color: #ecf0f1 !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.theme-light .porto-detail-card {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 1px solid rgba(0, 0, 0, 0.125) !important;
}

/* Breadcrumb */
body.theme-dark .breadcrumb {
    background: rgba(44, 62, 80, 0.8) !important;
}

body.theme-dark .breadcrumb-item a {
    color: #bdc3c7 !important;
}

body.theme-light .breadcrumb {
    background: rgba(248, 249, 250, 0.9) !important;
}

/* Paginazione */
body.theme-dark .page-link {
    background: rgba(44, 62, 80, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .page-link:hover {
    background: rgba(102, 126, 234, 0.8) !important;
    border-color: rgba(102, 126, 234, 0.8) !important;
    color: white !important;
}

body.theme-light .page-link {
    background: rgba(255, 255, 255, 0.95) !important;
    border-color: rgba(0, 0, 0, 0.125) !important;
    color: #495057 !important;
}

/* ===== DISABILITA ANIMAZIONI SE RICHIESTO ===== */
body.no-animations * {
    animation: none !important;
    transition: none !important;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    body.theme-dark .card {
        background: rgba(52, 73, 94, 0.98) !important;
    }
    
    body.theme-light .card {
        background: rgba(255, 255, 255, 0.99) !important;
    }
}

/* ===== OVERRIDE FINALI ===== */
body.theme-dark .text-dark {
    color: #ecf0f1 !important;
}

body.theme-dark .text-muted {
    color: #bdc3c7 !important;
}

/* ===== CORREZIONI LEGGIBILITÀ TEMA SCURO ===== */

/* Testi principali */
body.theme-dark .text-primary {
    color: #58a6ff !important;
}

body.theme-dark .text-secondary {
    color: #8b949e !important;
}

body.theme-dark .text-success {
    color: #3fb950 !important;
}

body.theme-dark .text-warning {
    color: #d29922 !important;
}

body.theme-dark .text-danger {
    color: #f85149 !important;
}

body.theme-dark .text-info {
    color: #79c0ff !important;
}

body.theme-dark .text-light {
    color: #f0f6fc !important;
}

body.theme-dark .text-white {
    color: #ffffff !important;
}

/* Pulsanti tema scuro */
body.theme-dark .btn-primary {
    background-color: #238636 !important;
    border-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-primary:hover {
    background-color: #2ea043 !important;
    border-color: #2ea043 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-secondary {
    background-color: #30363d !important;
    border-color: #30363d !important;
    color: #f0f6fc !important;
}

body.theme-dark .btn-secondary:hover {
    background-color: #484f58 !important;
    border-color: #484f58 !important;
    color: #f0f6fc !important;
}

body.theme-dark .btn-success {
    background-color: #238636 !important;
    border-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-success:hover {
    background-color: #2ea043 !important;
    border-color: #2ea043 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-warning {
    background-color: #9e6a03 !important;
    border-color: #9e6a03 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-warning:hover {
    background-color: #bf8700 !important;
    border-color: #bf8700 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-danger {
    background-color: #da3633 !important;
    border-color: #da3633 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-danger:hover {
    background-color: #f85149 !important;
    border-color: #f85149 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-info {
    background-color: #0969da !important;
    border-color: #0969da !important;
    color: #ffffff !important;
}

body.theme-dark .btn-info:hover {
    background-color: #1f6feb !important;
    border-color: #1f6feb !important;
    color: #ffffff !important;
}

body.theme-dark .btn-light {
    background-color: #f0f6fc !important;
    border-color: #f0f6fc !important;
    color: #24292f !important;
}

body.theme-dark .btn-light:hover {
    background-color: #e6edf3 !important;
    border-color: #e6edf3 !important;
    color: #24292f !important;
}

body.theme-dark .btn-dark {
    background-color: #21262d !important;
    border-color: #21262d !important;
    color: #f0f6fc !important;
}

body.theme-dark .btn-dark:hover {
    background-color: #30363d !important;
    border-color: #30363d !important;
    color: #f0f6fc !important;
}

/* Pulsanti outline tema scuro */
body.theme-dark .btn-outline-primary {
    border-color: #58a6ff !important;
    color: #58a6ff !important;
}

body.theme-dark .btn-outline-primary:hover {
    background-color: #58a6ff !important;
    border-color: #58a6ff !important;
    color: #ffffff !important;
}

body.theme-dark .btn-outline-secondary {
    border-color: #8b949e !important;
    color: #8b949e !important;
}

body.theme-dark .btn-outline-secondary:hover {
    background-color: #8b949e !important;
    border-color: #8b949e !important;
    color: #ffffff !important;
}

body.theme-dark .btn-outline-success {
    border-color: #3fb950 !important;
    color: #3fb950 !important;
}

body.theme-dark .btn-outline-success:hover {
    background-color: #3fb950 !important;
    border-color: #3fb950 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-outline-warning {
    border-color: #d29922 !important;
    color: #d29922 !important;
}

body.theme-dark .btn-outline-warning:hover {
    background-color: #d29922 !important;
    border-color: #d29922 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-outline-danger {
    border-color: #f85149 !important;
    color: #f85149 !important;
}

body.theme-dark .btn-outline-danger:hover {
    background-color: #f85149 !important;
    border-color: #f85149 !important;
    color: #ffffff !important;
}

body.theme-dark .btn-outline-info {
    border-color: #79c0ff !important;
    color: #79c0ff !important;
}

body.theme-dark .btn-outline-info:hover {
    background-color: #79c0ff !important;
    border-color: #79c0ff !important;
    color: #ffffff !important;
}

body.theme-dark .btn-outline-light {
    border-color: #f0f6fc !important;
    color: #f0f6fc !important;
}

body.theme-dark .btn-outline-light:hover {
    background-color: #f0f6fc !important;
    border-color: #f0f6fc !important;
    color: #24292f !important;
}

body.theme-dark .btn-outline-dark {
    border-color: #30363d !important;
    color: #30363d !important;
}

body.theme-dark .btn-outline-dark:hover {
    background-color: #30363d !important;
    border-color: #30363d !important;
    color: #f0f6fc !important;
}

/* Badge tema scuro ottimizzati */
body.theme-dark .badge.bg-primary {
    background-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .badge.bg-secondary {
    background-color: #30363d !important;
    color: #f0f6fc !important;
}

body.theme-dark .badge.bg-success {
    background-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .badge.bg-warning {
    background-color: #9e6a03 !important;
    color: #ffffff !important;
}

body.theme-dark .badge.bg-danger {
    background-color: #da3633 !important;
    color: #ffffff !important;
}

body.theme-dark .badge.bg-info {
    background-color: #0969da !important;
    color: #ffffff !important;
}

body.theme-dark .badge.bg-light {
    background-color: #f0f6fc !important;
    color: #24292f !important;
}

body.theme-dark .badge.bg-dark {
    background-color: #21262d !important;
    color: #f0f6fc !important;
}

/* Alert tema scuro ottimizzati */
body.theme-dark .alert-primary {
    background-color: rgba(88, 166, 255, 0.15) !important;
    border-color: #58a6ff !important;
    color: #58a6ff !important;
}

body.theme-dark .alert-secondary {
    background-color: rgba(139, 148, 158, 0.15) !important;
    border-color: #8b949e !important;
    color: #8b949e !important;
}

body.theme-dark .alert-success {
    background-color: rgba(63, 185, 80, 0.15) !important;
    border-color: #3fb950 !important;
    color: #3fb950 !important;
}

body.theme-dark .alert-warning {
    background-color: rgba(210, 153, 34, 0.15) !important;
    border-color: #d29922 !important;
    color: #d29922 !important;
}

body.theme-dark .alert-danger {
    background-color: rgba(248, 81, 73, 0.15) !important;
    border-color: #f85149 !important;
    color: #f85149 !important;
}

body.theme-dark .alert-info {
    background-color: rgba(121, 192, 255, 0.15) !important;
    border-color: #79c0ff !important;
    color: #79c0ff !important;
}

/* Card componenti tema scuro */
body.theme-dark .card-title {
    color: #f0f6fc !important;
}

body.theme-dark .card-text {
    color: #e6edf3 !important;
}

body.theme-dark .card-subtitle {
    color: #8b949e !important;
}

body.theme-dark .card-link {
    color: #58a6ff !important;
}

body.theme-dark .card-link:hover {
    color: #79c0ff !important;
}

/* Form componenti tema scuro */
body.theme-dark .form-text {
    color: #8b949e !important;
}

body.theme-dark .form-check-label {
    color: #f0f6fc !important;
}

/* ===== TABELLE TEMA SCURO OTTIMIZZATE ===== */

/* Tabella base */
body.theme-dark .table {
    background-color: #161b22 !important;
    color: #f0f6fc !important;
    border-color: #30363d !important;
}

/* Header tabella */
body.theme-dark .table th {
    background-color: #21262d !important;
    color: #f0f6fc !important;
    border-color: #30363d !important;
    font-weight: 600 !important;
}

/* Celle tabella */
body.theme-dark .table td {
    color: #e6edf3 !important;
    border-color: #30363d !important;
    background-color: transparent !important;
}

/* Hover righe */
body.theme-dark .table tbody tr:hover {
    background-color: rgba(56, 139, 253, 0.1) !important;
}

body.theme-dark .table tbody tr:hover td {
    color: #f0f6fc !important;
}

/* Testi specifici nelle tabelle */
body.theme-dark .table td strong {
    color: #f0f6fc !important;
}

body.theme-dark .table td small {
    color: #8b949e !important;
}

body.theme-dark .table td .text-muted {
    color: #8b949e !important;
}

/* Link nelle tabelle */
body.theme-dark .table td a {
    color: #58a6ff !important;
}

body.theme-dark .table td a:hover {
    color: #79c0ff !important;
}

/* Badge nelle tabelle */
body.theme-dark .table .badge {
    color: #ffffff !important;
}

/* Pulsanti nelle tabelle */
body.theme-dark .table .btn {
    color: inherit !important;
}

body.theme-dark .table .btn-outline-primary {
    border-color: #58a6ff !important;
    color: #58a6ff !important;
}

body.theme-dark .table .btn-outline-primary:hover {
    background-color: #58a6ff !important;
    color: #ffffff !important;
}

body.theme-dark .table .btn-outline-secondary {
    border-color: #8b949e !important;
    color: #8b949e !important;
}

body.theme-dark .table .btn-outline-secondary:hover {
    background-color: #8b949e !important;
    color: #ffffff !important;
}

body.theme-dark .table .btn-outline-success {
    border-color: #3fb950 !important;
    color: #3fb950 !important;
}

body.theme-dark .table .btn-outline-success:hover {
    background-color: #3fb950 !important;
    color: #ffffff !important;
}

body.theme-dark .table .btn-outline-warning {
    border-color: #d29922 !important;
    color: #d29922 !important;
}

body.theme-dark .table .btn-outline-warning:hover {
    background-color: #d29922 !important;
    color: #ffffff !important;
}

body.theme-dark .table .btn-outline-danger {
    border-color: #f85149 !important;
    color: #f85149 !important;
}

body.theme-dark .table .btn-outline-danger:hover {
    background-color: #f85149 !important;
    color: #ffffff !important;
}

body.theme-dark .table .btn-outline-info {
    border-color: #79c0ff !important;
    color: #79c0ff !important;
}

body.theme-dark .table .btn-outline-info:hover {
    background-color: #79c0ff !important;
    color: #ffffff !important;
}

/* Override per tutti i testi che potrebbero essere problematici */
body.theme-dark p,
body.theme-dark span,
body.theme-dark div,
body.theme-dark label,
body.theme-dark small {
    color: inherit !important;
}

/* Assicura che i testi nelle stat card siano visibili */
body.theme-dark .stat-number {
    color: #ffffff !important;
}

body.theme-dark .stat-label {
    color: #ffffff !important;
}

/* Override per icone */
body.theme-dark i,
body.theme-dark .fas,
body.theme-dark .far,
body.theme-dark .fab {
    color: inherit !important;
}

/* CORREZIONI CRITICHE TEMA CHIARO */
body.theme-light .text-white {
    color: #212529 !important;
}

body.theme-light .text-muted {
    color: #6c757d !important;
}

body.theme-light .text-dark {
    color: #212529 !important;
}

/* Forza tutti i testi bianchi a essere scuri nel tema chiaro */
body.theme-light * {
    color: inherit !important;
}

body.theme-light .navbar-brand,
body.theme-light .nav-link,
body.theme-light .dropdown-item,
body.theme-light .card-title,
body.theme-light .card-text,
body.theme-light .btn,
body.theme-light .form-label,
body.theme-light .form-control,
body.theme-light .table th,
body.theme-light .table td,
body.theme-light .breadcrumb-item,
body.theme-light .page-link {
    color: #212529 !important;
}

/* Elementi specifici che devono essere scuri */
body.theme-light .text-white,
body.theme-light .navbar .navbar-text,
body.theme-light .card-header,
body.theme-light .modal-header,
body.theme-light .modal-body,
body.theme-light .modal-footer {
    color: #212529 !important;
}

/* Assicura che i testi siano sempre leggibili */
body.theme-dark h1, body.theme-dark h2, body.theme-dark h3,
body.theme-dark h4, body.theme-dark h5, body.theme-dark h6 {
    color: #ecf0f1 !important;
}

body.theme-light h1, body.theme-light h2, body.theme-light h3,
body.theme-light h4, body.theme-light h5, body.theme-light h6 {
    color: #212529 !important;
    font-weight: 600 !important;
}

/* Breadcrumb tema chiaro */
body.theme-light .breadcrumb {
    background: rgba(248, 249, 250, 0.95) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

body.theme-light .breadcrumb-item a {
    color: #495057 !important;
}

body.theme-light .breadcrumb-item.active {
    color: #212529 !important;
}

/* Paginazione tema chiaro */
body.theme-light .page-link {
    background: rgba(255, 255, 255, 0.98) !important;
    border-color: rgba(0, 0, 0, 0.15) !important;
    color: #495057 !important;
}

body.theme-light .page-link:hover {
    background: rgba(73, 80, 87, 0.1) !important;
    border-color: rgba(73, 80, 87, 0.2) !important;
    color: #212529 !important;
}

/* Alert tema chiaro */
body.theme-light .alert {
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    color: #212529 !important;
}

body.theme-light .alert-info {
    background: rgba(23, 162, 184, 0.1) !important;
    border-color: rgba(23, 162, 184, 0.2) !important;
    color: #0c5460 !important;
}

body.theme-light .alert-warning {
    background: rgba(255, 193, 7, 0.1) !important;
    border-color: rgba(255, 193, 7, 0.2) !important;
    color: #856404 !important;
}

body.theme-light .alert-success {
    background: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.2) !important;
    color: #155724 !important;
}

body.theme-light .alert-danger {
    background: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.2) !important;
    color: #721c24 !important;
}

/* ===== PULSANTI TEMA CHIARO - CORREZIONE DEFINITIVA ===== */

/* Pulsanti base */
body.theme-light .btn {
    font-weight: 500 !important;
}

body.theme-light .btn-primary {
    background-color: #495057 !important;
    border-color: #495057 !important;
    color: white !important;
}

body.theme-light .btn-primary:hover,
body.theme-light .btn-primary:focus {
    background-color: #343a40 !important;
    border-color: #343a40 !important;
    color: white !important;
}

body.theme-light .btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

body.theme-light .btn-secondary:hover,
body.theme-light .btn-secondary:focus {
    background-color: #5a6268 !important;
    border-color: #5a6268 !important;
    color: white !important;
}

body.theme-light .btn-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

body.theme-light .btn-success:hover,
body.theme-light .btn-success:focus {
    background-color: #218838 !important;
    border-color: #218838 !important;
    color: white !important;
}

body.theme-light .btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

body.theme-light .btn-warning:hover,
body.theme-light .btn-warning:focus {
    background-color: #e0a800 !important;
    border-color: #e0a800 !important;
    color: #212529 !important;
}

body.theme-light .btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

body.theme-light .btn-danger:hover,
body.theme-light .btn-danger:focus {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
    color: white !important;
}

body.theme-light .btn-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
}

body.theme-light .btn-info:hover,
body.theme-light .btn-info:focus {
    background-color: #138496 !important;
    border-color: #138496 !important;
    color: white !important;
}

body.theme-light .btn-light {
    background-color: #f8f9fa !important;
    border-color: #f8f9fa !important;
    color: #212529 !important;
}

body.theme-light .btn-light:hover,
body.theme-light .btn-light:focus {
    background-color: #e2e6ea !important;
    border-color: #e2e6ea !important;
    color: #212529 !important;
}

body.theme-light .btn-dark {
    background-color: #343a40 !important;
    border-color: #343a40 !important;
    color: white !important;
}

body.theme-light .btn-dark:hover,
body.theme-light .btn-dark:focus {
    background-color: #23272b !important;
    border-color: #23272b !important;
    color: white !important;
}

/* Pulsanti Outline */
body.theme-light .btn-outline-primary {
    color: #495057 !important;
    border-color: #495057 !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-primary:hover,
body.theme-light .btn-outline-primary:focus {
    background-color: #495057 !important;
    border-color: #495057 !important;
    color: white !important;
}

body.theme-light .btn-outline-secondary {
    color: #6c757d !important;
    border-color: #6c757d !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-secondary:hover,
body.theme-light .btn-outline-secondary:focus {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

body.theme-light .btn-outline-success {
    color: #28a745 !important;
    border-color: #28a745 !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-success:hover,
body.theme-light .btn-outline-success:focus {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

body.theme-light .btn-outline-warning {
    color: #ffc107 !important;
    border-color: #ffc107 !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-warning:hover,
body.theme-light .btn-outline-warning:focus {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

body.theme-light .btn-outline-danger {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-danger:hover,
body.theme-light .btn-outline-danger:focus {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

body.theme-light .btn-outline-info {
    color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-info:hover,
body.theme-light .btn-outline-info:focus {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
}

body.theme-light .btn-outline-light {
    color: #6c757d !important;
    border-color: #6c757d !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-light:hover,
body.theme-light .btn-outline-light:focus {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

body.theme-light .btn-outline-dark {
    color: #343a40 !important;
    border-color: #343a40 !important;
    background-color: transparent !important;
}

body.theme-light .btn-outline-dark:hover,
body.theme-light .btn-outline-dark:focus {
    background-color: #343a40 !important;
    border-color: #343a40 !important;
    color: white !important;
}

/* ===== MODAL TEMA CHIARO ===== */
body.theme-light .modal-content {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light .modal-header {
    background: rgba(248, 249, 250, 0.95) !important;
    color: #212529 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light .modal-header .modal-title {
    color: #212529 !important;
}

body.theme-light .modal-body {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
}

body.theme-light .modal-footer {
    background: rgba(248, 249, 250, 0.95) !important;
    color: #212529 !important;
    border-top: 1px solid rgba(0, 0, 0, 0.15) !important;
}

/* ===== NAVBAR SPECIFICI TEMA CHIARO ===== */
body.theme-light .navbar .navbar-nav .nav-link {
    color: #343a40 !important;
    font-weight: 500 !important;
}

body.theme-light .navbar .navbar-nav .nav-link:hover {
    color: #212529 !important;
}

body.theme-light .navbar .navbar-brand {
    color: #212529 !important;
    font-weight: 600 !important;
}

/* ===== CARD SPECIFICI TEMA CHIARO ===== */
body.theme-light .card .card-title {
    color: #212529 !important;
    font-weight: 600 !important;
}

body.theme-light .card .card-text {
    color: #495057 !important;
}

body.theme-light .card .card-header h5 {
    color: #212529 !important;
    font-weight: 600 !important;
}

body.theme-light .card .card-header small {
    color: #6c757d !important;
}

/* ===== OVERRIDE SPECIFICI PER ELEMENTI PROBLEMATICI ===== */

/* Tutti gli elementi con classe text-white devono essere scuri */
body.theme-light .text-white,
body.theme-light .text-white * {
    color: #212529 !important;
}

/* Tutti i link devono essere visibili */
body.theme-light a {
    color: #495057 !important;
}

body.theme-light a:hover {
    color: #212529 !important;
}

/* Tutti i span e div devono ereditare il colore corretto */
body.theme-light span,
body.theme-light div,
body.theme-light p,
body.theme-light li,
body.theme-light td,
body.theme-light th {
    color: inherit !important;
}

/* Forza il colore per elementi specifici che potrebbero essere bianchi */
body.theme-light .navbar-text,
body.theme-light .nav-item,
body.theme-light .dropdown-header,
body.theme-light .list-group-item,
body.theme-light .card-subtitle,
body.theme-light .small,
body.theme-light .text-sm {
    color: #495057 !important;
}

/* Icone devono essere visibili */
body.theme-light i,
body.theme-light .fas,
body.theme-light .far,
body.theme-light .fab {
    color: inherit !important;
}

/* Override per elementi Bootstrap che potrebbero avere testo bianco */
body.theme-light .bg-primary,
body.theme-light .bg-secondary,
body.theme-light .bg-success,
body.theme-light .bg-danger,
body.theme-light .bg-warning,
body.theme-light .bg-info,
body.theme-light .bg-dark {
    color: white !important;
}

body.theme-light .bg-light,
body.theme-light .bg-white {
    color: #212529 !important;
}

/* Assicura che i placeholder siano visibili */
body.theme-light .form-control::placeholder {
    color: #6c757d !important;
    opacity: 1 !important;
}

/* Assicura che i testi dei select siano visibili */
body.theme-light .form-select option {
    color: #212529 !important;
    background: white !important;
}

/* ===== OVERRIDE FINALE TEMA CHIARO - FORZA LEGGIBILITÀ ===== */

/* RESET COMPLETO PER TEMA CHIARO */
body.theme-light {
    color: #212529 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* ===== OVERRIDE UNIVERSALE TEMA CHIARO ===== */

/* REGOLA UNIVERSALE: TUTTO DEVE ESSERE SCURO NEL TEMA CHIARO */
body.theme-light,
body.theme-light *,
body.theme-light *::before,
body.theme-light *::after {
    color: #212529 !important;
}

/* OVERRIDE SPECIFICO PER ELEMENTI CHE DEVONO RIMANERE BIANCHI */
body.theme-light .btn-primary,
body.theme-light .btn-primary *,
body.theme-light .btn-secondary,
body.theme-light .btn-secondary *,
body.theme-light .btn-success,
body.theme-light .btn-success *,
body.theme-light .btn-danger,
body.theme-light .btn-danger *,
body.theme-light .btn-info,
body.theme-light .btn-info *,
body.theme-light .btn-dark,
body.theme-light .btn-dark *,
body.theme-light .badge.bg-primary,
body.theme-light .badge.bg-primary *,
body.theme-light .badge.bg-secondary,
body.theme-light .badge.bg-secondary *,
body.theme-light .badge.bg-success,
body.theme-light .badge.bg-success *,
body.theme-light .badge.bg-danger,
body.theme-light .badge.bg-danger *,
body.theme-light .badge.bg-info,
body.theme-light .badge.bg-info *,
body.theme-light .badge.bg-dark,
body.theme-light .badge.bg-dark *,
body.theme-light .bg-primary,
body.theme-light .bg-primary *,
body.theme-light .bg-secondary,
body.theme-light .bg-secondary *,
body.theme-light .bg-success,
body.theme-light .bg-success *,
body.theme-light .bg-danger,
body.theme-light .bg-danger *,
body.theme-light .bg-info,
body.theme-light .bg-info *,
body.theme-light .bg-dark,
body.theme-light .bg-dark * {
    color: white !important;
}

/* ELEMENTI CHE DEVONO RIMANERE SCURI */
body.theme-light .btn-warning,
body.theme-light .btn-warning *,
body.theme-light .btn-light,
body.theme-light .btn-light *,
body.theme-light .badge.bg-warning,
body.theme-light .badge.bg-warning *,
body.theme-light .badge.bg-light,
body.theme-light .badge.bg-light *,
body.theme-light .bg-warning,
body.theme-light .bg-warning *,
body.theme-light .bg-light,
body.theme-light .bg-light * {
    color: #212529 !important;
}

/* ===== OVERRIDE SPECIFICI PER PULSANTI OUTLINE ===== */
body.theme-light .btn-outline-primary,
body.theme-light .btn-outline-primary * {
    color: #495057 !important;
}

body.theme-light .btn-outline-secondary,
body.theme-light .btn-outline-secondary * {
    color: #6c757d !important;
}

body.theme-light .btn-outline-success,
body.theme-light .btn-outline-success * {
    color: #28a745 !important;
}

body.theme-light .btn-outline-warning,
body.theme-light .btn-outline-warning * {
    color: #ffc107 !important;
}

body.theme-light .btn-outline-danger,
body.theme-light .btn-outline-danger * {
    color: #dc3545 !important;
}

body.theme-light .btn-outline-info,
body.theme-light .btn-outline-info * {
    color: #17a2b8 !important;
}

body.theme-light .btn-outline-light,
body.theme-light .btn-outline-light * {
    color: #6c757d !important;
}

body.theme-light .btn-outline-dark,
body.theme-light .btn-outline-dark * {
    color: #343a40 !important;
}

/* ===== OVERRIDE PER LINK E ANCHOR ===== */
body.theme-light a,
body.theme-light a * {
    color: #495057 !important;
}

body.theme-light a:hover,
body.theme-light a:hover * {
    color: #212529 !important;
}

/* ===== OVERRIDE PER DROPDOWN ITEMS ===== */
body.theme-light .dropdown-item,
body.theme-light .dropdown-item * {
    color: #343a40 !important;
}

body.theme-light .dropdown-item:hover,
body.theme-light .dropdown-item:hover * {
    color: white !important;
}

/* ===== OVERRIDE PER NAVBAR ===== */
body.theme-light .navbar,
body.theme-light .navbar * {
    color: #343a40 !important;
}

body.theme-light .navbar-brand,
body.theme-light .navbar-brand * {
    color: #212529 !important;
}

/* ===== OVERRIDE PER FORM ELEMENTS ===== */
body.theme-light .form-control,
body.theme-light .form-control *,
body.theme-light .form-select,
body.theme-light .form-select *,
body.theme-light .form-label,
body.theme-light .form-label * {
    color: #212529 !important;
}

body.theme-light .form-control::placeholder {
    color: #6c757d !important;
}

/* ===== OVERRIDE PER TABELLE ===== */
body.theme-light .table,
body.theme-light .table *,
body.theme-light .table th,
body.theme-light .table th *,
body.theme-light .table td,
body.theme-light .table td * {
    color: #212529 !important;
}

/* ===== OVERRIDE PER CARD ===== */
body.theme-light .card,
body.theme-light .card *,
body.theme-light .card-header,
body.theme-light .card-header *,
body.theme-light .card-body,
body.theme-light .card-body *,
body.theme-light .card-footer,
body.theme-light .card-footer * {
    color: #212529 !important;
}

/* ===== OVERRIDE PER MODAL ===== */
body.theme-light .modal,
body.theme-light .modal *,
body.theme-light .modal-content,
body.theme-light .modal-content *,
body.theme-light .modal-header,
body.theme-light .modal-header *,
body.theme-light .modal-body,
body.theme-light .modal-body *,
body.theme-light .modal-footer,
body.theme-light .modal-footer * {
    color: #212529 !important;
}

/* ===== OVERRIDE PER ELEMENTI SPECIFICI PROBLEMATICI ===== */

/* Tutti i span, div, p, li, strong, em, small */
body.theme-light span,
body.theme-light span *,
body.theme-light div,
body.theme-light div *,
body.theme-light p,
body.theme-light p *,
body.theme-light li,
body.theme-light li *,
body.theme-light strong,
body.theme-light strong *,
body.theme-light em,
body.theme-light em *,
body.theme-light small,
body.theme-light small *,
body.theme-light h1,
body.theme-light h1 *,
body.theme-light h2,
body.theme-light h2 *,
body.theme-light h3,
body.theme-light h3 *,
body.theme-light h4,
body.theme-light h4 *,
body.theme-light h5,
body.theme-light h5 *,
body.theme-light h6,
body.theme-light h6 * {
    color: #212529 !important;
}

/* Override per classi Bootstrap che potrebbero avere testo bianco */
body.theme-light .text-white,
body.theme-light .text-white * {
    color: #212529 !important;
}

body.theme-light .text-muted,
body.theme-light .text-muted * {
    color: #6c757d !important;
}

/* Override per breadcrumb */
body.theme-light .breadcrumb,
body.theme-light .breadcrumb *,
body.theme-light .breadcrumb-item,
body.theme-light .breadcrumb-item * {
    color: #495057 !important;
}

body.theme-light .breadcrumb-item.active,
body.theme-light .breadcrumb-item.active * {
    color: #212529 !important;
}

/* Override per paginazione */
body.theme-light .pagination,
body.theme-light .pagination *,
body.theme-light .page-link,
body.theme-light .page-link * {
    color: #495057 !important;
}

/* Override per list group */
body.theme-light .list-group,
body.theme-light .list-group *,
body.theme-light .list-group-item,
body.theme-light .list-group-item * {
    color: #212529 !important;
}

/* Override per progress bar */
body.theme-light .progress,
body.theme-light .progress *,
body.theme-light .progress-bar,
body.theme-light .progress-bar * {
    color: #212529 !important;
}

/* Override per tooltip e popover */
body.theme-light .tooltip,
body.theme-light .tooltip *,
body.theme-light .popover,
body.theme-light .popover * {
    color: #212529 !important;
}

/* Override per accordion */
body.theme-light .accordion,
body.theme-light .accordion *,
body.theme-light .accordion-item,
body.theme-light .accordion-item *,
body.theme-light .accordion-header,
body.theme-light .accordion-header *,
body.theme-light .accordion-body,
body.theme-light .accordion-body * {
    color: #212529 !important;
}

/* Override per offcanvas */
body.theme-light .offcanvas,
body.theme-light .offcanvas *,
body.theme-light .offcanvas-header,
body.theme-light .offcanvas-header *,
body.theme-light .offcanvas-body,
body.theme-light .offcanvas-body * {
    color: #212529 !important;
}

/* Override per toast */
body.theme-light .toast,
body.theme-light .toast *,
body.theme-light .toast-header,
body.theme-light .toast-header *,
body.theme-light .toast-body,
body.theme-light .toast-body * {
    color: #212529 !important;
}

/* ===== OVERRIDE FINALE ASSOLUTO ===== */

/* Cattura QUALSIASI elemento che potrebbe avere testo bianco */
body.theme-light [style*="color: white"],
body.theme-light [style*="color: #fff"],
body.theme-light [style*="color: #ffffff"],
body.theme-light [style*="color:white"],
body.theme-light [style*="color:#fff"],
body.theme-light [style*="color:#ffffff"] {
    color: #212529 !important;
}

/* Override per pseudo-elementi */
body.theme-light *::before,
body.theme-light *::after {
    color: inherit !important;
}

/* Override per elementi con display specifici */
body.theme-light .d-flex,
body.theme-light .d-flex *,
body.theme-light .d-block,
body.theme-light .d-block *,
body.theme-light .d-inline,
body.theme-light .d-inline *,
body.theme-light .d-inline-block,
body.theme-light .d-inline-block * {
    color: #212529 !important;
}

/* Override per utility classes */
body.theme-light .fw-bold,
body.theme-light .fw-bold *,
body.theme-light .fw-normal,
body.theme-light .fw-normal *,
body.theme-light .fw-light,
body.theme-light .fw-light *,
body.theme-light .fst-italic,
body.theme-light .fst-italic *,
body.theme-light .text-uppercase,
body.theme-light .text-uppercase *,
body.theme-light .text-lowercase,
body.theme-light .text-lowercase *,
body.theme-light .text-capitalize,
body.theme-light .text-capitalize * {
    color: #212529 !important;
}

/* Override per spacing utilities che potrebbero contenere testo */
body.theme-light .m-0,
body.theme-light .m-0 *,
body.theme-light .p-0,
body.theme-light .p-0 *,
body.theme-light .ms-auto,
body.theme-light .ms-auto *,
body.theme-light .me-auto,
body.theme-light .me-auto * {
    color: #212529 !important;
}

/* REGOLA FINALE: se tutto il resto fallisce, forza il colore */
body.theme-light * {
    color: #212529 !important;
}

/* Eccezioni finali per elementi che DEVONO rimanere bianchi */
body.theme-light .btn-primary,
body.theme-light .btn-secondary,
body.theme-light .btn-success,
body.theme-light .btn-danger,
body.theme-light .btn-info,
body.theme-light .btn-dark,
body.theme-light .badge.bg-primary,
body.theme-light .badge.bg-secondary,
body.theme-light .badge.bg-success,
body.theme-light .badge.bg-danger,
body.theme-light .badge.bg-info,
body.theme-light .badge.bg-dark,
body.theme-light .bg-primary,
body.theme-light .bg-secondary,
body.theme-light .bg-success,
body.theme-light .bg-danger,
body.theme-light .bg-info,
body.theme-light .bg-dark {
    color: white !important;
}

/* Eccezioni per elementi che devono rimanere con i loro colori specifici */
body.theme-light .btn-outline-primary {
    color: #495057 !important;
}

body.theme-light .btn-outline-secondary {
    color: #6c757d !important;
}

body.theme-light .btn-outline-success {
    color: #28a745 !important;
}

body.theme-light .btn-outline-warning {
    color: #ffc107 !important;
}

body.theme-light .btn-outline-danger {
    color: #dc3545 !important;
}

body.theme-light .btn-outline-info {
    color: #17a2b8 !important;
}

body.theme-light .text-primary {
    color: #495057 !important;
}

body.theme-light .text-secondary {
    color: #6c757d !important;
}

body.theme-light .text-success {
    color: #28a745 !important;
}

body.theme-light .text-warning {
    color: #ffc107 !important;
}

body.theme-light .text-danger {
    color: #dc3545 !important;
}

body.theme-light .text-info {
    color: #17a2b8 !important;
}

/* ===== OVERRIDE SPECIFICO STAT CARD ICONE ===== */

/* Forza le icone delle stat card a essere bianche */
body.theme-light .stat-card .stat-icon,
body.theme-light .stat-card .stat-icon *,
body.theme-light .stat-card .stat-icon i,
body.theme-light .stat-card .stat-icon .fas,
body.theme-light .stat-card .stat-icon .far,
body.theme-light .stat-card .stat-icon .fab {
    color: white !important;
}

/* ===== CORREZIONE SPECIFICA PULSANTE GENERA REPORT ===== */

/* Tutti i pulsanti nel tema chiaro devono avere testo nero se non specificato diversamente */
body.theme-light button,
body.theme-light button *,
body.theme-light .btn,
body.theme-light .btn *,
body.theme-light input[type="button"],
body.theme-light input[type="button"] *,
body.theme-light input[type="submit"],
body.theme-light input[type="submit"] * {
    color: #212529 !important;
}

/* Pulsanti con background colorato mantengono testo bianco */
body.theme-light .btn-primary,
body.theme-light .btn-primary *,
body.theme-light .btn-secondary,
body.theme-light .btn-secondary *,
body.theme-light .btn-success,
body.theme-light .btn-success *,
body.theme-light .btn-danger,
body.theme-light .btn-danger *,
body.theme-light .btn-info,
body.theme-light .btn-info *,
body.theme-light .btn-dark,
body.theme-light .btn-dark * {
    color: white !important;
}

/* Pulsanti warning e light mantengono testo scuro */
body.theme-light .btn-warning,
body.theme-light .btn-warning *,
body.theme-light .btn-light,
body.theme-light .btn-light * {
    color: #212529 !important;
}

/* Override specifico per qualsiasi pulsante che potrebbe avere problemi */
body.theme-light [class*="btn"],
body.theme-light [class*="btn"] * {
    color: #212529 !important;
}

/* Eccezioni per pulsanti colorati */
body.theme-light .btn-primary,
body.theme-light .btn-secondary,
body.theme-light .btn-success,
body.theme-light .btn-danger,
body.theme-light .btn-info,
body.theme-light .btn-dark {
    color: white !important;
}

/* Forza il colore per tutti gli elementi dentro i pulsanti colorati */
body.theme-light .btn-primary *,
body.theme-light .btn-secondary *,
body.theme-light .btn-success *,
body.theme-light .btn-danger *,
body.theme-light .btn-info *,
body.theme-light .btn-dark * {
    color: white !important;
}

/* ===== OVERRIDE BRUTALE PER TUTTI I PULSANTI ===== */

/* Regola nucleare: TUTTI i pulsanti nel tema chiaro hanno testo nero di default */
body.theme-light button,
body.theme-light .btn,
body.theme-light input[type="button"],
body.theme-light input[type="submit"],
body.theme-light input[type="reset"],
body.theme-light [role="button"] {
    color: #212529 !important;
}

/* Tutti gli elementi dentro i pulsanti */
body.theme-light button *,
body.theme-light .btn *,
body.theme-light input[type="button"] *,
body.theme-light input[type="submit"] *,
body.theme-light input[type="reset"] *,
body.theme-light [role="button"] * {
    color: #212529 !important;
}

/* Override per span, i, strong, em dentro i pulsanti */
body.theme-light button span,
body.theme-light button i,
body.theme-light button strong,
body.theme-light button em,
body.theme-light .btn span,
body.theme-light .btn i,
body.theme-light .btn strong,
body.theme-light .btn em {
    color: #212529 !important;
}

/* ECCEZIONI: Solo pulsanti con classi specifiche mantengono testo bianco */
body.theme-light .btn-primary,
body.theme-light .btn-secondary,
body.theme-light .btn-success,
body.theme-light .btn-danger,
body.theme-light .btn-info,
body.theme-light .btn-dark,
body.theme-light button.btn-primary,
body.theme-light button.btn-secondary,
body.theme-light button.btn-success,
body.theme-light button.btn-danger,
body.theme-light button.btn-info,
body.theme-light button.btn-dark {
    color: white !important;
}

/* E tutti i loro contenuti */
body.theme-light .btn-primary *,
body.theme-light .btn-secondary *,
body.theme-light .btn-success *,
body.theme-light .btn-danger *,
body.theme-light .btn-info *,
body.theme-light .btn-dark *,
body.theme-light button.btn-primary *,
body.theme-light button.btn-secondary *,
body.theme-light button.btn-success *,
body.theme-light button.btn-danger *,
body.theme-light button.btn-info *,
body.theme-light button.btn-dark * {
    color: white !important;
}

/* Override per pulsanti senza classi specifiche o con classi generiche */
body.theme-light button:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-info):not(.btn-dark),
body.theme-light .btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-info):not(.btn-dark) {
    color: #212529 !important;
}

body.theme-light button:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-info):not(.btn-dark) *,
body.theme-light .btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-info):not(.btn-dark) * {
    color: #212529 !important;
}

/* Eccezioni per elementi che devono mantenere il loro colore */
body.theme-light .btn-primary,
body.theme-light .btn-secondary,
body.theme-light .btn-success,
body.theme-light .btn-danger,
body.theme-light .btn-info,
body.theme-light .badge:not(.bg-warning):not(.bg-light):not(.bg-white),
body.theme-light .bg-primary,
body.theme-light .bg-secondary,
body.theme-light .bg-success,
body.theme-light .bg-danger,
body.theme-light .bg-info,
body.theme-light .bg-dark {
    color: white !important;
}

/* Warning e light backgrounds mantengono testo scuro */
body.theme-light .btn-warning,
body.theme-light .badge.bg-warning,
body.theme-light .badge.bg-light,
body.theme-light .badge.bg-white,
body.theme-light .bg-warning,
body.theme-light .bg-light,
body.theme-light .bg-white {
    color: #212529 !important;
}

/* Outline buttons mantengono il loro colore fino all'hover */
body.theme-light .btn-outline-primary:not(:hover) {
    color: #495057 !important;
}

body.theme-light .btn-outline-secondary:not(:hover) {
    color: #6c757d !important;
}

body.theme-light .btn-outline-success:not(:hover) {
    color: #28a745 !important;
}

body.theme-light .btn-outline-warning:not(:hover) {
    color: #ffc107 !important;
}

body.theme-light .btn-outline-danger:not(:hover) {
    color: #dc3545 !important;
}

body.theme-light .btn-outline-info:not(:hover) {
    color: #17a2b8 !important;
}

/* Text utilities specifici */
body.theme-light .text-primary {
    color: #495057 !important;
}

body.theme-light .text-secondary {
    color: #6c757d !important;
}

body.theme-light .text-success {
    color: #28a745 !important;
}

body.theme-light .text-warning {
    color: #ffc107 !important;
}

body.theme-light .text-danger {
    color: #dc3545 !important;
}

body.theme-light .text-info {
    color: #17a2b8 !important;
}

/* ===== CORREZIONE DEFINITIVA ICONE TEMA CHIARO ===== */

/* Tutte le icone FontAwesome devono essere visibili */
body.theme-light i,
body.theme-light .fas,
body.theme-light .far,
body.theme-light .fab,
body.theme-light .fal,
body.theme-light .fad {
    color: #495057 !important;
}

/* Icone con classi di colore specifiche */
body.theme-light .text-primary i,
body.theme-light i.text-primary {
    color: #495057 !important;
}

body.theme-light .text-secondary i,
body.theme-light i.text-secondary {
    color: #6c757d !important;
}

body.theme-light .text-success i,
body.theme-light i.text-success {
    color: #28a745 !important;
}

body.theme-light .text-warning i,
body.theme-light i.text-warning {
    color: #ffc107 !important;
}

body.theme-light .text-danger i,
body.theme-light i.text-danger {
    color: #dc3545 !important;
}

body.theme-light .text-info i,
body.theme-light i.text-info {
    color: #17a2b8 !important;
}

body.theme-light .text-light i,
body.theme-light i.text-light {
    color: #6c757d !important;
}

body.theme-light .text-dark i,
body.theme-light i.text-dark {
    color: #212529 !important;
}

/* Icone nei pulsanti mantengono il colore del pulsante */
body.theme-light .btn i {
    color: inherit !important;
}

/* Icone nei badge mantengono il colore del badge */
body.theme-light .badge i {
    color: inherit !important;
}

/* Icone negli alert mantengono il colore dell'alert */
body.theme-light .alert i {
    color: inherit !important;
}

/* Icone standalone senza classi specifiche */
body.theme-light .card i,
body.theme-light .table i,
body.theme-light .nav i,
body.theme-light .dropdown-item i,
body.theme-light .form-label i,
body.theme-light .breadcrumb i {
    color: #495057 !important;
}

/* ===== CORREZIONE Z-INDEX DROPDOWN ===== */

/* Dropdown menu z-index fix per Edge e altri browser */
.dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
}

/* Navbar dropdown specifici */
.navbar .dropdown-menu {
    z-index: 10000 !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Dropdown toggle z-index */
.dropdown-toggle {
    z-index: 1000 !important;
    position: relative !important;
}

/* Dropdown container */
.dropdown {
    z-index: 1000 !important;
    position: relative !important;
}

/* Card z-index più basso per non interferire */
.card {
    z-index: 1 !important;
    position: relative !important;
}

/* Modal z-index ancora più alto */
.modal {
    z-index: 10050 !important;
}

.modal-backdrop {
    z-index: 10040 !important;
}

/* Tooltip z-index */
.tooltip {
    z-index: 10100 !important;
}

/* Popover z-index */
.popover {
    z-index: 10060 !important;
}

/* Navbar z-index */
.navbar {
    z-index: 1030 !important;
    position: relative !important;
}

/* Sidebar z-index se presente */
.sidebar {
    z-index: 1020 !important;
}

/* Notification dropdown specifico */
.notification-bell + .dropdown-menu {
    z-index: 10001 !important;
    min-width: 300px !important;
    max-width: 400px !important;
}

/* User dropdown specifico */
.user-dropdown .dropdown-menu {
    z-index: 10002 !important;
    min-width: 200px !important;
}

/* Theme dropdown specifico */
#themeDropdown + .dropdown-menu {
    z-index: 10003 !important;
    min-width: 180px !important;
}

/* Fix per Edge - forza rendering layer */
.dropdown-menu {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    will-change: transform !important;
}

/* ===== COLORAZIONE GIALLA USER, DATA E CAMPANELLA ===== */

/* Data attuale gialla */
body.theme-dark .current-date,
body.theme-light .current-date,
body.theme-maritime .current-date {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
    font-weight: 600 !important;
}

body.theme-dark .current-date i,
body.theme-light .current-date i,
body.theme-maritime .current-date i {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
}

body.theme-dark .current-date #dateText,
body.theme-light .current-date #dateText,
body.theme-maritime .current-date #dateText {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
}

/* Campanella (notification bell) gialla */
body.theme-dark .notification-bell,
body.theme-light .notification-bell,
body.theme-maritime .notification-bell {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
    transition: all 0.3s ease !important;
}

body.theme-dark .notification-bell i,
body.theme-light .notification-bell i,
body.theme-maritime .notification-bell i {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
}

body.theme-dark .notification-bell:hover,
body.theme-light .notification-bell:hover,
body.theme-maritime .notification-bell:hover {
    color: #ffed4e !important;
    text-shadow: 0 0 12px rgba(255, 215, 0, 0.8) !important;
    transform: scale(1.1) !important;
}

/* Badge notifiche */
body.theme-dark .notification-badge,
body.theme-light .notification-badge,
body.theme-maritime .notification-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
    color: #ffffff !important;
    border: 2px solid #ffd700 !important;
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
}

/* User menu giallo */
body.theme-dark .user-dropdown .dropdown-toggle,
body.theme-light .user-dropdown .dropdown-toggle,
body.theme-maritime .user-dropdown .dropdown-toggle {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
    font-weight: 600 !important;
}

/* ===== MESSAGGI SNIP - ADATTAMENTO TEMI ===== */

/* Tema Scuro - Messaggi SNIP - COLORI MOLTO PIÙ VISIBILI */
body.theme-dark .snip-message.success {
    background: linear-gradient(135deg, #00ff88 0%, #00cc66 50%, #00aa44 100%) !important;
    color: #000000 !important;
    border: 3px solid #00ff88 !important;
    box-shadow:
        0 20px 40px rgba(0,255,136,0.6),
        0 10px 20px rgba(0,204,102,0.5),
        inset 0 1px 0 rgba(255,255,255,0.8) !important;
    font-weight: 700 !important;
}

body.theme-dark .snip-message.error {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 50%, #ff8888 100%) !important;
    color: #000000 !important;
    border: 3px solid #ff4444 !important;
    box-shadow:
        0 20px 40px rgba(255,68,68,0.6),
        0 10px 20px rgba(255,102,102,0.5),
        inset 0 1px 0 rgba(255,255,255,0.8) !important;
    font-weight: 700 !important;
}

body.theme-dark .snip-message.warning {
    background: linear-gradient(135deg, #ffdd00 0%, #ffee44 50%, #ffff88 100%) !important;
    color: #000000 !important;
    border: 3px solid #ffdd00 !important;
    box-shadow:
        0 20px 40px rgba(255,221,0,0.6),
        0 10px 20px rgba(255,238,68,0.5),
        inset 0 1px 0 rgba(255,255,255,0.8) !important;
    font-weight: 700 !important;
}

body.theme-dark .snip-message.info {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 50%, #88ccff 100%) !important;
    color: #000000 !important;
    border: 3px solid #00aaff !important;
    box-shadow:
        0 20px 40px rgba(0,170,255,0.6),
        0 10px 20px rgba(68,187,255,0.5),
        inset 0 1px 0 rgba(255,255,255,0.8) !important;
    font-weight: 700 !important;
}

/* Tema Chiaro - Messaggi SNIP */
body.theme-light .snip-message.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0,0,0,0.1) !important;
    box-shadow:
        0 20px 40px rgba(40,167,69,0.3),
        0 10px 20px rgba(32,201,151,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3) !important;
}

body.theme-light .snip-message.error {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 50%, #c0392b 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0,0,0,0.1) !important;
    box-shadow:
        0 20px 40px rgba(220,53,69,0.3),
        0 10px 20px rgba(231,76,60,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3) !important;
}

body.theme-light .snip-message.warning {
    background: linear-gradient(135deg, #ffc107 0%, #f39c12 50%, #e67e22 100%) !important;
    color: #212529 !important;
    border: 2px solid rgba(0,0,0,0.1) !important;
    box-shadow:
        0 20px 40px rgba(255,193,7,0.3),
        0 10px 20px rgba(243,156,18,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3) !important;
}

body.theme-light .snip-message.info {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 50%, #004085 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0,0,0,0.1) !important;
    box-shadow:
        0 20px 40px rgba(0,123,255,0.3),
        0 10px 20px rgba(0,86,179,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3) !important;
}

/* Testo messaggi - adattamento temi - MASSIMA VISIBILITÀ */
body.theme-dark .snip-message-title,
body.theme-dark .snip-message-body {
    color: #000000 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
    font-weight: 800 !important;
    font-size: 1.1em !important;
}

body.theme-light .snip-message-title,
body.theme-light .snip-message-body {
    text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
}

body.theme-light .snip-message.warning .snip-message-title,
body.theme-light .snip-message.warning .snip-message-body {
    color: #212529 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5) !important;
}

/* Pulsante chiusura - adattamento temi - MASSIMA VISIBILITÀ */
body.theme-dark .snip-message-close {
    background: #000000 !important;
    border: 3px solid #ffffff !important;
    color: #ffffff !important;
    font-weight: 900 !important;
    font-size: 1.2em !important;
}

body.theme-dark .snip-message-close:hover {
    background: #333333 !important;
    border-color: #ffffff !important;
    color: #ffffff !important;
    transform: scale(1.1) !important;
}

body.theme-light .snip-message-close {
    background: rgba(0,0,0,0.1) !important;
    border: 2px solid rgba(0,0,0,0.15) !important;
    color: #495057 !important;
}

body.theme-light .snip-message-close:hover {
    background: rgba(0,0,0,0.15) !important;
    border-color: rgba(0,0,0,0.2) !important;
}

/* Progress bar - adattamento temi */
body.theme-dark .snip-message-progress {
    background: linear-gradient(90deg,
        rgba(255,255,255,0.6) 0%,
        rgba(255,255,255,0.4) 50%,
        rgba(255,255,255,0.2) 100%) !important;
}

body.theme-light .snip-message-progress {
    background: linear-gradient(90deg,
        rgba(0,0,0,0.3) 0%,
        rgba(0,0,0,0.2) 50%,
        rgba(0,0,0,0.1) 100%) !important;
}

/* Pulsanti conferma messaggi SNIP - MASSIMA VISIBILITÀ */
body.theme-dark .snip-confirm-yes {
    background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%) !important;
    color: #000000 !important;
    border: 3px solid #00ff88 !important;
    font-weight: 800 !important;
    font-size: 1.1em !important;
    box-shadow: 0 5px 15px rgba(0,255,136,0.6) !important;
}

body.theme-dark .snip-confirm-yes:hover {
    background: linear-gradient(135deg, #00cc66 0%, #00aa44 100%) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 20px rgba(0,255,136,0.8) !important;
}

body.theme-dark .snip-confirm-no {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border: 3px solid #ff4444 !important;
    font-weight: 800 !important;
    font-size: 1.1em !important;
    box-shadow: 0 5px 15px rgba(255,68,68,0.6) !important;
}

body.theme-dark .snip-confirm-no:hover {
    background: linear-gradient(135deg, #ff6666 0%, #ff8888 100%) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 20px rgba(255,68,68,0.8) !important;
}

body.theme-light .snip-confirm-yes {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 5px 15px rgba(40,167,69,0.3) !important;
}

body.theme-light .snip-confirm-yes:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(40,167,69,0.4) !important;
}

body.theme-light .snip-confirm-no {
    background: linear-gradient(135deg, #dc3545 0%, #c0392b 100%) !important;
    box-shadow: 0 5px 15px rgba(220,53,69,0.3) !important;
}

body.theme-light .snip-confirm-no:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(220,53,69,0.4) !important;
}

/* ===== ALERT BOOTSTRAP - ADATTAMENTO TEMI ===== */

/* Alert Bootstrap tema scuro - MASSIMA VISIBILITÀ */
body.theme-dark .alert {
    border: 3px solid #ffffff !important;
    color: #000000 !important;
    font-weight: 700 !important;
    font-size: 1.1em !important;
}

body.theme-dark .alert-primary {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 100%) !important;
    border-color: #00aaff !important;
    color: #000000 !important;
}

body.theme-dark .alert-secondary {
    background: linear-gradient(135deg, #cccccc 0%, #eeeeee 100%) !important;
    border-color: #cccccc !important;
    color: #000000 !important;
}

body.theme-dark .alert-success {
    background: linear-gradient(135deg, #00ff88 0%, #44ff99 100%) !important;
    border-color: #00ff88 !important;
    color: #000000 !important;
}

body.theme-dark .alert-danger {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    border-color: #ff4444 !important;
    color: #000000 !important;
}

body.theme-dark .alert-warning {
    background: linear-gradient(135deg, #ffdd00 0%, #ffee44 100%) !important;
    border-color: #ffdd00 !important;
    color: #000000 !important;
}

body.theme-dark .alert-info {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 100%) !important;
    border-color: #00aaff !important;
    color: #000000 !important;
}

body.theme-dark .alert-light {
    background-color: rgba(248, 249, 250, 0.15) !important;
    border-color: #f8f9fa !important;
    color: #f0f6fc !important;
}

body.theme-dark .alert-dark {
    background-color: rgba(33, 37, 41, 0.15) !important;
    border-color: #212529 !important;
    color: #e6edf3 !important;
}

/* Alert Bootstrap tema chiaro */
body.theme-light .alert {
    border: 1px solid rgba(0, 0, 0, 0.125) !important;
    color: #212529 !important;
}

body.theme-light .alert-primary {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-color: #007bff !important;
    color: #004085 !important;
}

body.theme-light .alert-secondary {
    background-color: rgba(108, 117, 125, 0.1) !important;
    border-color: #6c757d !important;
    color: #383d41 !important;
}

body.theme-light .alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: #28a745 !important;
    color: #155724 !important;
}

body.theme-light .alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: #dc3545 !important;
    color: #721c24 !important;
}

body.theme-light .alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: #ffc107 !important;
    color: #856404 !important;
}

body.theme-light .alert-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-color: #17a2b8 !important;
    color: #0c5460 !important;
}

/* Custom alert (login/register pages) - MASSIMA VISIBILITÀ */
body.theme-dark .custom-alert {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    border: 3px solid #ff4444 !important;
    color: #000000 !important;
    border-radius: 15px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    backdrop-filter: blur(10px) !important;
    font-weight: 800 !important;
    font-size: 1.1em !important;
    box-shadow: 0 10px 30px rgba(255,68,68,0.6) !important;
}

body.theme-light .custom-alert {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%) !important;
    border: 1px solid #dc3545 !important;
    color: #721c24 !important;
    border-radius: 15px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
}

/* ===== TOAST BOOTSTRAP - ADATTAMENTO TEMI ===== */

/* Toast Bootstrap tema scuro - MASSIMA VISIBILITÀ */
body.theme-dark .toast {
    background: #ffffff !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
    backdrop-filter: blur(15px) !important;
    font-weight: 700 !important;
    font-size: 1.1em !important;
}

body.theme-dark .toast-header {
    background: #f0f0f0 !important;
    color: #000000 !important;
    border-bottom: 3px solid #000000 !important;
    font-weight: 800 !important;
}

body.theme-dark .toast.bg-success {
    background: linear-gradient(135deg, #00ff88 0%, #44ff99 100%) !important;
    color: #000000 !important;
    border-color: #00ff88 !important;
}

body.theme-dark .toast.bg-danger {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border-color: #ff4444 !important;
}

body.theme-dark .toast.bg-warning {
    background: linear-gradient(135deg, #ffdd00 0%, #ffee44 100%) !important;
    color: #000000 !important;
    border-color: #ffdd00 !important;
}

body.theme-dark .toast.bg-info {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 100%) !important;
    color: #000000 !important;
    border-color: #00aaff !important;
}

/* Toast Bootstrap tema chiaro */
body.theme-light .toast {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 1px solid rgba(0, 0, 0, 0.125) !important;
    backdrop-filter: blur(5px) !important;
}

body.theme-light .toast-header {
    background: rgba(248, 249, 250, 0.9) !important;
    color: #212529 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125) !important;
}

body.theme-light .toast.bg-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.9) 0%, rgba(32, 201, 151, 0.9) 100%) !important;
    color: #ffffff !important;
}

body.theme-light .toast.bg-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(231, 76, 60, 0.9) 100%) !important;
    color: #ffffff !important;
}

body.theme-light .toast.bg-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.9) 0%, rgba(243, 156, 18, 0.9) 100%) !important;
    color: #212529 !important;
}

body.theme-light .toast.bg-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.9) 0%, rgba(0, 123, 255, 0.9) 100%) !important;
    color: #ffffff !important;
}

body.theme-dark .user-dropdown .dropdown-toggle i,
body.theme-light .user-dropdown .dropdown-toggle i,
body.theme-maritime .user-dropdown .dropdown-toggle i {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
}

body.theme-dark .user-dropdown .dropdown-toggle:hover,
body.theme-light .user-dropdown .dropdown-toggle:hover,
body.theme-maritime .user-dropdown .dropdown-toggle:hover {
    color: #ffed4e !important;
    text-shadow: 0 0 12px rgba(255, 215, 0, 0.8) !important;
}

/* Testo user name giallo */
body.theme-dark .user-name,
body.theme-light .user-name,
body.theme-maritime .user-name {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
    font-weight: 600 !important;
}

/* Icone user gialle */
body.theme-dark .user-section-left .nav-link i,
body.theme-light .user-section-left .nav-link i,
body.theme-maritime .user-section-left .nav-link i {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5) !important;
}

/* Effetti hover per tutti gli elementi gialli */
body.theme-dark .user-section-left .nav-link:hover,
body.theme-light .user-section-left .nav-link:hover,
body.theme-maritime .user-section-left .nav-link:hover {
    color: #ffed4e !important;
    text-shadow: 0 0 12px rgba(255, 215, 0, 0.8) !important;
    transform: translateY(-1px) !important;
}

/* Animazione pulsazione per la campanella */
@keyframes bellPulse {
    0%, 100% {
        transform: scale(1);
        text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
    }
    50% {
        transform: scale(1.05);
        text-shadow: 0 0 12px rgba(255, 215, 0, 0.8);
    }
}

body.theme-dark .notification-bell.has-notifications,
body.theme-light .notification-bell.has-notifications,
body.theme-maritime .notification-bell.has-notifications {
    animation: bellPulse 2s ease-in-out infinite !important;
}

/* Effetto glow per elementi importanti */
body.theme-dark .current-date:hover,
body.theme-light .current-date:hover,
body.theme-maritime .current-date:hover {
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.8) !important;
}

/* Responsive per elementi gialli */
@media (max-width: 768px) {
    body.theme-dark .current-date,
    body.theme-light .current-date,
    body.theme-maritime .current-date {
        font-size: 0.8rem !important;
    }

    body.theme-dark .notification-bell,
    body.theme-light .notification-bell,
    body.theme-maritime .notification-bell {
        font-size: 1.1rem !important;
    }
}

/* ===== NOTIFICHE DATABASE - MASSIMA VISIBILITÀ TEMA SCURO ===== */

/* Notifiche dal sistema di notifiche (notification_system.py) */
body.theme-dark .notification-item {
    backdrop-filter: blur(15px) !important;
    border-radius: 15px !important;
    margin-bottom: 15px !important;
    padding: 20px !important;
    transition: all 0.3s ease !important;
    font-weight: 700 !important;
    font-size: 1.1em !important;
    border: 3px solid !important;
}

/* Notifiche SUCCESS */
body.theme-dark .notification-item.success,
body.theme-dark .notification-item[data-type="SUCCESS"],
body.theme-dark .notifica[data-type="SUCCESS"],
body.theme-dark .notification-card.success {
    background: linear-gradient(135deg, #00ff88 0%, #44ff99 100%) !important;
    color: #000000 !important;
    border-color: #00ff88 !important;
    box-shadow: 0 10px 30px rgba(0,255,136,0.6) !important;
}

/* Notifiche ERROR */
body.theme-dark .notification-item.error,
body.theme-dark .notification-item[data-type="ERROR"],
body.theme-dark .notifica[data-type="ERROR"],
body.theme-dark .notification-card.error {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border-color: #ff4444 !important;
    box-shadow: 0 10px 30px rgba(255,68,68,0.6) !important;
}

/* Notifiche WARNING */
body.theme-dark .notification-item.warning,
body.theme-dark .notification-item[data-type="WARNING"],
body.theme-dark .notifica[data-type="WARNING"],
body.theme-dark .notification-card.warning {
    background: linear-gradient(135deg, #ffdd00 0%, #ffee44 100%) !important;
    color: #000000 !important;
    border-color: #ffdd00 !important;
    box-shadow: 0 10px 30px rgba(255,221,0,0.6) !important;
}

/* Notifiche INFO */
body.theme-dark .notification-item.info,
body.theme-dark .notification-item[data-type="INFO"],
body.theme-dark .notifica[data-type="INFO"],
body.theme-dark .notification-card.info {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 100%) !important;
    color: #000000 !important;
    border-color: #00aaff !important;
    box-shadow: 0 10px 30px rgba(0,170,255,0.6) !important;
}

/* Titoli e testi notifiche */
body.theme-dark .notification-title,
body.theme-dark .notification-item .title,
body.theme-dark .notification-item h5,
body.theme-dark .notification-item h6,
body.theme-dark .notifica .title,
body.theme-dark .notifica h5,
body.theme-dark .notification-card h5,
body.theme-dark .notification-card .title {
    color: #000000 !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

body.theme-dark .notification-message,
body.theme-dark .notification-item .message,
body.theme-dark .notification-item p,
body.theme-dark .notifica .message,
body.theme-dark .notifica p,
body.theme-dark .notification-card p,
body.theme-dark .notification-card .message {
    color: #000000 !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5) !important;
}

/* Badge priorità notifiche */
body.theme-dark .notification-priority,
body.theme-dark .notification-badge,
body.theme-dark .notifica .badge {
    background: #000000 !important;
    color: #ffffff !important;
    border: 2px solid #ffffff !important;
    font-weight: 900 !important;
}

/* ===== MESSAGGI HTTP EXCEPTION - MASSIMA VISIBILITÀ ===== */

/* Pagine di errore HTTP */
body.theme-dark .error-container,
body.theme-dark .http-error,
body.theme-dark .error-page {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border: 3px solid #ff4444 !important;
    border-radius: 20px !important;
    padding: 30px !important;
    font-weight: 700 !important;
    box-shadow: 0 15px 40px rgba(255,68,68,0.6) !important;
}

body.theme-dark .error-title,
body.theme-dark .error-container h1,
body.theme-dark .error-container h2,
body.theme-dark .http-error h1,
body.theme-dark .error-page h1 {
    color: #000000 !important;
    font-weight: 900 !important;
    text-shadow: 0 2px 2px rgba(255,255,255,0.8) !important;
}

body.theme-dark .error-message,
body.theme-dark .error-container p,
body.theme-dark .http-error p,
body.theme-dark .error-page p {
    color: #000000 !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5) !important;
}

/* JSON error responses */
body.theme-dark .json-error {
    background: #ff4444 !important;
    color: #000000 !important;
    font-weight: 700 !important;
}

/* ===== MESSAGGI SISTEMA ADMIN - MASSIMA VISIBILITÀ ===== */

/* Messaggi dashboard admin */
body.theme-dark .admin-message,
body.theme-dark .system-message,
body.theme-dark .dashboard-alert {
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 15px !important;
    font-weight: 700 !important;
    font-size: 1.1em !important;
    border: 3px solid !important;
    backdrop-filter: blur(10px) !important;
}

/* Messaggi SUCCESS admin */
body.theme-dark .admin-message.success,
body.theme-dark .system-message.success,
body.theme-dark .dashboard-alert.success {
    background: linear-gradient(135deg, #00ff88 0%, #44ff99 100%) !important;
    color: #000000 !important;
    border-color: #00ff88 !important;
    box-shadow: 0 10px 30px rgba(0,255,136,0.6) !important;
}

/* Messaggi ERROR admin */
body.theme-dark .admin-message.error,
body.theme-dark .system-message.error,
body.theme-dark .dashboard-alert.error {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border-color: #ff4444 !important;
    box-shadow: 0 10px 30px rgba(255,68,68,0.6) !important;
}

/* Messaggi WARNING admin */
body.theme-dark .admin-message.warning,
body.theme-dark .system-message.warning,
body.theme-dark .dashboard-alert.warning {
    background: linear-gradient(135deg, #ffdd00 0%, #ffee44 100%) !important;
    color: #000000 !important;
    border-color: #ffdd00 !important;
    box-shadow: 0 10px 30px rgba(255,221,0,0.6) !important;
}

/* Messaggi INFO admin */
body.theme-dark .admin-message.info,
body.theme-dark .system-message.info,
body.theme-dark .dashboard-alert.info {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 100%) !important;
    color: #000000 !important;
    border-color: #00aaff !important;
    box-shadow: 0 10px 30px rgba(0,170,255,0.6) !important;
}

/* Testi messaggi admin */
body.theme-dark .admin-message h1,
body.theme-dark .admin-message h2,
body.theme-dark .admin-message h3,
body.theme-dark .system-message h1,
body.theme-dark .system-message h2,
body.theme-dark .dashboard-alert h1,
body.theme-dark .dashboard-alert h2 {
    color: #000000 !important;
    font-weight: 900 !important;
    text-shadow: 0 2px 2px rgba(255,255,255,0.8) !important;
}

body.theme-dark .admin-message p,
body.theme-dark .system-message p,
body.theme-dark .dashboard-alert p {
    color: #000000 !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5) !important;
}


/* ===== MESSAGGI SISTEMA ADMIN TEMA CHIARO - MASSIMA VISIBILITÀ ===== */
/* Risolve il problema dei messaggi troppo chiari nel dashboard amministrazione */

/* Messaggi dashboard admin tema chiaro - Base */
body.theme-light .admin-message,
body.theme-light .system-message,
body.theme-light .dashboard-alert {
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 15px !important;
    font-weight: 700 !important;
    font-size: 1.1em !important;
    border: 3px solid !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
}

/* Messaggi SUCCESS admin tema chiaro */
body.theme-light .admin-message.success,
body.theme-light .system-message.success,
body.theme-light .dashboard-alert.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border-color: #28a745 !important;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4) !important;
}

/* Messaggi ERROR admin tema chiaro */
body.theme-light .admin-message.error,
body.theme-light .system-message.error,
body.theme-light .dashboard-alert.error {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border-color: #dc3545 !important;
    box-shadow: 0 10px 30px rgba(220, 53, 69, 0.4) !important;
}

/* Messaggi WARNING admin tema chiaro */
body.theme-light .admin-message.warning,
body.theme-light .system-message.warning,
body.theme-light .dashboard-alert.warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    color: #212529 !important;
    border-color: #ffc107 !important;
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.4) !important;
}

/* Messaggi INFO admin tema chiaro */
body.theme-light .admin-message.info,
body.theme-light .system-message.info,
body.theme-light .dashboard-alert.info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: #ffffff !important;
    border-color: #17a2b8 !important;
    box-shadow: 0 10px 30px rgba(23, 162, 184, 0.4) !important;
}

/* Testi messaggi admin tema chiaro - Titoli */
body.theme-light .admin-message h1,
body.theme-light .admin-message h2,
body.theme-light .admin-message h3,
body.theme-light .system-message h1,
body.theme-light .system-message h2,
body.theme-light .dashboard-alert h1,
body.theme-light .dashboard-alert h2 {
    color: inherit !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    margin-bottom: 10px !important;
}

/* Testi messaggi admin tema chiaro - Paragrafi */
body.theme-light .admin-message p,
body.theme-light .system-message p,
body.theme-light .dashboard-alert p {
    color: inherit !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important;
    margin-bottom: 0 !important;
}

/* Messaggi WARNING - testo scuro per massimo contrasto */
body.theme-light .admin-message.warning h1,
body.theme-light .admin-message.warning h2,
body.theme-light .admin-message.warning h3,
body.theme-light .admin-message.warning p,
body.theme-light .system-message.warning h1,
body.theme-light .system-message.warning h2,
body.theme-light .system-message.warning p,
body.theme-light .dashboard-alert.warning h1,
body.theme-light .dashboard-alert.warning h2,
body.theme-light .dashboard-alert.warning p {
    color: #212529 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important;
}

/* Effetti hover per messaggi admin tema chiaro */
body.theme-light .admin-message:hover,
body.theme-light .system-message:hover,
body.theme-light .dashboard-alert:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2) !important;
}

/* Icone nei messaggi admin tema chiaro */
body.theme-light .admin-message i,
body.theme-light .system-message i,
body.theme-light .dashboard-alert i {
    color: inherit !important;
    margin-right: 8px !important;
}

/* Pulsanti di chiusura nei messaggi admin tema chiaro */
body.theme-light .admin-message .btn-close,
body.theme-light .system-message .btn-close,
body.theme-light .dashboard-alert .btn-close {
    filter: invert(1) !important;
    opacity: 0.8 !important;
}

body.theme-light .admin-message .btn-close:hover,
body.theme-light .system-message .btn-close:hover,
body.theme-light .dashboard-alert .btn-close:hover {
    opacity: 1 !important;
}
