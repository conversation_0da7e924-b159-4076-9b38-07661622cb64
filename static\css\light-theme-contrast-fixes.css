/* ===== FIX CONTRASTI TEMA CHIARO GLOBALI SNIP ===== */

/* ===== FIX TESTI PROBLEMATICI ===== */

/* Fix testi bianchi inline che potrebbero non essere visibili su sfondi chiari */
body.theme-light [style*="color: white"],
body.theme-light [style*="color:#fff"],
body.theme-light [style*="color: #ffffff"] {
    color: #212529 !important;
    text-shadow: none !important;
}

/* Fix classi Bootstrap problematiche */
body.theme-light .text-white {
    color: #212529 !important;
    text-shadow: none !important;
}

body.theme-light .text-light {
    color: #6c757d !important;
    font-weight: 600 !important;
}

body.theme-light .text-muted {
    color: #6c757d !important;
    font-weight: 500 !important;
}

body.theme-light .text-secondary {
    color: #6c757d !important;
    font-weight: 500 !important;
}

/* ===== FIX SFONDI PROBLEMATICI ===== */

body.theme-light .bg-light {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

body.theme-light .bg-white {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* ===== FIX FORM CONTROLS ===== */

/* Fix placeholder */
body.theme-light .form-control::placeholder,
body.theme-light .form-select::placeholder,
body.theme-light input::placeholder,
body.theme-light textarea::placeholder {
    color: #6c757d !important;
    opacity: 0.8 !important;
}

/* Fix form controls generali */
body.theme-light .form-control,
body.theme-light .form-select {
    background-color: #ffffff !important;
    border-color: #ced4da !important;
    color: #212529 !important;
}

body.theme-light .form-control:focus,
body.theme-light .form-select:focus {
    background-color: #ffffff !important;
    border-color: #86b7fe !important;
    color: #212529 !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

/* ===== FIX ELEMENTI DISABILITATI ===== */

body.theme-light .disabled,
body.theme-light :disabled,
body.theme-light .btn:disabled {
    color: #6c757d !important;
    opacity: 0.6 !important;
    background-color: rgba(108, 117, 125, 0.1) !important;
}

/* ===== FIX GRADIENTI PROBLEMATICI ===== */

/* Fix generale per tutti i gradienti */
body.theme-light .card[style*="linear-gradient"] .card-body,
body.theme-light .card[style*="linear-gradient"] .card-body *,
body.theme-light .card[style*="linear-gradient"] .card-header,
body.theme-light .card[style*="linear-gradient"] .card-header * {
    color: #212529 !important;
    text-shadow: none !important;
}

/* Fix specifici per gradienti comuni */
body.theme-light [style*="background: linear-gradient"] {
    color: #212529 !important;
}

body.theme-light [style*="background: linear-gradient"] * {
    color: #212529 !important;
    text-shadow: none !important;
}

/* ===== FIX NAVBAR ===== */

body.theme-light .navbar .nav-link {
    color: #212529 !important;
    font-weight: 500 !important;
}

body.theme-light .navbar .nav-link:hover {
    color: #0d6efd !important;
}

body.theme-light .navbar-brand {
    color: #212529 !important;
    font-weight: 600 !important;
}

/* ===== FIX DROPDOWN ===== */

body.theme-light .dropdown-menu {
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

body.theme-light .dropdown-item {
    color: #212529 !important;
}

body.theme-light .dropdown-item:hover {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* ===== FIX TABELLE ===== */

body.theme-light .table {
    color: #212529 !important;
}

body.theme-light .table thead th {
    background-color: #f8f9fa !important;
    color: #212529 !important;
    border-color: #dee2e6 !important;
}

body.theme-light .table tbody td {
    color: #212529 !important;
    border-color: #dee2e6 !important;
}

body.theme-light .table tbody tr:hover td {
    background-color: #f5f5f5 !important;
    color: #212529 !important;
}

/* ===== FIX ALERT ===== */

body.theme-light .alert {
    color: #212529 !important;
    font-weight: 500 !important;
}

body.theme-light .alert-light {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
}

body.theme-light .alert-info {
    background-color: #d1ecf1 !important;
    border-color: #bee5eb !important;
    color: #0c5460 !important;
}

body.theme-light .alert-warning {
    background-color: #fff3cd !important;
    border-color: #ffecb5 !important;
    color: #664d03 !important;
}

body.theme-light .alert-success {
    background-color: #d1e7dd !important;
    border-color: #badbcc !important;
    color: #0f5132 !important;
}

body.theme-light .alert-danger {
    background-color: #f8d7da !important;
    border-color: #f5c2c7 !important;
    color: #842029 !important;
}

/* ===== FIX BADGE ===== */

body.theme-light .badge {
    color: #212529 !important;
    font-weight: 600 !important;
}

body.theme-light .badge-light {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

/* ===== FIX PULSANTI ===== */

body.theme-light .btn-outline-light {
    color: #6c757d !important;
    border-color: #6c757d !important;
}

body.theme-light .btn-outline-light:hover {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

/* ===== FIX MODAL ===== */

body.theme-light .modal-content {
    background-color: #ffffff !important;
    color: #212529 !important;
}

body.theme-light .modal-header {
    border-color: #dee2e6 !important;
}

body.theme-light .modal-footer {
    border-color: #dee2e6 !important;
}

/* ===== FIX BREADCRUMB ===== */

body.theme-light .breadcrumb {
    background-color: #f8f9fa !important;
}

body.theme-light .breadcrumb-item {
    color: #6c757d !important;
}

body.theme-light .breadcrumb-item.active {
    color: #212529 !important;
}

/* ===== FIX PAGINATION ===== */

body.theme-light .page-link {
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #0d6efd !important;
}

body.theme-light .page-link:hover {
    background-color: #e9ecef !important;
    color: #0a58ca !important;
}

/* ===== FIX SPECIFICI PER ELEMENTI COMUNI ===== */

/* Fix per testi piccoli */
body.theme-light small,
body.theme-light .small {
    color: #6c757d !important;
    font-weight: 500 !important;
}

/* Fix per link */
body.theme-light a {
    color: #0d6efd !important;
}

body.theme-light a:hover {
    color: #0a58ca !important;
}

/* Fix per icone */
body.theme-light .fas,
body.theme-light .far,
body.theme-light .fab {
    color: inherit !important;
}

/* ===== FIX SPECIFICI INPUT GROUP ===== */

/* Fix per icone negli input-group-text */
body.theme-light .input-group-text {
    background-color: #e9ecef !important;
    border-color: #ced4da !important;
    color: #495057 !important;
}

body.theme-light .input-group-text i {
    color: #495057 !important;
    font-size: 1.1em !important;
}

/* Fix specifico per icona lente di ricerca */
body.theme-light .input-group-text .fa-search {
    color: #0d6efd !important;
    font-weight: 600 !important;
}

/* Fix per input group nel tema chiaro */
body.theme-light .input-group .form-control {
    background-color: #ffffff !important;
    border-color: #ced4da !important;
    color: #212529 !important;
}

body.theme-light .input-group .btn {
    border-color: #ced4da !important;
}

/* ===== FIX SPECIFICI PER COMPONENTI SNIP ===== */

/* Fix per avatar circle */
body.theme-light .avatar-circle {
    background-color: #0d6efd !important;
    color: #ffffff !important;
    border: 2px solid #dee2e6 !important;
}

/* Fix per status indicator */
body.theme-light .status-indicator {
    border: 1px solid #dee2e6 !important;
}
