/* 
 * 🚢 SNIP - Login Page Modern Styles
 * Design moderno e professionale per la pagina di accesso
 */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --shadow-soft: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-hover: 0 15px 35px rgba(31, 38, 135, 0.5);
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset e base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.login-page {
    font-family: 'Inter', 'Poppins', sans-serif;
    background: 
        linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 50%, rgba(79, 172, 254, 0.9) 100%),
        url('/static/images/cargo-ship.jpg') center/cover no-repeat fixed;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Effetti di sfondo animati */
body.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 120%;
    height: 120%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: 1;
}

body.login-page::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat bottom;
    background-size: cover;
    animation: wave 8s ease-in-out infinite;
    z-index: 2;
}

@keyframes float {
    0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

@keyframes wave {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(-50px); }
}

/* Container principale glassmorphism */
.login-container-modern {
    position: relative;
    z-index: 10;
    max-width: 480px;
    width: 90%;
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border-radius: var(--border-radius-lg);
    box-shadow: 
        var(--shadow-soft),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 0 1px var(--glass-border);
    padding: 50px 40px;
    animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--glass-border);
    transition: var(--transition);
}

.login-container-modern:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* Header con logo */
.header-section-modern {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container-modern {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-radius: var(--border-radius-lg);
    margin-bottom: 24px;
    box-shadow: 
        0 8px 32px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid var(--glass-border);
    animation: logoFloat 3s ease-in-out infinite;
    transition: var(--transition);
}

.logo-container-modern:hover {
    transform: scale(1.05);
    box-shadow: 
        0 12px 40px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.logo-image-modern {
    width: 60px;
    height: 60px;
    object-fit: contain;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.1));
}

.logo-icon-modern {
    font-size: 2.8rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(2deg); }
}

.app-title-modern {
    font-size: 2.2rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    letter-spacing: -1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-subtitle-modern {
    font-size: 1rem;
    color: var(--text-muted);
    font-weight: 500;
    margin-bottom: 0;
    opacity: 0.9;
}

/* Particelle animate */
.floating-particles-modern {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 3;
    pointer-events: none;
}

.particle-modern {
    position: absolute;
    border-radius: 50%;
    animation: particleFloat 20s infinite linear;
    backdrop-filter: blur(1px);
}

@keyframes particleFloat {
    0% { 
        transform: translateY(100vh) rotate(0deg) scale(0); 
        opacity: 0; 
    }
    10% { 
        opacity: 1; 
        transform: translateY(90vh) rotate(45deg) scale(1); 
    }
    90% { 
        opacity: 1; 
        transform: translateY(10vh) rotate(315deg) scale(1); 
    }
    100% { 
        transform: translateY(-10vh) rotate(360deg) scale(0); 
        opacity: 0; 
    }
}

/* Responsive */
@media (max-width: 768px) {
    .login-container-modern {
        max-width: 420px;
        padding: 40px 30px;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    body.login-page {
        padding: 10px;
    }

    .login-container-modern {
        margin: 10px;
        padding: 35px 25px;
        max-width: 100%;
    }

    .floating-particles-modern {
        display: none;
    }
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-particles-modern {
        display: none;
    }
}
