/* ===== DROPDOWN MODERNI SNIP - STILE UNIFICATO ===== */

/* ===== PULSANTI DROPDOWN NAVBAR CON TEMI ===== */
.navbar .nav-link.dropdown-toggle {
    /* Base styling */
    padding: 8px 16px !important;
    border-radius: 25px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    text-decoration: none !important;
    margin: 0 4px !important;
}

/* <PERSON><PERSON>/
body.theme-maritime .navbar .nav-link.dropdown-toggle,
body:not([class*="theme-"]) .navbar .nav-link.dropdown-toggle {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

body.theme-maritime .navbar .nav-link.dropdown-toggle:hover,
body:not([class*="theme-"]) .navbar .nav-link.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

/* <PERSON><PERSON> */
body.theme-dark .navbar .nav-link.dropdown-toggle {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ecf0f1 !important;
}

body.theme-dark .navbar .nav-link.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

/* Tema Chiaro */
body.theme-light .navbar .nav-link.dropdown-toggle {
    background: rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    color: #343a40 !important;
    font-weight: 500 !important;
}

body.theme-light .navbar .nav-link.dropdown-toggle:hover {
    background: rgba(0, 0, 0, 0.12) !important;
    border-color: rgba(0, 0, 0, 0.25) !important;
    color: #212529 !important;
}

.navbar .nav-link.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.navbar .nav-link.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
    color: white !important;
}

/* ===== DROPDOWN MENU MODERNI CON TEMI ===== */
.navbar .dropdown-menu {
    /* Base styling - sarà sovrascritto dai temi */
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 20px !important;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    margin-top: 12px !important;
    padding: 16px 8px !important;
    min-width: 280px !important;
    max-width: 320px !important;
    animation: dropdownSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    transform-origin: top center !important;

    /* ELIMINA BARRE DI SCORRIMENTO */
    overflow: visible !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;
    max-height: none !important;
    height: auto !important;

    /* POSIZIONAMENTO */
    position: absolute !important;
    z-index: 9999 !important;
}

/* ===== DROPDOWN TEMA MARITTIMO ===== */
body.theme-maritime .navbar .dropdown-menu,
body:not([class*="theme-"]) .navbar .dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(30, 60, 114, 0.3) !important;
    box-shadow:
        0 20px 60px rgba(30, 60, 114, 0.2),
        0 8px 25px rgba(30, 60, 114, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* ===== DROPDOWN TEMA SCURO ===== */
body.theme-dark .navbar .dropdown-menu {
    background: rgba(52, 73, 94, 0.98) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* ===== DROPDOWN TEMA CHIARO ===== */
body.theme-light .navbar .dropdown-menu {
    background: rgba(255, 255, 255, 0.99) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 1) !important;
}

@keyframes dropdownSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== DROPDOWN ITEMS MODERNI CON TEMI ===== */
.navbar .dropdown-menu .dropdown-item {
    /* Base styling */
    padding: 12px 20px !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    border-radius: 12px !important;
    margin: 4px 8px !important;
    position: relative !important;
    overflow: hidden !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    border: 1px solid transparent !important;
    text-decoration: none !important;
}

/* ===== DROPDOWN ITEMS TEMA MARITTIMO ===== */
body.theme-maritime .navbar .dropdown-menu .dropdown-item,
body:not([class*="theme-"]) .navbar .dropdown-menu .dropdown-item {
    color: #2c3e50 !important;
}

body.theme-maritime .navbar .dropdown-menu .dropdown-item:hover,
body:not([class*="theme-"]) .navbar .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: white !important;
}

/* ===== DROPDOWN ITEMS TEMA SCURO ===== */
body.theme-dark .navbar .dropdown-menu .dropdown-item {
    color: #ecf0f1 !important;
}

body.theme-dark .navbar .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
    color: white !important;
}

/* ===== DROPDOWN ITEMS TEMA CHIARO ===== */
body.theme-light .navbar .dropdown-menu .dropdown-item {
    color: #343a40 !important;
    font-weight: 500 !important;
}

body.theme-light .navbar .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
    color: #212529 !important;
}

.navbar .dropdown-menu .dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.navbar .dropdown-menu .dropdown-item:hover::before {
    left: 100%;
}

.navbar .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateX(8px) translateY(-2px) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: 
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.navbar .dropdown-menu .dropdown-item i {
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    background: rgba(102, 126, 234, 0.15) !important;
    color: #5a67d8 !important; /* Colore di default più scuro */
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0 !important;
}

.navbar .dropdown-menu .dropdown-item:hover i {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) rotate(5deg) !important;
    color: white !important;
}

/* ===== DIVIDER ELEGANTI ===== */
.navbar .dropdown-menu .dropdown-divider {
    height: 1px !important;
    margin: 12px 16px !important;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent) !important;
    border: none !important;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1200px) {
    .navbar .dropdown-menu {
        min-width: 260px !important;
        max-width: 300px !important;
    }
}

@media (max-width: 991px) {
    .navbar .dropdown-menu {
        min-width: 240px !important;
        max-width: 280px !important;
    }
}

@media (max-width: 768px) {
    .navbar .dropdown-menu {
        min-width: 220px !important;
        max-width: 260px !important;
        margin-top: 8px !important;
        border-radius: 16px !important;
        padding: 12px 6px !important;
    }
    
    .navbar .dropdown-menu .dropdown-item {
        padding: 10px 16px !important;
        font-size: 14px !important;
        gap: 10px !important;
    }
    
    .navbar .dropdown-menu .dropdown-item i {
        width: 20px !important;
        height: 20px !important;
        font-size: 14px !important;
    }
    
    .navbar .nav-link.dropdown-toggle {
        padding: 6px 12px !important;
        font-size: 14px !important;
    }
}

@media (max-width: 576px) {
    .navbar .dropdown-menu {
        min-width: 200px !important;
        max-width: 240px !important;
    }
    
    .navbar .nav-link.dropdown-toggle {
        padding: 6px 10px !important;
        font-size: 13px !important;
        margin: 0 2px !important;
    }
}

/* ===== MENU SPECIFICI CON CONTRASTO OTTIMALE ===== */

/* Menu NAVI - Colori più scuri per contrasto */
#naviDropdown + .dropdown-menu .dropdown-item[href*="viaggi"] i {
    background: rgba(52, 152, 219, 0.15) !important;
    color: #2980b9 !important; /* Blu più scuro */
}

#naviDropdown + .dropdown-menu .dropdown-item[href*="navi"] i {
    background: rgba(46, 204, 113, 0.15) !important;
    color: #27ae60 !important; /* Verde più scuro */
}

#naviDropdown + .dropdown-menu .dropdown-item[href*="armatori"] i {
    background: rgba(155, 89, 182, 0.15) !important;
    color: #8e44ad !important; /* Viola più scuro */
}

#naviDropdown + .dropdown-menu .dropdown-item[href*="porti"] i {
    background: rgba(230, 126, 34, 0.15) !important;
    color: #d35400 !important; /* Arancione più scuro */
}

/* Menu SOF - Colori più scuri */
#sofDropdown + .dropdown-menu .dropdown-item[href*="da-realizzare"] i {
    background: rgba(241, 196, 15, 0.15) !important;
    color: #f39c12 !important; /* Giallo più scuro */
}

#sofDropdown + .dropdown-menu .dropdown-item[href*="realizzati"] i {
    background: rgba(39, 174, 96, 0.15) !important;
    color: #27ae60 !important; /* Verde scuro */
}

#sofDropdown + .dropdown-menu .dropdown-item[href*="archiviati"] i {
    background: rgba(149, 165, 166, 0.15) !important;
    color: #7f8c8d !important; /* Grigio scuro per archivio */
}

/* Menu Amministrazione - Rosso scuro */
#adminDropdown + .dropdown-menu .dropdown-item i {
    background: rgba(231, 76, 60, 0.15) !important;
    color: #c0392b !important; /* Rosso più scuro */
}

/* Menu Shortsea - Blu scuro */
#shortseaDropdown + .dropdown-menu .dropdown-item i {
    background: rgba(52, 152, 219, 0.15) !important;
    color: #2980b9 !important; /* Blu più scuro */
}

/* Menu Contabilità - Verde scuro */
#contabilitaDropdown + .dropdown-menu .dropdown-item i {
    background: rgba(46, 204, 113, 0.15) !important;
    color: #27ae60 !important; /* Verde più scuro */
}

/* ===== HOVER EFFECTS SPECIFICI ===== */
.navbar .dropdown-menu .dropdown-item:hover i {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}
