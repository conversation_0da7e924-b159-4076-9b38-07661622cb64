/* ===== FIX CONTRASTI SOF ARCHIVIATI - TUTTI I TEMI ===== */

/* ===== MODAL SUCCESS/ERROR - TEMA SCURO ===== */
body.theme-dark #successModal .modal-content,
body.theme-dark #errorModal .modal-content {
    background: rgba(52, 73, 94, 0.98) !important;
    color: #ecf0f1 !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark #successModal .modal-header,
body.theme-dark #errorModal .modal-header {
    background: rgba(44, 62, 80, 0.9) !important;
    color: #ecf0f1 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.theme-dark #successModal .modal-header.bg-success {
    background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%) !important;
    color: #000000 !important;
    border-bottom: 2px solid #000000 !important;
}

body.theme-dark #errorModal .modal-header.bg-danger {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border-bottom: 2px solid #000000 !important;
}

body.theme-dark #successModal .modal-title,
body.theme-dark #errorModal .modal-title {
    color: #000000 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

body.theme-dark #successModal .modal-body,
body.theme-dark #errorModal .modal-body {
    background: rgba(52, 73, 94, 0.95) !important;
    color: #ecf0f1 !important;
}

body.theme-dark #successModal .modal-body pre,
body.theme-dark #errorModal .modal-body pre {
    background: rgba(44, 62, 80, 0.8) !important;
    color: #ecf0f1 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-weight: 600 !important;
    font-size: 1.1em !important;
}

/* ===== MODAL SUCCESS/ERROR - TEMA CHIARO ===== */
body.theme-light #successModal .modal-content,
body.theme-light #errorModal .modal-content {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 2px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light #successModal .modal-header,
body.theme-light #errorModal .modal-header {
    background: rgba(248, 249, 250, 0.95) !important;
    color: #212529 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light #successModal .modal-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border-bottom: 2px solid rgba(0, 0, 0, 0.2) !important;
}

body.theme-light #errorModal .modal-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border-bottom: 2px solid rgba(0, 0, 0, 0.2) !important;
}

body.theme-light #successModal .modal-title,
body.theme-light #errorModal .modal-title {
    color: inherit !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

body.theme-light #successModal .modal-body,
body.theme-light #errorModal .modal-body {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
}

body.theme-light #successModal .modal-body pre,
body.theme-light #errorModal .modal-body pre {
    background: rgba(248, 249, 250, 0.9) !important;
    color: #212529 !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    font-weight: 600 !important;
    font-size: 1.1em !important;
}

/* ===== MODAL SUCCESS/ERROR - TEMA MARITTIMO ===== */
body.theme-maritime #successModal .modal-content,
body.theme-maritime #errorModal .modal-content {
    background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.98) 100%) !important;
    color: #212529 !important;
    border: 3px solid #ffd700 !important;
    backdrop-filter: blur(10px) !important;
}

body.theme-maritime #successModal .modal-header,
body.theme-maritime #errorModal .modal-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
}

body.theme-maritime #successModal .modal-header.bg-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
}

body.theme-maritime #errorModal .modal-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
}

body.theme-maritime #successModal .modal-title,
body.theme-maritime #errorModal .modal-title {
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

body.theme-maritime #successModal .modal-title i,
body.theme-maritime #errorModal .modal-title i {
    color: #ffd700 !important;
    text-shadow: 0 0 10px rgba(255,215,0,0.6) !important;
}

body.theme-maritime #successModal .modal-body,
body.theme-maritime #errorModal .modal-body {
    background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.98) 100%) !important;
    color: #212529 !important;
}

body.theme-maritime #successModal .modal-body pre,
body.theme-maritime #errorModal .modal-body pre {
    background: rgba(30, 60, 114, 0.1) !important;
    color: #1e3c72 !important;
    border: 2px solid rgba(30, 60, 114, 0.2) !important;
    font-weight: 700 !important;
    font-size: 1.1em !important;
    border-radius: 8px !important;
    padding: 1rem !important;
}

/* ===== PULSANTI MODAL - TUTTI I TEMI ===== */

/* Tema Scuro */
body.theme-dark #successModal .btn-success,
body.theme-dark #errorModal .btn-danger {
    background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%) !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

body.theme-dark #errorModal .btn-danger {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
}

/* Tema Chiaro */
body.theme-light #successModal .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    font-weight: 600 !important;
}

body.theme-light #errorModal .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    font-weight: 600 !important;
}

/* Tema Marittimo */
body.theme-maritime #successModal .btn-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border: 2px solid #1e3c72 !important;
    font-weight: 700 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

body.theme-maritime #errorModal .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border: 2px solid #1e3c72 !important;
    font-weight: 700 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* ===== PROGRESS MODAL - TUTTI I TEMI ===== */

/* Tema Scuro */
body.theme-dark #progressModal .modal-content {
    background: rgba(52, 73, 94, 0.98) !important;
    color: #ecf0f1 !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark #progressModal .modal-header {
    background: rgba(44, 62, 80, 0.9) !important;
    color: #ecf0f1 !important;
}

body.theme-dark #progressModal .modal-body {
    color: #ecf0f1 !important;
}

body.theme-dark #progressModal .progress-bar {
    background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%) !important;
}

/* Tema Chiaro */
body.theme-light #progressModal .modal-content {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 2px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light #progressModal .modal-header {
    background: rgba(248, 249, 250, 0.95) !important;
    color: #212529 !important;
}

body.theme-light #progressModal .modal-body {
    color: #212529 !important;
}

/* Tema Marittimo */
body.theme-maritime #progressModal .modal-content {
    background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.98) 100%) !important;
    color: #212529 !important;
    border: 3px solid #ffd700 !important;
}

body.theme-maritime #progressModal .modal-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
}

body.theme-maritime #progressModal .modal-title {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

body.theme-maritime #progressModal .modal-body {
    color: #212529 !important;
}

body.theme-maritime #progressModal .progress-bar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
}

/* ===== ALERT DINAMICI - TUTTI I TEMI ===== */

/* Container alert dinamici */
#alertContainer .alert {
    font-weight: 600 !important;
    border-radius: 15px !important;
    padding: 1rem 1.5rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Tema Scuro */
body.theme-dark #alertContainer .alert-success {
    background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%) !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

body.theme-dark #alertContainer .alert-warning {
    background: linear-gradient(135deg, #ffdd00 0%, #ffee44 100%) !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

body.theme-dark #alertContainer .alert-danger {
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 100%) !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

body.theme-dark #alertContainer .alert-info {
    background: linear-gradient(135deg, #00aaff 0%, #44bbff 100%) !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

/* Tema Chiaro */
body.theme-light #alertContainer .alert-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

body.theme-light #alertContainer .alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    color: #212529 !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important;
}

body.theme-light #alertContainer .alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

body.theme-light #alertContainer .alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Tema Marittimo */
body.theme-maritime #alertContainer .alert-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border: 2px solid #1e3c72 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

body.theme-maritime #alertContainer .alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    color: #212529 !important;
    border: 2px solid #1e3c72 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important;
}

body.theme-maritime #alertContainer .alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
    border: 2px solid #1e3c72 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

body.theme-maritime #alertContainer .alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: #ffffff !important;
    border: 2px solid #1e3c72 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* ===== ICONE NEGLI ALERT E MODAL ===== */

/* Tema Scuro - Icone nere per contrasto */
body.theme-dark #alertContainer .alert i,
body.theme-dark #successModal i,
body.theme-dark #errorModal i {
    color: #000000 !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8) !important;
}

/* Tema Chiaro - Icone ereditate */
body.theme-light #alertContainer .alert i,
body.theme-light #successModal i,
body.theme-light #errorModal i {
    color: inherit !important;
    font-weight: 700 !important;
}

/* Tema Marittimo - Icone oro per header, ereditate per body */
body.theme-maritime #successModal .modal-header i,
body.theme-maritime #errorModal .modal-header i {
    color: #ffd700 !important;
    text-shadow: 0 0 10px rgba(255,215,0,0.6) !important;
}

body.theme-maritime #alertContainer .alert i,
body.theme-maritime #successModal .modal-body i,
body.theme-maritime #errorModal .modal-body i {
    color: inherit !important;
    font-weight: 700 !important;
}

/* ===== PULSANTI CHIUSURA MODAL ===== */

/* Tema Scuro */
body.theme-dark #successModal .btn-close,
body.theme-dark #errorModal .btn-close,
body.theme-dark #progressModal .btn-close {
    filter: invert(1) brightness(1.2) !important;
    opacity: 0.9 !important;
}

/* Tema Chiaro */
body.theme-light #successModal .btn-close,
body.theme-light #errorModal .btn-close,
body.theme-light #progressModal .btn-close {
    filter: none !important;
    opacity: 0.8 !important;
}

/* Tema Marittimo */
body.theme-maritime #successModal .btn-close,
body.theme-maritime #errorModal .btn-close,
body.theme-maritime #progressModal .btn-close {
    filter: invert(1) brightness(1.2) !important;
    opacity: 0.9 !important;
}

body.theme-maritime #successModal .btn-close:hover,
body.theme-maritime #errorModal .btn-close:hover,
body.theme-maritime #progressModal .btn-close:hover {
    opacity: 1 !important;
    transform: scale(1.1) !important;
    filter: invert(1) brightness(1.5) drop-shadow(0 0 5px rgba(255,215,0,0.8)) !important;
}

/* ===== OVERRIDE GLOBALE PER MODAL JAVASCRIPT DINAMICI ===== */

/* Qualsiasi modal creato dinamicamente */
.modal.fade.show .modal-content {
    border-radius: 15px !important;
    overflow: hidden !important;
}

/* Tema Scuro - Modal dinamici */
body.theme-dark .modal.fade.show .modal-content {
    background: rgba(52, 73, 94, 0.98) !important;
    color: #ecf0f1 !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .modal.fade.show .modal-header {
    color: #ecf0f1 !important;
}

body.theme-dark .modal.fade.show .modal-body {
    color: #ecf0f1 !important;
}

body.theme-dark .modal.fade.show .modal-body pre {
    background: rgba(44, 62, 80, 0.8) !important;
    color: #ecf0f1 !important;
    font-weight: 600 !important;
}

/* Tema Chiaro - Modal dinamici */
body.theme-light .modal.fade.show .modal-content {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 2px solid rgba(0, 0, 0, 0.15) !important;
}

body.theme-light .modal.fade.show .modal-header {
    color: #212529 !important;
}

body.theme-light .modal.fade.show .modal-body {
    color: #212529 !important;
}

body.theme-light .modal.fade.show .modal-body pre {
    background: rgba(248, 249, 250, 0.9) !important;
    color: #212529 !important;
    font-weight: 600 !important;
}

/* Tema Marittimo - Modal dinamici */
body.theme-maritime .modal.fade.show .modal-content {
    background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.98) 100%) !important;
    color: #212529 !important;
    border: 3px solid #ffd700 !important;
}

body.theme-maritime .modal.fade.show .modal-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
}

body.theme-maritime .modal.fade.show .modal-title {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

body.theme-maritime .modal.fade.show .modal-body {
    color: #212529 !important;
}

body.theme-maritime .modal.fade.show .modal-body pre {
    background: rgba(30, 60, 114, 0.1) !important;
    color: #1e3c72 !important;
    border: 2px solid rgba(30, 60, 114, 0.2) !important;
    font-weight: 700 !important;
}

/* ===== Z-INDEX FIXES ===== */

/* Assicura che i modal siano sempre sopra */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
}

/* Alert container sopra tutto tranne i modal */
#alertContainer {
    z-index: 1040 !important;
}

/* ===== RESPONSIVE FIXES ===== */

@media (max-width: 768px) {
    .modal-dialog {
        margin: 1rem !important;
    }
    
    .modal-body pre {
        font-size: 0.8rem !important;
        padding: 0.75rem !important;
    }
    
    #alertContainer .alert {
        padding: 0.75rem 1rem !important;
        font-size: 0.9rem !important;
    }
}

/* ===== ANIMAZIONI SMOOTH ===== */

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out !important;
}

.modal.fade.show .modal-dialog {
    transform: none !important;
}

#alertContainer .alert {
    animation: slideInRight 0.5s ease-out !important;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== ACCESSIBILITÀ ===== */

.modal-header .modal-title {
    font-size: 1.25rem !important;
    line-height: 1.4 !important;
}

.modal-body pre {
    max-height: 300px !important;
    overflow-y: auto !important;
    scrollbar-width: thin !important;
}

.modal-body pre::-webkit-scrollbar {
    width: 6px !important;
}

.modal-body pre::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1) !important;
}

.modal-body pre::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 3px !important;
}

/* ===== FOCUS STATES ===== */

.modal .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    outline: none !important;
}

body.theme-maritime .modal .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

/* ===== PRINT STYLES ===== */

@media print {
    .modal,
    .modal-backdrop,
    #alertContainer {
        display: none !important;
    }
}
