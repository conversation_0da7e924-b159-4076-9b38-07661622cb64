/* ===== FIX ANTEPRIMA SOF PER TUTTI I TEMI ===== */

/* Stile base per l'anteprima SOF */
.sof-preview-container {
    font-family: 'Courier New', monospace !important;
    font-size: 0.85em !important;
    line-height: 1.4 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* ===== TEMA CHIARO (default) ===== */
body:not([class*="theme-"]) .sof-preview-container,
body.theme-light .sof-preview-container {
    background-color: #f8f9fa !important;
    color: #212529 !important;
    border: 1px solid #dee2e6 !important;
}

/* ===== TEMA MARITTIMO ===== */
body.theme-maritime .sof-preview-container {
    background-color: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 3px solid rgba(255, 215, 0, 0.8) !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(5px) !important;
}

/* Override specifico per tema marittimo con priorità massima */
body.theme-maritime #sof_preview_container .sof-preview-container {
    background-color: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
    border: 3px solid rgba(255, 215, 0, 0.8) !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
}

/* ===== TEMA SCURO ===== */
body.theme-dark .sof-preview-container {
    background-color: rgba(52, 73, 94, 0.95) !important;
    color: #ecf0f1 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* ===== FIX TESTO MUTED NELL'ANTEPRIMA ===== */

/* Tema chiaro */
body:not([class*="theme-"]) .sof-preview-container .text-muted,
body.theme-light .sof-preview-container .text-muted {
    color: #6c757d !important;
}

/* Tema marittimo */
body.theme-maritime .sof-preview-container .text-muted {
    color: #495057 !important;
    font-weight: 600 !important;
}

/* Tema scuro */
body.theme-dark .sof-preview-container .text-muted {
    color: #95a5a6 !important;
}

/* ===== FIX SCROLLBAR ANTEPRIMA ===== */

.sof-preview-container::-webkit-scrollbar {
    width: 8px;
}

.sof-preview-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.sof-preview-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.sof-preview-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* ===== FIX CARD ANTEPRIMA SOF ===== */

/* Tema marittimo - Card anteprima SOF */
body.theme-maritime .card.sof-preview-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
    border: 2px solid rgba(255, 215, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
}

body.theme-maritime .card.sof-preview-card .card-header {
    background: rgba(255, 215, 0, 0.2) !important;
    border-bottom: 1px solid rgba(255, 215, 0, 0.4) !important;
    color: #ffffff !important;
}

body.theme-maritime .card.sof-preview-card .card-body {
    background: rgba(255, 255, 255, 0.05) !important;
}

/* ===== FIX MESSAGGIO PLACEHOLDER ===== */

/* Messaggio quando non c'è anteprima */
.sof-preview-placeholder {
    text-align: center !important;
    padding: 2rem !important;
}

/* Tema chiaro */
body:not([class*="theme-"]) .sof-preview-placeholder,
body.theme-light .sof-preview-placeholder {
    color: #6c757d !important;
}

/* Tema marittimo */
body.theme-maritime .sof-preview-placeholder {
    color: rgba(255, 255, 255, 0.8) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Tema scuro */
body.theme-dark .sof-preview-placeholder {
    color: #95a5a6 !important;
}

/* ===== OVERRIDE FORZATO PER TEMA MARITTIMO ===== */

/* Selettore ultra-specifico per tema marittimo */
body.theme-maritime div#sof_preview_container div.sof-preview-container,
body.theme-maritime .card-body div.sof-preview-container,
body.theme-maritime [id="sof_preview_container"] .sof-preview-container {
    background-color: #ffffff !important;
    background: #ffffff !important;
    color: #212529 !important;
    border: 3px solid #ffd700 !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(5px) !important;
}

/* Override per tutti i div figli nel tema marittimo */
body.theme-maritime .sof-preview-container div,
body.theme-maritime .sof-preview-container span,
body.theme-maritime .sof-preview-container p {
    color: #212529 !important;
    background: transparent !important;
}

/* Override per testo muted nel tema marittimo */
body.theme-maritime .sof-preview-container .text-muted {
    color: #6c757d !important;
    background: transparent !important;
}

/* ===== RESPONSIVE ===== */

@media (max-width: 768px) {
    .sof-preview-container {
        font-size: 0.75em !important;
        max-height: 300px !important;
    }
}
