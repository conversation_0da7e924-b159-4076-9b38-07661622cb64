/* ===== TEMA BIANCO PROFESSIONALE ===== */

/* ===== PALETTE COLORI PROFESSIONALE ===== */
:root {
    --light-primary: #2c3e50;
    --light-secondary: #34495e;
    --light-accent: #3498db;
    --light-success: #27ae60;
    --light-warning: #f39c12;
    --light-danger: #e74c3c;
    --light-info: #16a085;
    --light-bg-primary: #ffffff;
    --light-bg-secondary: #f8f9fa;
    --light-bg-accent: #ecf0f1;
    --light-border: #dee2e6;
    --light-shadow: rgba(44, 62, 80, 0.1);
    --light-shadow-hover: rgba(44, 62, 80, 0.15);
}

/* ===== BACKGROUND PROFESSIONALE ===== */
body.theme-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #ecf0f1 100%) !important;
    min-height: 100vh;
    position: relative;
}

body.theme-light::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(52, 152, 219, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(46, 204, 113, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(155, 89, 182, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* ===== NAVBAR PROFESSIONALE ===== */
body.theme-light .snip-navbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-bottom: 2px solid var(--light-border) !important;
    box-shadow: 0 4px 20px var(--light-shadow) !important;
    backdrop-filter: blur(10px) !important;
}

body.theme-light .navbar-brand {
    color: var(--light-primary) !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

body.theme-light .navbar .nav-link {
    color: var(--light-secondary) !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

body.theme-light .navbar .nav-link:hover {
    color: var(--light-accent) !important;
    transform: translateY(-1px) !important;
}

/* ===== CARD PROFESSIONALI ===== */
body.theme-light .card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 1px solid var(--light-border) !important;
    border-radius: 16px !important;
    box-shadow: 
        0 8px 32px var(--light-shadow),
        0 2px 8px rgba(0,0,0,0.04) !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    overflow: hidden !important;
    position: relative !important;
}

body.theme-light .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--light-accent), var(--light-success), var(--light-info)) !important;
}

body.theme-light .card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 
        0 16px 48px var(--light-shadow-hover),
        0 4px 16px rgba(0,0,0,0.08) !important;
}

body.theme-light .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-bottom: 1px solid var(--light-border) !important;
    padding: 20px !important;
    position: relative !important;
}

body.theme-light .card-header h5 {
    color: var(--light-primary) !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
}

body.theme-light .card-header small {
    color: var(--light-secondary) !important;
    font-weight: 500 !important;
}

body.theme-light .card-body {
    padding: 24px !important;
    background: rgba(255, 255, 255, 0.8) !important;
}

/* ===== PULSANTI PROFESSIONALI ===== */
body.theme-light .btn {
    border-radius: 12px !important;
    font-weight: 500 !important;
    padding: 12px 24px !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-light .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

body.theme-light .btn:hover::before {
    left: 100%;
}

body.theme-light .btn-primary {
    background: linear-gradient(135deg, var(--light-primary) 0%, var(--light-secondary) 100%) !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(44, 62, 80, 0.3) !important;
}

body.theme-light .btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(44, 62, 80, 0.4) !important;
}

body.theme-light .btn-success {
    background: linear-gradient(135deg, var(--light-success) 0%, #2ecc71 100%) !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(39, 174, 96, 0.3) !important;
}

body.theme-light .btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(39, 174, 96, 0.4) !important;
}

body.theme-light .btn-warning {
    background: linear-gradient(135deg, var(--light-warning) 0%, #e67e22 100%) !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(243, 156, 18, 0.3) !important;
    color: white !important;
}

body.theme-light .btn-warning:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(243, 156, 18, 0.4) !important;
    color: white !important;
}

body.theme-light .btn-danger {
    background: linear-gradient(135deg, var(--light-danger) 0%, #c0392b 100%) !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.3) !important;
}

body.theme-light .btn-danger:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(231, 76, 60, 0.4) !important;
}

body.theme-light .btn-info {
    background: linear-gradient(135deg, var(--light-info) 0%, #1abc9c 100%) !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(22, 160, 133, 0.3) !important;
}

body.theme-light .btn-info:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(22, 160, 133, 0.4) !important;
}

/* ===== PULSANTI OUTLINE PROFESSIONALI ===== */
body.theme-light .btn-outline-primary {
    border: 2px solid var(--light-primary) !important;
    color: var(--light-primary) !important;
    background: rgba(44, 62, 80, 0.05) !important;
}

body.theme-light .btn-outline-primary:hover {
    background: var(--light-primary) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(44, 62, 80, 0.3) !important;
}

body.theme-light .btn-outline-success {
    border: 2px solid var(--light-success) !important;
    color: var(--light-success) !important;
    background: rgba(39, 174, 96, 0.05) !important;
}

body.theme-light .btn-outline-success:hover {
    background: var(--light-success) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(39, 174, 96, 0.3) !important;
}

body.theme-light .btn-outline-warning {
    border: 2px solid var(--light-warning) !important;
    color: var(--light-warning) !important;
    background: rgba(243, 156, 18, 0.05) !important;
}

body.theme-light .btn-outline-warning:hover {
    background: var(--light-warning) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(243, 156, 18, 0.3) !important;
}

body.theme-light .btn-outline-danger {
    border: 2px solid var(--light-danger) !important;
    color: var(--light-danger) !important;
    background: rgba(231, 76, 60, 0.05) !important;
}

body.theme-light .btn-outline-danger:hover {
    background: var(--light-danger) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(231, 76, 60, 0.3) !important;
}

body.theme-light .btn-outline-info {
    border: 2px solid var(--light-info) !important;
    color: var(--light-info) !important;
    background: rgba(22, 160, 133, 0.05) !important;
}

body.theme-light .btn-outline-info:hover {
    background: var(--light-info) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(22, 160, 133, 0.3) !important;
}

/* ===== BADGE PROFESSIONALI ===== */
body.theme-light .badge {
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 6px 12px !important;
    font-size: 0.75rem !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

body.theme-light .badge.bg-primary {
    background: linear-gradient(135deg, var(--light-primary), var(--light-secondary)) !important;
}

body.theme-light .badge.bg-success {
    background: linear-gradient(135deg, var(--light-success), #2ecc71) !important;
}

body.theme-light .badge.bg-warning {
    background: linear-gradient(135deg, var(--light-warning), #e67e22) !important;
    color: white !important;
}

body.theme-light .badge.bg-danger {
    background: linear-gradient(135deg, var(--light-danger), #c0392b) !important;
}

body.theme-light .badge.bg-info {
    background: linear-gradient(135deg, var(--light-info), #1abc9c) !important;
}

/* ===== FORM PROFESSIONALI ===== */
body.theme-light .form-control {
    border: 2px solid var(--light-border) !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    background: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease !important;
}

body.theme-light .form-control:focus {
    border-color: var(--light-accent) !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
    background: white !important;
    transform: translateY(-1px) !important;
}

body.theme-light .form-label {
    color: var(--light-primary) !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
}

body.theme-light .form-select {
    border: 2px solid var(--light-border) !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    background: rgba(255, 255, 255, 0.9) !important;
}

body.theme-light .form-select:focus {
    border-color: var(--light-accent) !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

/* ===== TABELLE PROFESSIONALI ===== */
body.theme-light .table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 16px var(--light-shadow) !important;
    background: white !important;
}

body.theme-light .table th {
    background: linear-gradient(135deg, var(--light-bg-secondary) 0%, #ffffff 100%) !important;
    color: var(--light-primary) !important;
    font-weight: 600 !important;
    padding: 16px !important;
    border: none !important;
    position: relative !important;
}

body.theme-light .table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--light-accent), var(--light-success)) !important;
}

body.theme-light .table td {
    padding: 16px !important;
    border: none !important;
    border-bottom: 1px solid rgba(222, 226, 230, 0.5) !important;
    transition: all 0.3s ease !important;
}

body.theme-light .table tbody tr:hover {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(46, 204, 113, 0.05) 100%) !important;
    transform: scale(1.01) !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.05) !important;
}

/* ===== DROPDOWN PROFESSIONALI ===== */
body.theme-light .dropdown-menu {
    border: none !important;
    border-radius: 16px !important;
    box-shadow:
        0 16px 48px rgba(0,0,0,0.1),
        0 4px 16px rgba(0,0,0,0.05) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    padding: 12px !important;
}

body.theme-light .dropdown-item {
    border-radius: 12px !important;
    padding: 12px 16px !important;
    margin: 4px 0 !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    color: var(--light-secondary) !important;
    font-weight: 500 !important;
}

body.theme-light .dropdown-item:hover {
    background: linear-gradient(135deg, var(--light-accent) 0%, var(--light-info) 100%) !important;
    color: white !important;
    transform: translateX(4px) !important;
    box-shadow: 0 4px 16px rgba(52, 152, 219, 0.3) !important;
}

body.theme-light .dropdown-item i {
    width: 20px !important;
    margin-right: 12px !important;
    color: inherit !important;
}

/* ===== ALERT PROFESSIONALI ===== */
body.theme-light .alert {
    border: none !important;
    border-radius: 16px !important;
    padding: 20px !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-light .alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

body.theme-light .alert-primary {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(44, 62, 80, 0.05) 100%) !important;
    color: var(--light-primary) !important;
}

body.theme-light .alert-primary::before {
    background: var(--light-accent) !important;
}

body.theme-light .alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(46, 204, 113, 0.05) 100%) !important;
    color: var(--light-success) !important;
}

body.theme-light .alert-success::before {
    background: var(--light-success) !important;
}

body.theme-light .alert-warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 0%, rgba(230, 126, 34, 0.05) 100%) !important;
    color: var(--light-warning) !important;
}

body.theme-light .alert-warning::before {
    background: var(--light-warning) !important;
}

body.theme-light .alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.05) 100%) !important;
    color: var(--light-danger) !important;
}

body.theme-light .alert-danger::before {
    background: var(--light-danger) !important;
}

body.theme-light .alert-info {
    background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%) !important;
    color: var(--light-info) !important;
}

body.theme-light .alert-info::before {
    background: var(--light-info) !important;
}

/* ===== MODAL PROFESSIONALI ===== */
body.theme-light .modal-content {
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 24px 64px rgba(0,0,0,0.15) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    overflow: hidden !important;
}

body.theme-light .modal-header {
    background: linear-gradient(135deg, var(--light-bg-secondary) 0%, #ffffff 100%) !important;
    border-bottom: 2px solid var(--light-border) !important;
    padding: 24px !important;
    position: relative !important;
}

body.theme-light .modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--light-accent), var(--light-success), var(--light-info)) !important;
}

body.theme-light .modal-title {
    color: var(--light-primary) !important;
    font-weight: 600 !important;
}

body.theme-light .modal-body {
    padding: 24px !important;
    background: rgba(255, 255, 255, 0.9) !important;
}

body.theme-light .modal-footer {
    background: linear-gradient(135deg, #ffffff 0%, var(--light-bg-secondary) 100%) !important;
    border-top: 1px solid var(--light-border) !important;
    padding: 20px 24px !important;
}

/* ===== BREADCRUMB PROFESSIONALI ===== */
body.theme-light .breadcrumb {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%) !important;
    border: 1px solid var(--light-border) !important;
    border-radius: 12px !important;
    padding: 12px 20px !important;
    box-shadow: 0 2px 8px var(--light-shadow) !important;
}

body.theme-light .breadcrumb-item a {
    color: var(--light-accent) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

body.theme-light .breadcrumb-item a:hover {
    color: var(--light-primary) !important;
    transform: translateY(-1px) !important;
}

body.theme-light .breadcrumb-item.active {
    color: var(--light-secondary) !important;
    font-weight: 600 !important;
}

/* ===== ICONE PROFESSIONALI ===== */
body.theme-light i,
body.theme-light .fas,
body.theme-light .far,
body.theme-light .fab {
    transition: all 0.3s ease !important;
}

body.theme-light .text-primary i {
    color: var(--light-primary) !important;
}

body.theme-light .text-success i {
    color: var(--light-success) !important;
}

body.theme-light .text-warning i {
    color: var(--light-warning) !important;
}

body.theme-light .text-danger i {
    color: var(--light-danger) !important;
}

body.theme-light .text-info i {
    color: var(--light-info) !important;
}

/* ===== ANIMAZIONI PROFESSIONALI ===== */
@keyframes lightThemeSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes lightThemePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes colorfulGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    25% {
        box-shadow: 0 0 20px rgba(17, 153, 142, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
    }
    75% {
        box-shadow: 0 0 20px rgba(240, 147, 251, 0.3);
    }
}

@keyframes borderColorCycle {
    0%, 100% {
        border-top-color: rgba(102, 126, 234, 0.6);
    }
    25% {
        border-top-color: rgba(17, 153, 142, 0.6);
    }
    50% {
        border-top-color: rgba(79, 172, 254, 0.6);
    }
    75% {
        border-top-color: rgba(240, 147, 251, 0.6);
    }
}

body.theme-light .card {
    animation: lightThemeSlideIn 0.6s ease-out !important;
}

body.theme-light .btn:active {
    animation: lightThemePulse 0.3s ease !important;
}

/* Animazioni colorate per le stat cards */
body.theme-light .stat-card.primary:hover {
    animation: colorfulGlow 2s ease-in-out infinite !important;
}

body.theme-light .stat-card.success:hover {
    animation: colorfulGlow 2s ease-in-out infinite 0.5s !important;
}

body.theme-light .stat-card.warning:hover {
    animation: colorfulGlow 2s ease-in-out infinite 1s !important;
}

body.theme-light .stat-card.info:hover {
    animation: colorfulGlow 2s ease-in-out infinite 1.5s !important;
}

/* Effetto border colorato ciclico per porto cards */
body.theme-light .porto-detail-card:hover::before {
    animation: borderColorCycle 3s ease-in-out infinite !important;
}

/* ===== EFFETTI SPECIALI COLORATI ===== */

/* Effetto arcobaleno per card importanti */
body.theme-light .stat-card.primary::before {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.9) 0%,
        rgba(118, 75, 162, 0.8) 100%) !important;
}

body.theme-light .stat-card.success::before {
    background: linear-gradient(135deg,
        rgba(17, 153, 142, 0.9) 0%,
        rgba(56, 239, 125, 0.8) 100%) !important;
}

body.theme-light .stat-card.warning::before {
    background: linear-gradient(135deg,
        rgba(240, 147, 251, 0.9) 0%,
        rgba(245, 87, 108, 0.8) 100%) !important;
}

body.theme-light .stat-card.info::before {
    background: linear-gradient(135deg,
        rgba(79, 172, 254, 0.9) 0%,
        rgba(0, 242, 254, 0.8) 100%) !important;
}

body.theme-light .stat-card.danger::before {
    background: linear-gradient(135deg,
        rgba(255, 154, 158, 0.9) 0%,
        rgba(254, 207, 239, 0.8) 100%) !important;
}

/* Effetto particelle colorate */
@keyframes floatingParticles {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        opacity: 1;
    }
}

body.theme-light .stat-card::after {
    animation: floatingParticles 4s ease-in-out infinite !important;
}

/* Effetto shimmer colorato per le icone */
@keyframes colorfulShimmer {
    0% {
        background-position: -200px 0;
        filter: hue-rotate(0deg);
    }
    50% {
        filter: hue-rotate(180deg);
    }
    100% {
        background-position: calc(200px + 100%) 0;
        filter: hue-rotate(360deg);
    }
}

body.theme-light .stat-card .stat-icon:hover {
    animation: colorfulShimmer 2s ease-in-out infinite !important;
}

/* ===== COLORAZIONE IMMAGINI E ICONE CARD ===== */

/* Avatar Circle Colorati */
body.theme-light .avatar-circle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: 3px solid rgba(255, 255, 255, 0.8) !important;
    box-shadow:
        0 8px 24px rgba(102, 126, 234, 0.3),
        0 4px 12px rgba(0,0,0,0.1) !important;
    color: white !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-light .avatar-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

body.theme-light .avatar-circle:hover::before {
    opacity: 1;
    transform: rotate(45deg) translate(50%, 50%);
}

/* ===== ICONE STAT CARD ULTRA-COLORATE ===== */

/* Icone Stat Card Primary (Navi Totali) */
body.theme-light .stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: 3px solid rgba(255, 255, 255, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    margin: 0 auto 20px auto !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

body.theme-light .stat-card.primary .stat-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.theme-light .stat-card.primary .stat-icon:hover::after {
    opacity: 1;
}

/* Icone Stat Card Success (Navi Schedulate) */
body.theme-light .stat-card.success .stat-icon {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
    color: white !important;
    border: 3px solid rgba(255, 255, 255, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    margin: 0 auto 20px auto !important;
    box-shadow: 0 8px 24px rgba(17, 153, 142, 0.4) !important;
}

body.theme-light .stat-card.success .stat-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.theme-light .stat-card.success .stat-icon:hover::after {
    opacity: 1;
}

/* Icone Stat Card Warning (SOF da Completare) */
body.theme-light .stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    color: white !important;
    border: 3px solid rgba(255, 255, 255, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    margin: 0 auto 20px auto !important;
    box-shadow: 0 8px 24px rgba(240, 147, 251, 0.4) !important;
}

body.theme-light .stat-card.warning .stat-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.theme-light .stat-card.warning .stat-icon:hover::after {
    opacity: 1;
}

/* Icone Stat Card Info (Utenti Online) */
body.theme-light .stat-card.info .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
    border: 3px solid rgba(255, 255, 255, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    margin: 0 auto 20px auto !important;
    box-shadow: 0 8px 24px rgba(79, 172, 254, 0.4) !important;
}

body.theme-light .stat-card.info .stat-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.theme-light .stat-card.info .stat-icon:hover::after {
    opacity: 1;
}

/* Icone Stat Card Danger */
body.theme-light .stat-card.danger .stat-icon {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    color: white !important;
    border: 3px solid rgba(255, 255, 255, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.5rem !important;
    margin: 0 auto 20px auto !important;
    box-shadow: 0 8px 24px rgba(255, 154, 158, 0.4) !important;
}

body.theme-light .stat-card.danger .stat-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.theme-light .stat-card.danger .stat-icon:hover::after {
    opacity: 1;
}

/* ===== OVERRIDE SPECIFICO PER ICONE STAT CARD ===== */

/* Assicura che le icone nelle stat card siano sempre visibili e colorate */
body.theme-light .stat-card .stat-icon i,
body.theme-light .stat-card .stat-icon .fas,
body.theme-light .stat-card .stat-icon .far,
body.theme-light .stat-card .stat-icon .fab {
    color: white !important;
    font-size: 1.5rem !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

/* Hover effects per le icone */
body.theme-light .stat-card .stat-icon:hover i,
body.theme-light .stat-card .stat-icon:hover .fas,
body.theme-light .stat-card .stat-icon:hover .far,
body.theme-light .stat-card .stat-icon:hover .fab {
    transform: scale(1.1) rotate(10deg) !important;
    text-shadow: 0 4px 8px rgba(0,0,0,0.4) !important;
}

/* Animazione pulsante per le icone */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

body.theme-light .stat-card:hover .stat-icon {
    animation: iconPulse 2s ease-in-out infinite !important;
}

/* Override per qualsiasi regola che potrebbe nascondere le icone */
body.theme-light .stat-card .stat-icon * {
    color: white !important;
}

/* Specifico per ogni tipo di stat card */
body.theme-light .stat-card.primary .stat-icon * {
    color: white !important;
}

body.theme-light .stat-card.success .stat-icon * {
    color: white !important;
}

body.theme-light .stat-card.warning .stat-icon * {
    color: white !important;
}

body.theme-light .stat-card.info .stat-icon * {
    color: white !important;
}

body.theme-light .stat-card.danger .stat-icon * {
    color: white !important;
}

/* ===== EFFETTI IMMAGINI DECORATIVE ===== */

/* Immagini di background colorate per le card */
body.theme-light .card::before {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(17, 153, 142, 0.05) 100%) !important;
}

/* Overlay colorato per immagini nelle card */
body.theme-light .card img {
    border-radius: 12px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

body.theme-light .card img::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(17, 153, 142, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
}

body.theme-light .card img:hover::after {
    opacity: 1;
}

/* Filtri colorati per immagini */
body.theme-light .card img:hover {
    filter: brightness(1.1) saturate(1.2) hue-rotate(10deg) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3) !important;
}

/* Logo e icone brand colorate */
body.theme-light .navbar-brand img,
body.theme-light .card-header img {
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3)) !important;
    transition: all 0.3s ease !important;
}

body.theme-light .navbar-brand img:hover,
body.theme-light .card-header img:hover {
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.5)) brightness(1.1) !important;
    transform: scale(1.05) !important;
}

/* Effetti per immagini di sfondo delle card */
body.theme-light .card[style*="background-image"] {
    position: relative !important;
}

body.theme-light .card[style*="background-image"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(17, 153, 142, 0.1) 50%,
        rgba(79, 172, 254, 0.1) 100%) !important;
    border-radius: inherit;
    z-index: 1;
}

body.theme-light .card[style*="background-image"] > * {
    position: relative;
    z-index: 2;
}

/* Effetti per avatar e profili */
body.theme-light .profile-image,
body.theme-light .user-avatar {
    border: 4px solid transparent !important;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
}

body.theme-light .profile-image:hover,
body.theme-light .user-avatar:hover {
    transform: scale(1.05) rotate(5deg) !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
    filter: brightness(1.1) !important;
}

/* Icone FontAwesome colorate */
body.theme-light .card .fas,
body.theme-light .card .far,
body.theme-light .card .fab {
    transition: all 0.3s ease !important;
}

body.theme-light .card .text-primary {
    color: #667eea !important;
    text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3) !important;
}

body.theme-light .card .text-success {
    color: #11998e !important;
    text-shadow: 0 2px 4px rgba(17, 153, 142, 0.3) !important;
}

body.theme-light .card .text-warning {
    color: #f093fb !important;
    text-shadow: 0 2px 4px rgba(240, 147, 251, 0.3) !important;
}

body.theme-light .card .text-info {
    color: #4facfe !important;
    text-shadow: 0 2px 4px rgba(79, 172, 254, 0.3) !important;
}

body.theme-light .card .text-danger {
    color: #ff9a9e !important;
    text-shadow: 0 2px 4px rgba(255, 154, 158, 0.3) !important;
}

/* Hover effects per icone colorate */
body.theme-light .card .fas:hover,
body.theme-light .card .far:hover,
body.theme-light .card .fab:hover {
    transform: scale(1.1) rotate(10deg) !important;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.4)) !important;
}

/* ===== EFFETTI SPECIALI PER IMMAGINI SPECIFICHE ===== */

/* Effetti per cargo-ship.jpg e immagini marittime */
body.theme-light [style*="cargo-ship.jpg"],
body.theme-light .maritime-bg,
body.theme-light .ship-image {
    position: relative !important;
    overflow: hidden !important;
}

body.theme-light [style*="cargo-ship.jpg"]::before,
body.theme-light .maritime-bg::before,
body.theme-light .ship-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.15) 0%,
        rgba(17, 153, 142, 0.15) 30%,
        rgba(79, 172, 254, 0.15) 60%,
        rgba(240, 147, 251, 0.15) 100%) !important;
    z-index: 1;
    transition: all 0.3s ease;
}

body.theme-light [style*="cargo-ship.jpg"]:hover::before,
body.theme-light .maritime-bg:hover::before,
body.theme-light .ship-image:hover::before {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.25) 0%,
        rgba(17, 153, 142, 0.25) 30%,
        rgba(79, 172, 254, 0.25) 60%,
        rgba(240, 147, 251, 0.25) 100%) !important;
}

/* Effetti per loghi e brand images */
body.theme-light .logo,
body.theme-light [src*="logo"],
body.theme-light .brand-image {
    filter: drop-shadow(0 4px 12px rgba(102, 126, 234, 0.3)) !important;
    transition: all 0.3s ease !important;
}

body.theme-light .logo:hover,
body.theme-light [src*="logo"]:hover,
body.theme-light .brand-image:hover {
    filter: drop-shadow(0 6px 16px rgba(102, 126, 234, 0.5))
            brightness(1.1)
            saturate(1.2) !important;
    transform: scale(1.05) !important;
}

/* Effetti per immagini di profilo e avatar */
body.theme-light .profile-pic,
body.theme-light .user-photo,
body.theme-light [src*="avatar"],
body.theme-light [src*="profile"] {
    border: 3px solid transparent !important;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea 0%, #11998e 50%, #4facfe 100%) border-box !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

body.theme-light .profile-pic::after,
body.theme-light .user-photo::after,
body.theme-light [src*="avatar"]::after,
body.theme-light [src*="profile"]::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    width: calc(100% + 6px);
    height: calc(100% + 6px);
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #11998e, #4facfe);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

body.theme-light .profile-pic:hover::after,
body.theme-light .user-photo:hover::after,
body.theme-light [src*="avatar"]:hover::after,
body.theme-light [src*="profile"]:hover::after {
    opacity: 1;
}

body.theme-light .profile-pic:hover,
body.theme-light .user-photo:hover,
body.theme-light [src*="avatar"]:hover,
body.theme-light [src*="profile"]:hover {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

/* Effetti per icone grandi nelle card */
body.theme-light .card .fa-5x,
body.theme-light .card .fa-4x,
body.theme-light .card .fa-3x {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3)) !important;
    transition: all 0.3s ease !important;
}

body.theme-light .card .fa-5x:hover,
body.theme-light .card .fa-4x:hover,
body.theme-light .card .fa-3x:hover {
    transform: scale(1.1) rotate(10deg) !important;
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.5)) !important;
}

/* Animazione arcobaleno per immagini speciali */
@keyframes rainbowGlow {
    0% { filter: hue-rotate(0deg) drop-shadow(0 4px 8px rgba(102, 126, 234, 0.4)); }
    25% { filter: hue-rotate(90deg) drop-shadow(0 4px 8px rgba(17, 153, 142, 0.4)); }
    50% { filter: hue-rotate(180deg) drop-shadow(0 4px 8px rgba(79, 172, 254, 0.4)); }
    75% { filter: hue-rotate(270deg) drop-shadow(0 4px 8px rgba(240, 147, 251, 0.4)); }
    100% { filter: hue-rotate(360deg) drop-shadow(0 4px 8px rgba(102, 126, 234, 0.4)); }
}

body.theme-light .card:hover .logo,
body.theme-light .card:hover [src*="logo"],
body.theme-light .card:hover .brand-image {
    animation: rainbowGlow 3s ease-in-out infinite !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    body.theme-light .card {
        margin: 10px !important;
        border-radius: 12px !important;
    }

    body.theme-light .btn {
        padding: 10px 20px !important;
        font-size: 0.9rem !important;
    }

    body.theme-light .modal-content {
        margin: 20px !important;
        border-radius: 16px !important;
    }
}

/* ===== DASHBOARD CARDS PROFESSIONALI ===== */

/* Stat Cards Speciali */
body.theme-light .stat-card {
    border-radius: 20px !important;
    overflow: hidden !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 8px 32px rgba(0,0,0,0.1),
        0 2px 8px rgba(0,0,0,0.05) !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

body.theme-light .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.8) 100%);
    z-index: 1;
}

body.theme-light .stat-card > * {
    position: relative;
    z-index: 2;
}

body.theme-light .stat-card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow:
        0 20px 60px rgba(0,0,0,0.15),
        0 8px 24px rgba(0,0,0,0.1) !important;
}

/* Hover effects specifici per colore */
body.theme-light .stat-card.primary:hover {
    box-shadow:
        0 20px 60px rgba(102, 126, 234, 0.3),
        0 8px 24px rgba(102, 126, 234, 0.2) !important;
    filter: brightness(1.1) !important;
}

body.theme-light .stat-card.success:hover {
    box-shadow:
        0 20px 60px rgba(17, 153, 142, 0.3),
        0 8px 24px rgba(17, 153, 142, 0.2) !important;
    filter: brightness(1.1) !important;
}

body.theme-light .stat-card.warning:hover {
    box-shadow:
        0 20px 60px rgba(240, 147, 251, 0.3),
        0 8px 24px rgba(240, 147, 251, 0.2) !important;
    filter: brightness(1.1) !important;
}

body.theme-light .stat-card.info:hover {
    box-shadow:
        0 20px 60px rgba(79, 172, 254, 0.3),
        0 8px 24px rgba(79, 172, 254, 0.2) !important;
    filter: brightness(1.1) !important;
}

body.theme-light .stat-card.danger:hover {
    box-shadow:
        0 20px 60px rgba(255, 154, 158, 0.3),
        0 8px 24px rgba(255, 154, 158, 0.2) !important;
    filter: brightness(1.1) !important;
}

/* ===== STAT CARDS COLORATE E VIVACI ===== */

/* Stat Cards Primary - Blu Oceano */
body.theme-light .stat-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-left: 5px solid #5a67d8 !important;
    border-top: 3px solid rgba(102, 126, 234, 0.6) !important;
    position: relative !important;
}

body.theme-light .stat-card.primary::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    border-radius: 50%;
}

body.theme-light .stat-card.primary .stat-icon {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    border-radius: 16px !important;
    padding: 16px !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Stat Cards Success - Verde Smeraldo */
body.theme-light .stat-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
    border-left: 5px solid #0d7377 !important;
    border-top: 3px solid rgba(17, 153, 142, 0.6) !important;
    position: relative !important;
}

body.theme-light .stat-card.success::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    border-radius: 50%;
}

body.theme-light .stat-card.success .stat-icon {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    border-radius: 16px !important;
    padding: 16px !important;
    box-shadow: 0 4px 16px rgba(17, 153, 142, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Stat Cards Warning - Rosa Corallo */
body.theme-light .stat-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border-left: 5px solid #e91e63 !important;
    border-top: 3px solid rgba(240, 147, 251, 0.6) !important;
    position: relative !important;
}

body.theme-light .stat-card.warning::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    border-radius: 50%;
}

body.theme-light .stat-card.warning .stat-icon {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    border-radius: 16px !important;
    padding: 16px !important;
    box-shadow: 0 4px 16px rgba(240, 147, 251, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Stat Cards Info - Ciano Elettrico */
body.theme-light .stat-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border-left: 5px solid #0099cc !important;
    border-top: 3px solid rgba(79, 172, 254, 0.6) !important;
    position: relative !important;
}

body.theme-light .stat-card.info::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    border-radius: 50%;
}

body.theme-light .stat-card.info .stat-icon {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    border-radius: 16px !important;
    padding: 16px !important;
    box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Stat Cards Danger - Rosa Elegante */
body.theme-light .stat-card.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    border-left: 5px solid #ff6b6b !important;
    border-top: 3px solid rgba(255, 154, 158, 0.6) !important;
    position: relative !important;
}

body.theme-light .stat-card.danger::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    border-radius: 50%;
}

body.theme-light .stat-card.danger .stat-icon {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    border-radius: 16px !important;
    padding: 16px !important;
    box-shadow: 0 4px 16px rgba(255, 154, 158, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* ===== PORTO CARDS COLORATE ===== */
body.theme-light .porto-detail-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%) !important;
    border: 1px solid var(--light-border) !important;
    border-radius: 20px !important;
    box-shadow:
        0 8px 32px var(--light-shadow),
        0 2px 8px rgba(0,0,0,0.04) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Gradient colorato specifico per porto */
body.theme-light .porto-detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #11998e 50%, #4facfe 100%) !important;
}

/* Effetto decorativo laterale */
body.theme-light .porto-detail-card::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

body.theme-light .porto-detail-card:hover {
    transform: translateY(-6px) scale(1.01) !important;
    box-shadow:
        0 16px 48px rgba(102, 126, 234, 0.15),
        0 4px 16px rgba(17, 153, 142, 0.1) !important;
}

body.theme-light .porto-header {
    color: var(--light-primary) !important;
    font-weight: 600 !important;
    position: relative !important;
}

/* Icone porto con colori specifici per porto */
body.theme-light .porto-detail-card:nth-child(1) .porto-header i {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border-radius: 12px !important;
    padding: 12px !important;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

body.theme-light .porto-detail-card:nth-child(2) .porto-header i {
    background: linear-gradient(135deg, #11998e, #38ef7d) !important;
    color: white !important;
    border-radius: 12px !important;
    padding: 12px !important;
    box-shadow: 0 4px 16px rgba(17, 153, 142, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

body.theme-light .porto-detail-card:nth-child(3) .porto-header i {
    background: linear-gradient(135deg, #4facfe, #00f2fe) !important;
    color: white !important;
    border-radius: 12px !important;
    padding: 12px !important;
    box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

body.theme-light .porto-detail-card:nth-child(4) .porto-header i {
    background: linear-gradient(135deg, #f093fb, #f5576c) !important;
    color: white !important;
    border-radius: 12px !important;
    padding: 12px !important;
    box-shadow: 0 4px 16px rgba(240, 147, 251, 0.4) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Badge porto colorati */
body.theme-light .porto-detail-card .badge {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(17, 153, 142, 0.9)) !important;
    color: white !important;
    border-radius: 12px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* ===== EFFETTI SPECIALI ===== */

/* Shimmer Effect */
@keyframes lightShimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

body.theme-light .card-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    background-size: 200px 100%;
    animation: lightShimmer 3s infinite;
    pointer-events: none;
}

/* Glow Effect per pulsanti importanti */
body.theme-light .btn-primary,
body.theme-light .btn-success {
    position: relative !important;
}

body.theme-light .btn-primary::after,
body.theme-light .btn-success::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    background: inherit;
    filter: blur(8px);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

body.theme-light .btn-primary:hover::after,
body.theme-light .btn-success:hover::after {
    opacity: 0.6;
}

/* ===== SCROLLBAR PROFESSIONALE ===== */
body.theme-light ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

body.theme-light ::-webkit-scrollbar-track {
    background: var(--light-bg-secondary);
    border-radius: 4px;
}

body.theme-light ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--light-accent), var(--light-info));
    border-radius: 4px;
    transition: all 0.3s ease;
}

body.theme-light ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--light-primary), var(--light-secondary));
}
