/**
 * JavaScript per Dashboard Amministrazione SNIP
 * Gestisce tutte le funzionalità del pannello admin
 */

// Variabili globali
let currentSection = 'dashboard';
let dashboardData = null;
let usersData = [];
let chartsInitialized = false;

// Inizializzazione
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
    setupNavigation();
    loadDashboardData();
});

// ===== INIZIALIZZAZIONE =====

function initializeAdmin() {
    console.log('🚀 Inizializzazione Dashboard Admin SNIP');
    
    // Setup event listeners
    setupEventListeners();
    
    // Carica dati iniziali
    loadDashboardData();
    
    // Auto-refresh ogni 2 minuti per dashboard (ridotto da 30 secondi)
    setInterval(() => {
        if (currentSection === 'dashboard') {
            loadDashboardData();
        }
    }, 120000);
}

function setupEventListeners() {
    // Search utenti
    const userSearch = document.getElementById('user-search');
    if (userSearch) {
        userSearch.addEventListener('input', debounce(searchUsers, 500));
    }
}

function setupNavigation() {
    // Navigation links
    document.querySelectorAll('.admin-nav .nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const section = this.getAttribute('data-section');
            switchSection(section);
            
            // Update active state
            document.querySelectorAll('.admin-nav .nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

function switchSection(section) {
    console.log(`📋 Switching to section: ${section}`);
    
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(s => s.style.display = 'none');
    
    // Show selected section
    const sectionElement = document.getElementById(`${section}-section`);
    if (sectionElement) {
        sectionElement.style.display = 'block';
    }
    
    currentSection = section;
    
    // Load section-specific data
    switch (section) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'users':
            loadUsers();
            break;
        case 'config':
            loadConfigurations();
            break;
        case 'audit':
            loadAuditLogs();
            break;
        case 'sessions':
            loadSessions();
            break;
        case 'system':
            loadSystemInfo();
            break;
    }
}

// ===== DASHBOARD =====

async function loadDashboardData() {
    try {
        console.log('📊 Caricamento dati dashboard...');
        
        const response = await fetch('/admin/stats/dashboard');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        dashboardData = await response.json();
        updateDashboardUI();
        
        // Carica anche system health
        loadSystemHealth();
        
    } catch (error) {
        console.error('❌ Errore caricamento dashboard:', error);
        showError('Errore caricamento dashboard: ' + error.message);
    }
}

function updateDashboardUI() {
    if (!dashboardData) return;
    
    console.log('🔄 Aggiornamento UI dashboard');
    
    // Update statistics cards
    const totals = dashboardData.totals || {};
    document.getElementById('total-users').textContent = totals.users || 0;
    document.getElementById('active-sessions').textContent = totals.active_sessions || 0;
    document.getElementById('total-viaggi').textContent = totals.viaggi || 0;
    document.getElementById('recent-activity').textContent = dashboardData.activity?.recent_actions || 0;
    
    // Update charts
    updateCharts();
}

function updateCharts() {
    if (!dashboardData || chartsInitialized) return;
    
    // Chart Utenti per Reparto
    const repartoCtx = document.getElementById('repartoChart');
    if (repartoCtx && dashboardData.users_by_reparto) {
        new Chart(repartoCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(dashboardData.users_by_reparto),
                datasets: [{
                    data: Object.values(dashboardData.users_by_reparto),
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // Chart Utenti per Ruolo
    const ruoloCtx = document.getElementById('ruoloChart');
    if (ruoloCtx && dashboardData.users_by_ruolo) {
        new Chart(ruoloCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(dashboardData.users_by_ruolo),
                datasets: [{
                    label: 'Utenti',
                    data: Object.values(dashboardData.users_by_ruolo),
                    backgroundColor: '#667eea',
                    borderColor: '#764ba2',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    chartsInitialized = true;
}

async function loadSystemHealth() {
    try {
        const response = await fetch('/admin/system/health');
        const health = await response.json();
        
        const healthElement = document.getElementById('system-health');
        if (healthElement) {
            const statusClass = health.status === 'healthy' ? 'text-success' : 'text-danger';
            const statusIcon = health.status === 'healthy' ? 'fa-check-circle' : 'fa-exclamation-triangle';
            
            healthElement.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <i class="fas ${statusIcon} ${statusClass} me-2"></i>
                            <span class="${statusClass}">Sistema ${health.status === 'healthy' ? 'Operativo' : 'Problemi'}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Database:</small><br>
                        <span class="text-success">${health.database || 'OK'}</span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Sessioni Attive:</small><br>
                        <span>${health.active_sessions || 0}</span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Ultimo Aggiornamento:</small><br>
                        <span>${new Date(health.timestamp).toLocaleString('it-IT')}</span>
                    </div>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('❌ Errore caricamento system health:', error);
        const healthElement = document.getElementById('system-health');
        if (healthElement) {
            healthElement.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Errore caricamento stato sistema
                </div>
            `;
        }
    }
}

// ===== GESTIONE UTENTI =====

async function loadUsers(search = '') {
    try {
        console.log('👥 Caricamento utenti...');
        
        let url = '/admin/users?limit=100';
        if (search) {
            url += `&search=${encodeURIComponent(search)}`;
        }
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        usersData = await response.json();
        updateUsersTable();
        
    } catch (error) {
        console.error('❌ Errore caricamento utenti:', error);
        showError('Errore caricamento utenti: ' + error.message);
    }
}

function updateUsersTable() {
    const tbody = document.getElementById('users-table-body');
    if (!tbody) return;
    
    if (usersData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-users me-2"></i>Nessun utente trovato
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = usersData.map(user => `
        <tr>
            <td>${user.id_user}</td>
            <td>${user.Nome} ${user.Cognome}</td>
            <td>${user.email}</td>
            <td>
                <span class="badge bg-primary">${user.reparto}</span>
            </td>
            <td>
                <span class="badge bg-secondary">${user.ruolo}</span>
            </td>
            <td>
                <span class="badge ${user.visibile === 'si' ? 'bg-success' : 'bg-danger'}">
                    ${user.visibile === 'si' ? 'Attivo' : 'Inattivo'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editUser(${user.id_user})" title="Modifica">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="invalidateUserSessions(${user.id_user})" title="Invalida Sessioni">
                        <i class="fas fa-user-times"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteUser(${user.id_user})" title="Elimina">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function searchUsers() {
    const searchTerm = document.getElementById('user-search').value;
    loadUsers(searchTerm);
}

function showCreateUserModal() {
    // TODO: Implementare modal per creazione utente
    alert('Funzionalità in sviluppo: Creazione nuovo utente');
}

function editUser(userId) {
    // TODO: Implementare modal per modifica utente
    alert(`Funzionalità in sviluppo: Modifica utente ${userId}`);
}

async function deleteUser(userId) {
    if (!confirm('Sei sicuro di voler eliminare questo utente?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/users/${userId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showSuccess('Utente eliminato con successo');
            loadUsers();
        } else {
            throw new Error('Errore eliminazione utente');
        }
        
    } catch (error) {
        console.error('❌ Errore eliminazione utente:', error);
        showError('Errore eliminazione utente: ' + error.message);
    }
}

async function invalidateUserSessions(userId) {
    if (!confirm('Sei sicuro di voler invalidare tutte le sessioni di questo utente?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/users/${userId}/invalidate-sessions`, {
            method: 'POST'
        });
        
        if (response.ok) {
            const result = await response.json();
            showSuccess(`${result.sessions_invalidated} sessioni invalidate`);
        } else {
            throw new Error('Errore invalidazione sessioni');
        }
        
    } catch (error) {
        console.error('❌ Errore invalidazione sessioni:', error);
        showError('Errore invalidazione sessioni: ' + error.message);
    }
}

// ===== PLACEHOLDER FUNCTIONS =====

function loadConfigurations() {
    console.log('⚙️ Caricamento configurazioni...');
    // TODO: Implementare
}

function loadAuditLogs() {
    console.log('📋 Caricamento audit logs...');
    // TODO: Implementare
}

function loadSessions() {
    console.log('🔐 Caricamento sessioni...');
    // TODO: Implementare
}

function loadSystemInfo() {
    console.log('🖥️ Caricamento info sistema...');
    // TODO: Implementare
}

// ===== UTILITY FUNCTIONS =====

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showSuccess(message) {
    // TODO: Implementare toast notifications
    alert('✅ ' + message);
}

function showError(message) {
    // TODO: Implementare toast notifications
    alert('❌ ' + message);
}

function showInfo(message) {
    // TODO: Implementare toast notifications
    alert('ℹ️ ' + message);
}
