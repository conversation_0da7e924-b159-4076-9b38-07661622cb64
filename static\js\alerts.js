/**
 * Auto-hide alerts after 10 seconds with enhanced animations
 * Applies to all Bootstrap alerts with .alert class
 */
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert, index) {
        // Only auto-hide if the alert is dismissible
        if (alert.classList.contains('alert-dismissible')) {
            // Add a progress bar for visual feedback
            addProgressBar(alert);

            // Auto-hide after 10 seconds
            setTimeout(function() {
                // Check if the alert still exists (user might have closed it manually)
                if (alert && alert.parentNode) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 10000); // 10 seconds
        }
    });
});

/**
 * Add a progress bar to show remaining time
 */
function addProgressBar(alert) {
    const progressBar = document.createElement('div');
    progressBar.className = 'alert-progress-bar';
    progressBar.style.cssText = `
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: currentColor;
        opacity: 0.3;
        width: 100%;
        animation: shrinkWidth 10s linear forwards;
    `;

    // Add CSS animation if not already added
    if (!document.getElementById('alert-animations')) {
        const style = document.createElement('style');
        style.id = 'alert-animations';
        style.textContent = `
            @keyframes shrinkWidth {
                from { width: 100%; }
                to { width: 0%; }
            }
        `;
        document.head.appendChild(style);
    }

    alert.style.position = 'relative';
    alert.appendChild(progressBar);
}
