/**
 * Configuration Management JavaScript
 * Gestisce tutte le configurazioni del sistema SNIP
 */

// Configurazioni globali
let currentConfigurations = {};
let hasUnsavedChanges = false;

// Inizializzazione
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('config-section')) {
        initializeConfigurationManagement();
    }
});

function initializeConfigurationManagement() {
    console.log('🔧 Inizializzazione gestione configurazioni...');
    
    // Carica configurazioni esistenti
    loadConfigurations();
    
    // Aggiungi event listeners per i cambiamenti
    addConfigurationListeners();
    
    // Aggiorna statistiche sistema
    updateSystemStats();
    
    // Auto-save ogni 5 minuti se ci sono modifiche (ridotto da 30 secondi)
    setInterval(autoSaveConfigurations, 300000);
}

function loadConfigurations() {
    console.log('📥 Caricamento configurazioni...');
    
    fetch('/admin/api/configurations', {
        credentials: 'include' // IMPORTANTE: Include i cookie di autenticazione
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentConfigurations = data.configurations;
                populateConfigurationForms();
                console.log('✅ Configurazioni caricate');
            } else {
                console.error('❌ Errore caricamento configurazioni:', data.message);
                showNotification('Errore nel caricamento delle configurazioni', 'error');
            }
        })
        .catch(error => {
            console.error('❌ Errore fetch configurazioni:', error);
            // Carica valori predefiniti se l'API non è disponibile
            loadDefaultConfigurations();
        });
}

function loadDefaultConfigurations() {
    console.log('🔄 Caricamento configurazioni predefinite...');
    
    // Configurazioni predefinite per sviluppo
    currentConfigurations = {
        ports: {
            salerno: {
                name: 'Porto di Salerno',
                code: 'ITSAL',
                harbor: '+39 089 123456',
                customs: '+39 089 654321',
                email: '<EMAIL>'
            },
            gioia: {
                name: 'Porto di Gioia Tauro',
                code: 'ITGIT',
                harbor: '+39 0966 123456',
                customs: '+39 0966 654321',
                email: '<EMAIL>'
            }
        },
        email: {
            smtp_host: 'smtp.gmail.com',
            smtp_port: 587,
            smtp_username: '<EMAIL>',
            sender_email: '<EMAIL>',
            sender_name: 'Michele Autuori Srl',
            admin_email: '<EMAIL>',
            support_email: '<EMAIL>'
        },
        security: {
            password_min_length: 6,
            password_expiry_days: 90,
            session_timeout: 60,
            max_login_attempts: 5,
            log_retention: 90
        },
        sof: {
            title: 'STATEMENT OF FACTS',
            subtitle: 'shipping and forwarding agency',
            logo_size: 'medium',
            numbering: 'auto'
        },
        interface: {
            theme: 'maritime',
            primary_color: '#0066cc',
            language: 'it',
            timezone: 'Europe/Rome'
        }
    };
    
    populateConfigurationForms();
}

function populateConfigurationForms() {
    console.log('📝 Popolamento form configurazioni...');
    
    // Popola configurazioni porti
    if (currentConfigurations.ports) {
        const ports = currentConfigurations.ports;
        
        // Salerno
        if (ports.salerno) {
            setFieldValue('salerno-name', ports.salerno.name);
            setFieldValue('salerno-code', ports.salerno.code);
            setFieldValue('salerno-harbor', ports.salerno.harbor);
            setFieldValue('salerno-customs', ports.salerno.customs);
            setFieldValue('salerno-email', ports.salerno.email);
        }
        
        // Gioia Tauro
        if (ports.gioia) {
            setFieldValue('gioia-name', ports.gioia.name);
            setFieldValue('gioia-code', ports.gioia.code);
            setFieldValue('gioia-harbor', ports.gioia.harbor);
            setFieldValue('gioia-customs', ports.gioia.customs);
            setFieldValue('gioia-email', ports.gioia.email);
        }
    }
    
    // Popola configurazioni email
    if (currentConfigurations.email) {
        const email = currentConfigurations.email;
        setFieldValue('smtp-host', email.smtp_host);
        setFieldValue('smtp-port', email.smtp_port);
        setFieldValue('smtp-username', email.smtp_username);

        // Gestione speciale per la password
        const passwordField = document.getElementById('smtp-password');
        const passwordStatus = document.getElementById('password-status');
        const passwordHelpText = document.getElementById('password-help-text');

        if (passwordField) {
            if (email.smtp_password && email.smtp_password.trim() !== '') {
                // Se c'è una password salvata, mostra placeholder
                passwordField.value = '';
                passwordField.placeholder = 'Password configurata (lascia vuoto per non modificare)';

                // Aggiorna indicatore di stato
                if (passwordStatus) {
                    passwordStatus.innerHTML = '<i class="fas fa-check-circle text-success" title="Password configurata"></i>';
                }
                if (passwordHelpText) {
                    passwordHelpText.textContent = 'Password già configurata. Inserisci una nuova password solo se vuoi modificarla.';
                }
            } else {
                // Se non c'è password, mostra campo vuoto
                passwordField.value = '';
                passwordField.placeholder = 'Inserisci password SMTP';

                // Aggiorna indicatore di stato
                if (passwordStatus) {
                    passwordStatus.innerHTML = '<i class="fas fa-exclamation-triangle text-warning" title="Password non configurata"></i>';
                }
                if (passwordHelpText) {
                    passwordHelpText.textContent = 'Password SMTP richiesta per l\'invio email. Inserisci la password del tuo account email.';
                }
            }
        }

        setFieldValue('sender-email', email.sender_email);
        setFieldValue('sender-name', email.sender_name);
        setFieldValue('admin-email', email.admin_email);
        setFieldValue('support-email', email.support_email);
        setFieldValue('smtp-ssl', email.smtp_ssl, 'checkbox');
    }
    
    // Popola configurazioni sicurezza
    if (currentConfigurations.security) {
        const security = currentConfigurations.security;
        setFieldValue('password-min-length', security.password_min_length);
        setFieldValue('password-expiry', security.password_expiry_days);
        setFieldValue('session-timeout', security.session_timeout);
        setFieldValue('max-login-attempts', security.max_login_attempts);
        setFieldValue('log-retention', security.log_retention);
        setFieldValue('password-uppercase', security.password_uppercase, 'checkbox');
        setFieldValue('password-numbers', security.password_numbers, 'checkbox');
        setFieldValue('password-special', security.password_special, 'checkbox');
    }
    
    // Popola configurazioni SOF
    if (currentConfigurations.sof) {
        const sof = currentConfigurations.sof;
        setFieldValue('sof-title', sof.title);
        setFieldValue('sof-subtitle', sof.subtitle);
        setFieldValue('sof-logo-size', sof.logo_size);
        setFieldValue('sof-numbering', sof.numbering);
    }
    
    // Popola configurazioni interface
    if (currentConfigurations.interface) {
        const ui = currentConfigurations.interface;
        setFieldValue('theme-main', ui.theme);
        setFieldValue('primary-color', ui.primary_color);
        setFieldValue('default-language', ui.language);
        setFieldValue('timezone', ui.timezone);
    }

    // Popola configurazioni database
    if (currentConfigurations.database) {
        const database = currentConfigurations.database;
        setFieldValue('db-backup-schedule', database.backup_schedule);
        setFieldValue('db-backup-time', database.backup_time);
        setFieldValue('db-backup-retention', database.backup_retention);
        setFieldValue('db-backup-path', database.backup_path);
        setFieldValue('db-compress-backup', database.compress_backup, 'checkbox');
        setFieldValue('db-log-cleanup', database.log_cleanup);
        setFieldValue('db-archive-months', database.archive_months);
        setFieldValue('db-optimize', database.optimize);
        setFieldValue('db-auto-vacuum', database.auto_vacuum, 'checkbox');
        setFieldValue('db-analyze', database.analyze, 'checkbox');
        setFieldValue('db-disk-threshold', database.disk_threshold);
        setFieldValue('db-connection-threshold', database.connection_threshold);
        setFieldValue('db-monitor-performance', database.monitor_performance, 'checkbox');
        setFieldValue('db-alert-email', database.alert_email, 'checkbox');
    }

    // Popola configurazioni reporting
    if (currentConfigurations.reporting) {
        const reporting = currentConfigurations.reporting;
        setFieldValue('daily-report', reporting.daily_report);
        setFieldValue('weekly-report', reporting.weekly_report);
        setFieldValue('monthly-report', reporting.monthly_report);
        setFieldValue('kpi-voyages', reporting.kpi_voyages, 'checkbox');
        setFieldValue('kpi-sof', reporting.kpi_sof, 'checkbox');
        setFieldValue('kpi-users', reporting.kpi_users, 'checkbox');
        setFieldValue('kpi-ports', reporting.kpi_ports, 'checkbox');
        setFieldValue('kpi-performance', reporting.kpi_performance, 'checkbox');
        setFieldValue('kpi-revenue', reporting.kpi_revenue, 'checkbox');
        setFieldValue('export-format', reporting.export_format);
        setFieldValue('auto-export', reporting.auto_export, 'checkbox');
        setFieldValue('compress-exports', reporting.compress_exports, 'checkbox');
        setFieldValue('report-recipients', reporting.report_recipients);
        setFieldValue('include-charts', reporting.include_charts, 'checkbox');
        setFieldValue('detailed-reports', reporting.detailed_reports, 'checkbox');
    }

    // Popola configurazioni system
    if (currentConfigurations.system) {
        const system = currentConfigurations.system;
        setFieldValue('app-version', system.app_version);
        setFieldValue('environment', system.environment);
        setFieldValue('debug-mode', system.debug_mode);
        setFieldValue('log-level', system.log_level);
        setFieldValue('max-upload-size', system.max_upload_size);
        setFieldValue('request-timeout', system.request_timeout);
        setFieldValue('max-connections', system.max_connections);
        setFieldValue('cache-enabled', system.cache_enabled, 'checkbox');
        setFieldValue('compression-enabled', system.compression_enabled, 'checkbox');
        setFieldValue('maintenance-mode', system.maintenance_mode);
        setFieldValue('maintenance-message', system.maintenance_message);
        setFieldValue('auto-updates', system.auto_updates, 'checkbox');
    }
}

function setFieldValue(fieldId, value) {
    const field = document.getElementById(fieldId);
    if (field && value !== undefined && value !== null) {
        if (field.type === 'checkbox') {
            field.checked = value === true || value === 'true' || value === 'si';
        } else {
            field.value = value;
        }
    }
}

function addConfigurationListeners() {
    console.log('👂 Aggiunta event listeners...');

    // Aggiungi listener a tutti i campi di configurazione
    const configFields = document.querySelectorAll('#config-content input, #config-content select, #config-content textarea');

    configFields.forEach(field => {
        field.addEventListener('change', function() {
            hasUnsavedChanges = true;
            updateSaveButtonState();

            // Validazione email in tempo reale
            if (field.type === 'email' || ['smtp-username', 'sender-email', 'admin-email', 'support-email'].includes(field.id)) {
                validateEmailField(field.id);
            }
        });

        // Per input text, aggiungi anche listener per input
        if (field.type === 'text' || field.type === 'email' || field.type === 'number' || field.type === 'password') {
            field.addEventListener('input', function() {
                hasUnsavedChanges = true;
                updateSaveButtonState();

                // Validazione email in tempo reale
                if (field.type === 'email' || ['smtp-username', 'sender-email', 'admin-email', 'support-email'].includes(field.id)) {
                    validateEmailField(field.id);
                }

                // Aggiorna stato password
                if (field.id === 'smtp-password') {
                    updatePasswordStatus();
                }
            });
        }
    });
}

function updateSaveButtonState() {
    const saveButton = document.querySelector('button[onclick="saveAllConfigurations()"]');
    if (saveButton) {
        if (hasUnsavedChanges) {
            saveButton.classList.remove('btn-success');
            saveButton.classList.add('btn-warning');
            saveButton.innerHTML = '<i class="fas fa-save me-2"></i>Salva Modifiche';
        } else {
            saveButton.classList.remove('btn-warning');
            saveButton.classList.add('btn-success');
            saveButton.innerHTML = '<i class="fas fa-save me-2"></i>Salva Tutto';
        }
    }
}

function saveAllConfigurations() {
    console.log('💾 Salvataggio configurazioni...');

    if (!hasUnsavedChanges) {
        showNotification('Nessuna modifica da salvare', 'info');
        return;
    }

    // Valida i campi email prima del salvataggio
    if (!validateAllEmailFields()) {
        showNotification('Correggere gli errori nei campi email prima di salvare', 'error');
        return;
    }

    // Raccogli tutte le configurazioni dai form
    const configurations = collectConfigurationData();
    
    // Mostra loading
    const saveButton = document.querySelector('button[onclick="saveAllConfigurations()"]');
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Salvando...';
    saveButton.disabled = true;
    
    fetch('/admin/api/configurations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include', // IMPORTANTE: Include i cookie di autenticazione
        body: JSON.stringify(configurations)
    })
    .then(response => {
        if (!response.ok) {
            // Se la risposta non è ok, gestisci l'errore
            return response.json().then(errorData => {
                throw new Error(errorData.detail || errorData.message || `Errore HTTP ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            hasUnsavedChanges = false;
            updateSaveButtonState();
            showNotification('Configurazioni salvate con successo!', 'success');
            console.log('✅ Configurazioni salvate');
        } else {
            // Gestisci sia 'message' che 'detail' per compatibilità
            const errorMessage = data.message || data.detail || 'Errore sconosciuto durante il salvataggio';
            showNotification('Errore nel salvataggio: ' + errorMessage, 'error');
            console.error('❌ Errore salvataggio:', errorMessage);
        }
    })
    .catch(error => {
        const errorMessage = error.message || 'Errore di connessione durante il salvataggio';
        showNotification('Errore nel salvataggio: ' + errorMessage, 'error');
        console.error('❌ Errore fetch salvataggio:', error);
    })
    .finally(() => {
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    });
}

function collectConfigurationData() {
    return {
        ports: {
            salerno: {
                name: getFieldValue('salerno-name'),
                code: getFieldValue('salerno-code'),
                harbor: getFieldValue('salerno-harbor'),
                customs: getFieldValue('salerno-customs'),
                email: getFieldValue('salerno-email')
            },
            gioia: {
                name: getFieldValue('gioia-name'),
                code: getFieldValue('gioia-code'),
                harbor: getFieldValue('gioia-harbor'),
                customs: getFieldValue('gioia-customs'),
                email: getFieldValue('gioia-email')
            }
        },
        email: {
            smtp_host: getFieldValue('smtp-host'),
            smtp_port: parseInt(getFieldValue('smtp-port')),
            smtp_username: getFieldValue('smtp-username'),
            smtp_password: getPasswordFieldValue('smtp-password'),
            sender_email: getFieldValue('sender-email'),
            sender_name: getFieldValue('sender-name'),
            admin_email: getFieldValue('admin-email'),
            support_email: getFieldValue('support-email'),
            smtp_ssl: getFieldValue('smtp-ssl', 'checkbox')
        },
        security: {
            password_min_length: parseInt(getFieldValue('password-min-length')),
            password_expiry_days: parseInt(getFieldValue('password-expiry')),
            session_timeout: parseInt(getFieldValue('session-timeout')),
            max_login_attempts: parseInt(getFieldValue('max-login-attempts')),
            account_lockout_minutes: parseInt(getFieldValue('account-lockout')),
            log_retention: parseInt(getFieldValue('log-retention')),
            password_uppercase: getFieldValue('password-uppercase', 'checkbox'),
            password_numbers: getFieldValue('password-numbers', 'checkbox'),
            password_special: getFieldValue('password-special', 'checkbox'),
            two_factor_auth: getFieldValue('two-factor-auth', 'checkbox'),
            ip_whitelist: getFieldValue('ip-whitelist', 'checkbox'),
            log_login: getFieldValue('log-login', 'checkbox'),
            log_actions: getFieldValue('log-actions', 'checkbox'),
            log_errors: getFieldValue('log-errors', 'checkbox'),
            backup_frequency: getFieldValue('backup-frequency'),
            backup_retention: parseInt(getFieldValue('backup-retention')),
            encrypt_backups: getFieldValue('encrypt-backups', 'checkbox'),
            ssl_only: getFieldValue('ssl-only', 'checkbox')
        },
        sof: {
            title: getFieldValue('sof-title'),
            subtitle: getFieldValue('sof-subtitle'),
            logo_size: getFieldValue('sof-logo-size'),
            numbering: getFieldValue('sof-numbering'),
            logo_top: getFieldValue('sof-logo-top', 'checkbox'),
            logo_bottom: getFieldValue('sof-logo-bottom', 'checkbox')
        },
        interface: {
            theme: getFieldValue('theme-main'),
            primary_color: getFieldValue('primary-color'),
            secondary_color: getFieldValue('secondary-color'),
            accent_color: getFieldValue('accent-color'),
            language: getFieldValue('default-language'),
            timezone: getFieldValue('timezone'),
            date_format: getFieldValue('date-format'),
            sidebar_default: getFieldValue('sidebar-default'),
            responsive_design: getFieldValue('responsive-design', 'checkbox'),
            dark_mode: getFieldValue('dark-mode', 'checkbox'),
            glassmorphism: getFieldValue('glassmorphism', 'checkbox')
        },
        database: {
            backup_schedule: getFieldValue('db-backup-schedule'),
            backup_time: getFieldValue('db-backup-time'),
            backup_retention: parseInt(getFieldValue('db-backup-retention')),
            backup_path: getFieldValue('db-backup-path'),
            compress_backup: getFieldValue('db-compress-backup', 'checkbox'),
            log_cleanup: getFieldValue('db-log-cleanup'),
            archive_months: parseInt(getFieldValue('db-archive-months')),
            optimize: getFieldValue('db-optimize'),
            auto_vacuum: getFieldValue('db-auto-vacuum', 'checkbox'),
            analyze: getFieldValue('db-analyze', 'checkbox'),
            disk_threshold: parseInt(getFieldValue('db-disk-threshold')),
            connection_threshold: parseInt(getFieldValue('db-connection-threshold')),
            monitor_performance: getFieldValue('db-monitor-performance', 'checkbox'),
            alert_email: getFieldValue('db-alert-email', 'checkbox')
        },
        reporting: {
            daily_report: getFieldValue('daily-report'),
            daily_report_time: getFieldValue('daily-report-time'),
            weekly_report: getFieldValue('weekly-report'),
            monthly_report: getFieldValue('monthly-report'),
            kpi_voyages: getFieldValue('kpi-voyages', 'checkbox'),
            kpi_sof: getFieldValue('kpi-sof', 'checkbox'),
            kpi_users: getFieldValue('kpi-users', 'checkbox'),
            kpi_ports: getFieldValue('kpi-ports', 'checkbox'),
            kpi_performance: getFieldValue('kpi-performance', 'checkbox'),
            kpi_revenue: getFieldValue('kpi-revenue', 'checkbox'),
            export_format: getFieldValue('export-format'),
            auto_export: getFieldValue('auto-export', 'checkbox'),
            compress_exports: getFieldValue('compress-exports', 'checkbox'),
            report_recipients: getFieldValue('report-recipients'),
            include_charts: getFieldValue('include-charts', 'checkbox'),
            detailed_reports: getFieldValue('detailed-reports', 'checkbox')
        },
        system: {
            app_version: getFieldValue('app-version'),
            debug_mode: getFieldValue('debug-mode'),
            log_level: getFieldValue('log-level'),
            max_upload_size: parseInt(getFieldValue('max-upload-size')),
            request_timeout: parseInt(getFieldValue('request-timeout')),
            max_connections: parseInt(getFieldValue('max-connections')),
            cache_enabled: getFieldValue('cache-enabled', 'checkbox'),
            compression_enabled: getFieldValue('compression-enabled', 'checkbox'),
            maintenance_mode: getFieldValue('maintenance-mode'),
            maintenance_message: getFieldValue('maintenance-message'),
            auto_updates: getFieldValue('auto-updates', 'checkbox')
        }
    };
}

function getFieldValue(fieldId, type = 'text') {
    const field = document.getElementById(fieldId);
    if (!field) return null;

    if (type === 'checkbox') {
        return field.checked;
    }
    return field.value;
}

function getPasswordFieldValue(fieldId) {
    const field = document.getElementById(fieldId);
    if (!field) return null;

    const value = field.value.trim();

    // Se il campo è vuoto, restituisci una stringa speciale per indicare "non modificare"
    if (!value) {
        return '__KEEP_EXISTING__';
    }

    // Se il valore è un placeholder, restituisci la stringa speciale
    if (value === '••••••••' || value === '********') {
        return '__KEEP_EXISTING__';
    }

    // Altrimenti restituisci il valore reale (nuova password)
    return value;
}

// Funzione di validazione email
function validateEmailField(fieldId) {
    const field = document.getElementById(fieldId);
    if (!field) return true;

    const email = field.value.trim();
    if (!email) return true; // Campo vuoto è valido

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);

    // Rimuovi classi precedenti
    field.classList.remove('is-valid', 'is-invalid');

    // Aggiungi classe appropriata
    if (isValid) {
        field.classList.add('is-valid');
    } else {
        field.classList.add('is-invalid');
    }

    return isValid;
}

// Funzione per validare tutti i campi email
function validateAllEmailFields() {
    const emailFields = ['smtp-username', 'sender-email', 'admin-email', 'support-email'];
    let allValid = true;

    emailFields.forEach(fieldId => {
        if (!validateEmailField(fieldId)) {
            allValid = false;
        }
    });

    return allValid;
}

function autoSaveConfigurations() {
    if (hasUnsavedChanges) {
        console.log('🔄 Auto-save configurazioni...');
        saveAllConfigurations();
    }
}

function resetConfigurations() {
    Swal.fire({
        title: 'Reset Configurazioni',
        text: 'Sei sicuro di voler ripristinare tutte le configurazioni ai valori predefiniti?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sì, ripristina',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            loadDefaultConfigurations();
            hasUnsavedChanges = true;
            updateSaveButtonState();
            showNotification('Configurazioni ripristinate ai valori predefiniti', 'info');
        }
    });
}

// Funzioni specifiche per le sezioni

function testEmailConfiguration() {
    console.log('📧 Test configurazione email...');

    const emailConfig = {
        smtp_host: getFieldValue('smtp-host'),
        smtp_port: parseInt(getFieldValue('smtp-port')),
        smtp_username: getFieldValue('smtp-username'),
        smtp_password: getPasswordFieldValue('smtp-password'),
        sender_email: getFieldValue('sender-email'),
        smtp_ssl: getFieldValue('smtp-ssl', 'checkbox')
    };
    
    fetch('/admin/api/test-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include', // IMPORTANTE: Include i cookie di autenticazione
        body: JSON.stringify(emailConfig)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Email di test inviata con successo!', 'success');
        } else {
            let errorMessage = 'Errore nell\'invio email: ' + data.message;

            // Aggiungi suggerimenti se disponibili
            if (data.suggestions && data.suggestions.length > 0) {
                errorMessage += '\n\nSuggerimenti:\n' + data.suggestions.map(s => '• ' + s).join('\n');
            }

            // Mostra errore dettagliato con suggerimenti
            Swal.fire({
                icon: 'error',
                title: 'Errore Test Email',
                text: data.message,
                html: data.suggestions ?
                    `<p><strong>Errore:</strong> ${data.message}</p>
                     <hr>
                     <p><strong>Suggerimenti:</strong></p>
                     <ul style="text-align: left;">
                        ${data.suggestions.map(s => `<li>${s}</li>`).join('')}
                     </ul>` :
                    `<p>${data.message}</p>`,
                width: '600px'
            });
        }
    })
    .catch(error => {
        showNotification('Errore di connessione durante il test email', 'error');
        console.error('❌ Errore test email:', error);
    });
}

function previewSOFTemplate() {
    console.log('👁️ Anteprima template SOF...');
    
    const sofConfig = {
        title: getFieldValue('sof-title'),
        subtitle: getFieldValue('sof-subtitle'),
        logo_size: getFieldValue('sof-logo-size'),
        logo_top: getFieldValue('sof-logo-top', 'checkbox'),
        logo_bottom: getFieldValue('sof-logo-bottom', 'checkbox')
    };
    
    // Apri modal con anteprima
    Swal.fire({
        title: 'Anteprima Template SOF',
        html: generateSOFPreview(sofConfig),
        width: '80%',
        showCloseButton: true,
        showConfirmButton: false
    });
}

function generateSOFPreview(config) {
    return `
        <div class="sof-preview" style="border: 1px solid #ddd; padding: 20px; background: white; text-align: center;">
            ${config.logo_top ? '<div style="margin-bottom: 20px;"><img src="/static/images/logo.png" style="height: 60px;"></div>' : ''}
            <h2 style="color: #0066cc; margin: 20px 0;">${config.title}</h2>
            <p style="color: #666; margin-bottom: 30px;">${config.subtitle}</p>
            <div style="text-align: left; margin: 20px 0;">
                <p><strong>Campo:</strong> Valore di esempio</p>
                <p><strong>Porto di Arrivo:</strong> SALERNO</p>
                <p><strong>SBE:</strong> 12:00</p>
                <p><strong>Arrival Pilot:</strong> 12:30</p>
            </div>
            ${config.logo_bottom ? '<div style="margin-top: 30px;"><img src="/static/images/logo.png" style="height: 40px;"></div>' : ''}
        </div>
    `;
}

function updateSystemStats() {
    console.log('📊 Aggiornamento statistiche sistema...');
    
    fetch('/admin/api/system-stats', {
        credentials: 'include' // IMPORTANTE: Include i cookie di autenticazione
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatDisplay('cpu-usage', data.stats.cpu_usage);
                updateStatDisplay('memory-usage', data.stats.memory_usage);
                updateStatDisplay('disk-usage', data.stats.disk_usage);
            }
        })
        .catch(error => {
            console.error('❌ Errore caricamento statistiche:', error);
        });
}

function updateStatDisplay(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value + '%';
        
        // Aggiorna colore badge
        element.className = 'badge';
        if (value < 50) {
            element.classList.add('bg-success');
        } else if (value < 80) {
            element.classList.add('bg-warning');
        } else {
            element.classList.add('bg-danger');
        }
        
        // Aggiorna progress bar
        const progressBar = element.closest('.mb-3').querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = value + '%';
            progressBar.className = 'progress-bar';
            if (value < 50) {
                progressBar.classList.add('bg-success');
            } else if (value < 80) {
                progressBar.classList.add('bg-warning');
            } else {
                progressBar.classList.add('bg-danger');
            }
        }
    }
}

function refreshSystemStats() {
    updateSystemStats();
    showNotification('Statistiche sistema aggiornate', 'info');
}

// Funzioni per azioni database
async function createManualBackup() {
    try {
        showNotification('Backup manuale avviato...', 'info');

        // Disabilita il pulsante durante l'operazione
        const backupBtn = document.querySelector('[onclick="createManualBackup()"]');
        if (backupBtn) {
            backupBtn.disabled = true;
            backupBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creando backup...';
        }

        // Chiama l'endpoint per il backup manuale
        const response = await fetch('/admin/backup/manual?format_type=custom', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include' // IMPORTANTE: Include i cookie di autenticazione
        });

        const data = await response.json();

        if (response.ok && data.success) {
            showNotification(`Backup creato con successo: ${data.backup_file}`, 'success');

            // Mostra dettagli del backup
            const details = `
                <strong>Backup completato!</strong><br>
                📁 File: ${data.backup_file}<br>
                📊 Dimensione: ${(data.file_size / 1024).toFixed(1)} KB<br>
                🔧 Formato: ${data.format}<br>
                👤 Creato da: ${data.created_by}<br>
                ⏰ Timestamp: ${new Date(data.timestamp).toLocaleString()}
            `;

            // Mostra modal con dettagli (se disponibile)
            if (typeof showModal === 'function') {
                showModal('Backup Completato', details);
            }

        } else {
            throw new Error(data.detail || 'Errore durante la creazione del backup');
        }

    } catch (error) {
        console.error('Errore backup manuale:', error);
        showNotification(`Errore backup: ${error.message}`, 'error');
    } finally {
        // Riabilita il pulsante
        const backupBtn = document.querySelector('[onclick="createManualBackup()"]');
        if (backupBtn) {
            backupBtn.disabled = false;
            backupBtn.innerHTML = '<i class="fas fa-database me-1"></i>Backup Manuale';
        }
    }
}

function optimizeDatabase() {
    showNotification('Ottimizzazione database avviata...', 'info');
}

function checkDatabaseHealth() {
    showNotification('Verifica salute database in corso...', 'info');
}

function viewDatabaseStats() {
    showNotification('Caricamento statistiche database...', 'info');
}

function updatePasswordStatus() {
    const passwordField = document.getElementById('smtp-password');
    const passwordStatus = document.getElementById('password-status');
    const passwordHelpText = document.getElementById('password-help-text');

    if (!passwordField || !passwordStatus || !passwordHelpText) return;

    const value = passwordField.value.trim();

    if (value.length > 0) {
        // L'utente ha inserito una nuova password
        passwordStatus.innerHTML = '<i class="fas fa-edit text-primary" title="Nuova password inserita"></i>';
        passwordHelpText.textContent = 'Nuova password inserita. Verrà salvata quando clicchi "Salva Tutto".';
        passwordHelpText.className = 'form-text text-primary';
    } else {
        // Campo vuoto - controlla se c'era una password esistente
        const hasExistingPassword = passwordField.placeholder.includes('configurata');
        if (hasExistingPassword) {
            passwordStatus.innerHTML = '<i class="fas fa-check-circle text-success" title="Password configurata"></i>';
            passwordHelpText.textContent = 'Password già configurata. Inserisci una nuova password solo se vuoi modificarla.';
            passwordHelpText.className = 'form-text text-muted';
        } else {
            passwordStatus.innerHTML = '<i class="fas fa-exclamation-triangle text-warning" title="Password non configurata"></i>';
            passwordHelpText.textContent = 'Password SMTP richiesta per l\'invio email. Inserisci la password del tuo account email.';
            passwordHelpText.className = 'form-text text-warning';
        }
    }
}

// 🎨 Utility per notifiche SPETTACOLARI
function showNotification(message, type = 'info') {
    // Usa il sistema SNIP Messages se disponibile
    if (typeof window.snipMessages !== 'undefined') {
        switch(type) {
            case 'success': return window.snipMessages.success('🎉 Successo!', message);
            case 'error': return window.snipMessages.error('💥 Errore!', message);
            case 'warning': return window.snipMessages.warning('⚡ Attenzione!', message);
            default: return window.snipMessages.info('💫 Informazione', message);
        }
    }

    // Fallback: SweetAlert
    const icon = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';

    Swal.fire({
        icon: icon,
        title: message,
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true
    });
}
