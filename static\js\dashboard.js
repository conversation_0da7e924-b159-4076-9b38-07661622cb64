// Dashboard JavaScript

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize operational status chart
    initOperationalStatusChart();

    // Initialize voyage completion chart
    initVoyageCompletionChart();

    // Initialize navi per porto chart
    initNaviPerPortoChart();

    // Initialize counters with animation
    animateCounters();
});

// Function to animate counters
function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
        const target = +counter.getAttribute('data-target');
        const duration = 1500; // Animation duration in milliseconds
        const step = target / (duration / 16); // 60fps
        
        let count = 0;
        const updateCounter = () => {
            count += step;
            if (count < target) {
                counter.innerText = Math.floor(count);
                requestAnimationFrame(updateCounter);
            } else {
                counter.innerText = target;
            }
        };
        
        updateCounter();
    });
}

// Function to initialize operational status chart
function initOperationalStatusChart() {
    const ctx = document.getElementById('operationalStatusChart');
    
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['In Navigazione', 'In Porto', 'In Manutenzione', 'Ferme'],
            datasets: [{
                data: [12, 8, 3, 2],
                backgroundColor: [
                    '#4CAF50', // Green
                    '#2196F3', // Blue
                    '#FFC107', // Yellow
                    '#F44336'  // Red
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'Stato Operativo delle Navi',
                    font: {
                        size: 16
                    }
                }
            },
            cutout: '70%'
        }
    });
}

// Function to initialize voyage completion chart
function initVoyageCompletionChart() {
    const ctx = document.getElementById('voyageCompletionChart');
    
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio'],
            datasets: [{
                label: 'Viaggi Completati',
                data: [42, 38, 45, 50, 47],
                backgroundColor: '#3F51B5',
                borderWidth: 0
            }, {
                label: 'Viaggi Pianificati',
                data: [45, 42, 50, 55, 52],
                backgroundColor: '#E8EAF6',
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Completamento Viaggi',
                    font: {
                        size: 16
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Numero di Viaggi'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Mese'
                    }
                }
            }
        }
    });
}

// Function to initialize navi per porto chart
async function initNaviPerPortoChart() {
    const ctx = document.getElementById('naviPerPortoChart');

    if (!ctx) {
        console.warn('Canvas naviPerPortoChart non trovato');
        return;
    }

    try {
        console.log('Caricamento dati SOF realizzati per porto...');

        // Fetch data from API
        const response = await fetch('/api/dashboard/navi-per-porto');
        const result = await response.json();

        if (!result.success) {
            console.error('Errore API navi per porto:', result.error);
            // Mostra grafico con messaggio di errore
            showErrorChart(ctx, 'Errore nel caricamento dei dati SOF');
            return;
        }

        const data = result.data;
        console.log('Dati ricevuti:', data);

        // Se non ci sono dati, mostra grafico informativo
        if (!data.porti || data.porti.length === 0 ||
            (data.porti.length === 1 && (data.porti[0] === 'Nessun SOF' || data.porti[0] === 'Non Assegnato'))) {
            console.log('Nessun SOF realizzato trovato, mostro grafico informativo');
            showNoDataChart(ctx);
            showInfoMessage(true);
            updateNaviPerPortoStats({
                sof_realizzati: [0],
                navi_distinte: [0],
                giorni_operativi: [0],
                porti: ['Nessun dato']
            });
            return;
        }

        // Nascondi messaggio informativo se ci sono dati
        showInfoMessage(false);

        // Update statistics
        updateNaviPerPortoStats(data);

        // Create chart
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.porti,
                datasets: [
                    {
                        label: 'SOF Realizzati',
                        data: data.sof_realizzati,
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false,
                    },
                    {
                        label: 'Navi Utilizzate',
                        data: data.navi_distinte,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false,
                    },
                    {
                        label: 'Giorni Operativi',
                        data: data.giorni_operativi,
                        backgroundColor: 'rgba(255, 193, 7, 0.8)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false,
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12,
                                weight: 'bold'
                            },
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    title: {
                        display: true,
                        text: 'SOF Realizzati per Porto di Gestione',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 30
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            title: function(context) {
                                return 'Porto: ' + context[0].label;
                            },
                            label: function(context) {
                                const label = context.dataset.label;
                                const value = context.parsed.y;
                                return label + ': ' + value;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Numero',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            stepSize: 1,
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Porto di Gestione',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0,
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        console.log('Grafico SOF realizzati per porto inizializzato');

    } catch (error) {
        console.error('Errore caricamento grafico SOF per porto:', error);
        showErrorChart(ctx, 'Errore di connessione');
    }
}

// Function to show error chart
function showErrorChart(ctx, errorMessage) {
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Errore'],
            datasets: [{
                label: 'Dati non disponibili',
                data: [1],
                backgroundColor: 'rgba(220, 53, 69, 0.5)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 2,
                borderRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: errorMessage,
                    color: '#dc3545',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                }
            },
            scales: {
                y: {
                    display: false
                },
                x: {
                    display: false
                }
            }
        }
    });
}

// Function to show no data chart
function showNoDataChart(ctx) {
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Nessun SOF Realizzato', 'SOF da Completare'],
            datasets: [{
                data: [1, 3],
                backgroundColor: [
                    'rgba(108, 117, 125, 0.5)',
                    'rgba(40, 167, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(108, 117, 125, 1)',
                    'rgba(40, 167, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'Nessun SOF Realizzato Trovato',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: {
                        top: 10,
                        bottom: 20
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.label === 'Nessun SOF Realizzato') {
                                return 'Completa alcuni viaggi per vedere i dati';
                            }
                            return 'Viaggi ancora da completare';
                        }
                    }
                }
            }
        }
    });
}

// Function to update SOF per porto statistics
function updateNaviPerPortoStats(data) {
    try {
        // Calculate totals
        const totalSofRealizzati = data.sof_realizzati.reduce((sum, val) => sum + val, 0);
        const totalNaviUtilizzate = data.navi_distinte.reduce((sum, val) => sum + val, 0);
        const totalPortiOperativi = data.porti.filter(porto => porto !== 'Nessun SOF' && porto !== 'Errore').length;
        const totalGiorniOperativi = data.giorni_operativi.reduce((sum, val) => sum + val, 0);

        // Update DOM elements with animation
        animateValue('total-sof-realizzati', totalSofRealizzati);
        animateValue('total-navi-utilizzate', totalNaviUtilizzate);
        animateValue('total-porti-operativi', totalPortiOperativi);
        animateValue('total-giorni-operativi', totalGiorniOperativi);

        console.log('Statistiche SOF aggiornate:', {
            sof_realizzati: totalSofRealizzati,
            navi_utilizzate: totalNaviUtilizzate,
            porti_operativi: totalPortiOperativi,
            giorni_operativi: totalGiorniOperativi
        });

    } catch (error) {
        console.error('Errore aggiornamento statistiche SOF:', error);

        // Set fallback values
        document.getElementById('total-sof-realizzati').textContent = '0';
        document.getElementById('total-navi-utilizzate').textContent = '0';
        document.getElementById('total-porti-operativi').textContent = '0';
        document.getElementById('total-giorni-operativi').textContent = '0';
    }
}

// Function to animate counter values
function animateValue(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const startValue = 0;
    const duration = 1500;
    const startTime = performance.now();

    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

        element.textContent = currentValue;

        if (progress < 1) {
            requestAnimationFrame(updateValue);
        } else {
            element.textContent = targetValue;
        }
    }

    requestAnimationFrame(updateValue);
}

// Function to show/hide info message
function showInfoMessage(show) {
    const messageElement = document.getElementById('sof-info-message');
    if (messageElement) {
        messageElement.style.display = show ? 'block' : 'none';

        // Aggiungi event listener per il pulsante di test se mostrato
        if (show) {
            const testBtn = document.getElementById('create-test-data-btn');
            if (testBtn && !testBtn.hasAttribute('data-listener-added')) {
                testBtn.setAttribute('data-listener-added', 'true');
                testBtn.addEventListener('click', createTestSOFData);
            }
        }
    }
}

// Function to create test SOF data
async function createTestSOFData() {
    const btn = document.getElementById('create-test-data-btn');
    if (!btn) return;

    // Disabilita il pulsante durante la richiesta
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creando...';

    try {
        console.log('Creazione dati test SOF...');

        const response = await fetch('/api/dashboard/create-test-sof', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            console.log('Dati test SOF creati con successo:', result);

            // Mostra messaggio di successo
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Creati!';
            btn.className = 'btn btn-sm btn-success';

            // Ricarica il grafico dopo 1 secondo
            setTimeout(() => {
                console.log('Ricaricamento grafico con nuovi dati...');
                initNaviPerPortoChart();
            }, 1000);

        } else {
            console.error('Errore creazione dati test:', result.message);

            // Mostra messaggio di errore
            btn.innerHTML = '<i class="fas fa-exclamation me-1"></i>Errore';
            btn.className = 'btn btn-sm btn-danger';

            // Ripristina dopo 3 secondi
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-flask me-1"></i>Crea Dati Test';
                btn.className = 'btn btn-sm btn-outline-primary';
                btn.disabled = false;
            }, 3000);
        }

    } catch (error) {
        console.error('Errore richiesta creazione dati test:', error);

        // Mostra errore
        btn.innerHTML = '<i class="fas fa-times me-1"></i>Errore';
        btn.className = 'btn btn-sm btn-danger';

        // Ripristina dopo 3 secondi
        setTimeout(() => {
            btn.innerHTML = '<i class="fas fa-flask me-1"></i>Crea Dati Test';
            btn.className = 'btn btn-sm btn-outline-primary';
            btn.disabled = false;
        }, 3000);
    }
}
