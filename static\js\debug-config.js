/**
 * Script di debug per testare il salvataggio delle configurazioni
 * Utilizzare nella console del browser per testare il sistema
 */

// Funzione per testare il salvataggio delle configurazioni
function debugTestSaveConfigurations() {
    console.log('🧪 DEBUG: Test salvataggio configurazioni...');
    
    // Configurazioni di test
    const testConfigurations = {
        database: {
            backup_time: "04:00",
            backup_retention: 60,
            backup_schedule: "weekly",
            compress_backup: true,
            backup_path: "/backups/debug/",
            log_cleanup: "daily",
            archive_months: 3,
            optimize: "daily",
            auto_vacuum: true,
            analyze: true,
            disk_threshold: 95,
            connection_threshold: 70,
            monitor_performance: true,
            alert_email: true
        },
        email: {
            smtp_host: "smtp.debug.com",
            smtp_port: 465,
            smtp_username: "<EMAIL>",
            smtp_password: "debug123",
            sender_email: "<EMAIL>",
            sender_name: "Debug System",
            admin_email: "<EMAIL>",
            smtp_ssl: true
        },
        ports: {
            salerno: {
                name: "Porto di Salerno DEBUG",
                code: "ITSAL",
                harbor: "+39 089 111111",
                customs: "+39 089 222222",
                email: "<EMAIL>"
            },
            gioia: {
                name: "Porto di Gioia Tauro DEBUG",
                code: "ITGIT",
                harbor: "+39 0966 111111",
                customs: "+39 0966 222222",
                email: "<EMAIL>"
            }
        },
        security: {
            password_min_length: 10,
            password_expiry_days: 60,
            session_timeout: 30,
            max_login_attempts: 2,
            account_lockout_minutes: 60,
            log_retention: 365,
            password_uppercase: true,
            password_numbers: true,
            password_special: true,
            two_factor_auth: false,
            ip_whitelist: true,
            log_login: true,
            log_actions: true,
            log_errors: true,
            encrypt_backups: true,
            ssl_only: true
        },
        sof: {
            title: "DEBUG STATEMENT OF FACTS",
            subtitle: "debug shipping agency",
            logo_size: "small",
            numbering: "auto",
            logo_top: false,
            logo_bottom: true
        },
        interface: {
            theme: "light",
            primary_color: "#purple",
            secondary_color: "#orange",
            accent_color: "#green",
            language: "it",
            timezone: "Europe/Rome",
            date_format: "yyyy-mm-dd",
            sidebar_default: "auto",
            responsive_design: false,
            dark_mode: false,
            glassmorphism: false
        },
        reporting: {
            daily_report: "disabled",
            daily_report_time: "06:00",
            weekly_report: "disabled",
            monthly_report: "disabled",
            kpi_voyages: false,
            kpi_sof: false,
            kpi_users: true,
            kpi_ports: false,
            kpi_performance: true,
            kpi_revenue: false,
            export_format: "csv",
            auto_export: false,
            compress_exports: true,
            report_recipients: "<EMAIL>",
            include_charts: true,
            detailed_reports: false
        },
        system: {
            app_version: "0.9.0",
            debug_mode: "disabled",
            log_level: "ERROR",
            max_upload_size: 5,
            request_timeout: 15,
            max_connections: 50,
            cache_enabled: true,
            compression_enabled: true,
            maintenance_mode: "disabled",
            maintenance_message: "Debug maintenance",
            auto_updates: false
        }
    };
    
    console.log('📦 Configurazioni di test:', testConfigurations);
    
    // Invia la richiesta
    fetch('/admin/api/configurations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testConfigurations)
    })
    .then(response => {
        console.log('📡 Risposta ricevuta:', response.status, response.statusText);
        return response.json();
    })
    .then(data => {
        console.log('✅ Risultato salvataggio:', data);
        if (data.success) {
            console.log('🎉 Salvataggio riuscito!');
            console.log('📊 Configurazioni salvate:', data.saved_configs);
        } else {
            console.error('❌ Errore nel salvataggio:', data.message);
        }
    })
    .catch(error => {
        console.error('💥 Errore di rete:', error);
    });
}

// Funzione per testare il caricamento delle configurazioni
function debugTestLoadConfigurations() {
    console.log('🔍 DEBUG: Test caricamento configurazioni...');
    
    fetch('/admin/api/configurations')
    .then(response => {
        console.log('📡 Risposta ricevuta:', response.status, response.statusText);
        return response.json();
    })
    .then(data => {
        console.log('✅ Risultato caricamento:', data);
        if (data.success) {
            console.log('🎉 Caricamento riuscito!');
            console.log('📊 Configurazioni caricate:');
            
            // Mostra le configurazioni per sezione
            Object.keys(data.configurations).forEach(section => {
                const config = data.configurations[section];
                const count = typeof config === 'object' ? Object.keys(config).length : 0;
                console.log(`  📋 ${section}: ${count} parametri`, config);
            });
        } else {
            console.error('❌ Errore nel caricamento:', data.message);
        }
    })
    .catch(error => {
        console.error('💥 Errore di rete:', error);
    });
}

// Funzione per verificare lo stato dei campi del form
function debugCheckFormFields() {
    console.log('🔍 DEBUG: Verifica campi form...');
    
    const sections = ['database', 'email', 'ports', 'security', 'sof', 'interface', 'reporting', 'system'];
    
    sections.forEach(section => {
        console.log(`📋 Sezione ${section}:`);
        
        // Cerca tutti i campi input nella sezione
        const sectionElement = document.querySelector(`#${section}-config`);
        if (sectionElement) {
            const inputs = sectionElement.querySelectorAll('input, select, textarea');
            console.log(`  🔧 Trovati ${inputs.length} campi`);
            
            inputs.forEach(input => {
                const value = input.type === 'checkbox' ? input.checked : input.value;
                console.log(`    ${input.id || input.name}: ${value}`);
            });
        } else {
            console.log(`  ❌ Sezione non trovata nel DOM`);
        }
    });
}

// Funzione per popolare i campi con dati di test
function debugPopulateTestData() {
    console.log('🧪 DEBUG: Popolamento dati di test...');
    
    // Popola alcuni campi di test
    const testFields = {
        'db-backup-time': '05:00',
        'db-backup-retention': '90',
        'smtp-host': 'smtp.test.it',
        'smtp-port': '587',
        'salerno-name': 'Porto Test Salerno',
        'sof-title': 'TEST SOF TITLE'
    };
    
    Object.keys(testFields).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.value = testFields[fieldId];
            console.log(`✅ Campo ${fieldId} popolato con: ${testFields[fieldId]}`);
            
            // Trigger change event
            field.dispatchEvent(new Event('change'));
        } else {
            console.log(`❌ Campo ${fieldId} non trovato`);
        }
    });
}

// Esporta le funzioni per uso globale
window.debugTestSaveConfigurations = debugTestSaveConfigurations;
window.debugTestLoadConfigurations = debugTestLoadConfigurations;
window.debugCheckFormFields = debugCheckFormFields;
window.debugPopulateTestData = debugPopulateTestData;

console.log('🔧 Script di debug configurazioni caricato!');
console.log('📝 Funzioni disponibili:');
console.log('  - debugTestSaveConfigurations()');
console.log('  - debugTestLoadConfigurations()');
console.log('  - debugCheckFormFields()');
console.log('  - debugPopulateTestData()');
