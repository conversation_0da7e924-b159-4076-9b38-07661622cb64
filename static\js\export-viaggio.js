// JavaScript per gestione export dati viaggio
document.addEventListener('DOMContentLoaded', function() {
    const exportForm = document.getElementById('exportForm');
    const fileInput = document.getElementById('file_upload_export');
    const previewContainer = document.getElementById('preview_container_export');
    const validateBtn = document.getElementById('validate_btn_export');
    const exportBtn = document.getElementById('export_btn');
    const exportStatusText = document.getElementById('export_status_text');
    const rowsCount = document.getElementById('rows_count_export');
    const exportLog = document.getElementById('export_log');
    const deleteExportBtn = document.getElementById('delete_export_btn');
    const forceExportGreenCheckbox = document.getElementById('force_export_green');

    let uploadedData = null;
    let validatedData = null;

    // Gestione upload file
    exportForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!fileInput.files[0]) {
            updateStatus('Seleziona un file', 'error');
            addLog('❌ Nessun file selezionato');
            return;
        }

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        uploadFile(formData);
    });

    // Funzione upload file
    function uploadFile(formData) {
        updateStatus('Caricamento file...', 'warning');
        addLog('📤 Inizio caricamento file...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/export/upload`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                uploadedData = data.data;
                showPreview(data.data, data.columns);
                updateStatus('File caricato', 'success');
                addLog(`✅ File caricato con successo: ${data.rows} righe`);
                rowsCount.textContent = data.rows;
                validateBtn.disabled = false;
                updateExportStatusIndicator('green');
            } else {
                updateStatus('Errore caricamento', 'error');
                addLog(`❌ Errore: ${data.message}`);
                updateExportStatusIndicator('red');
            }
        })
        .catch(error => {
            updateStatus('Errore caricamento', 'error');
            addLog(`❌ Errore di rete: ${error.message}`);
            updateExportStatusIndicator('red');
        });
    }

    // Mostra anteprima dati
    function showPreview(data, columns) {
        let html = '<div class="table-responsive">';
        html += '<table class="table table-sm table-striped">';

        // Header
        html += '<thead class="table-dark"><tr>';
        columns.forEach(col => {
            html += `<th class="small">${col}</th>`;
        });
        html += '</tr></thead>';

        // Righe (max 10 per anteprima)
        html += '<tbody>';
        const maxRows = Math.min(data.length, 10);
        for (let i = 0; i < maxRows; i++) {
            html += '<tr>';
            columns.forEach(col => {
                const value = data[i][col] || '';
                html += `<td class="small">${value}</td>`;
            });
            html += '</tr>';
        }
        html += '</tbody></table>';

        if (data.length > 10) {
            html += `<small class="text-muted">Mostrate prime 10 righe di ${data.length}</small>`;
        }

        html += '</div>';
        previewContainer.innerHTML = html;
    }

    // Validazione dati
    validateBtn.addEventListener('click', function() {
        if (!uploadedData) return;

        updateStatus('Validazione in corso...', 'warning');
        addLog('🔍 Inizio validazione dati...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/export/validate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: uploadedData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                validatedData = data.validated_data;
                updateStatus('Dati validati', 'success');
                addLog(`✅ Validazione completata: ${data.valid_rows} righe valide`);

                // Mostra messaggio di successo moderno
                if (typeof mostraSuccesso === 'function') {
                    if (data.errors && data.errors.length > 0) {
                        mostraAvviso(`Validazione completata: ${data.valid_rows} righe valide, ${data.errors.length} avvisi`);
                    } else {
                        mostraSuccesso(`Validazione completata: ${data.valid_rows} righe valide`);
                    }
                }

                if (data.errors && data.errors.length > 0) {
                    data.errors.forEach(error => {
                        addLog(`⚠️ Riga ${error.row}: ${error.message}`);
                    });
                }

                exportBtn.disabled = false;
            } else {
                updateStatus('Errori validazione', 'error');
                addLog(`❌ Validazione fallita: ${data.message}`);

                // Mostra messaggio di errore moderno
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante la validazione');
                }

                updateExportStatusIndicator('red');
            }
        })
        .catch(error => {
            updateStatus('Errore validazione', 'error');
            addLog(`❌ Errore validazione: ${error.message}`);

            // Mostra messaggio di errore moderno
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante la validazione');
            }

            updateExportStatusIndicator('red');
        });
    });

    // Export dati
    exportBtn.addEventListener('click', function() {
        if (!validatedData) return;

        updateStatus('Export in corso...', 'warning');
        addLog('💾 Inizio export dati nel database...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/export/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: validatedData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatus('Export completato', 'success');
                addLog(`🎉 Export completato con successo: ${data.exported_rows} righe esportate`);

                // Mostra messaggio di successo moderno
                if (typeof mostraSuccesso === 'function') {
                    mostraSuccesso(`Export completato: ${data.exported_rows} righe esportate`);
                }

                // Reset form
                resetExportForm();

                // Carica dati EXPORT aggiornati
                addLog('📦 Caricamento dati EXPORT...');
                loadExportData();

                updateExportStatusIndicator('green');
            } else {
                updateStatus('Errore export', 'error');
                addLog(`❌ Export fallito: ${data.message}`);

                // Mostra messaggio di errore moderno
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante l\'export');
                }

                updateExportStatusIndicator('red');
            }
        })
        .catch(error => {
            updateStatus('Errore export', 'error');
            addLog(`❌ Errore export: ${error.message}`);

            // Mostra messaggio di errore moderno
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante l\'export');
            }

            updateExportStatusIndicator('red');
        });
    });

    // Elimina dati EXPORT
    deleteExportBtn.addEventListener('click', function() {
        // Mostra dialog di conferma moderno
        mostraConfermaEliminazione(() => {
            eseguiEliminazione();
        });
    });

    function eseguiEliminazione() {

        updateStatus('Eliminazione in corso...', 'warning');
        addLog('🗑️ Inizio eliminazione dati EXPORT...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/export/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatus('Dati eliminati', 'success');
                addLog(`🎉 Eliminazione completata: ${data.deleted_rows} record eliminati`);

                // Mostra messaggio di successo moderno
                if (typeof mostraSuccesso === 'function') {
                    mostraSuccesso(`Eliminati ${data.deleted_rows} record EXPORT`);
                }

                // Aggiorna visualizzazione
                loadExportData();

                // Solo se il checkbox non è spuntato, metti rosso
                const forceGreenCheckbox = document.getElementById('force_export_green');
                if (!forceGreenCheckbox || !forceGreenCheckbox.checked) {
                    updateExportStatusIndicator('red');
                }
            } else {
                updateStatus('Errore eliminazione', 'error');
                addLog(`❌ Eliminazione fallita: ${data.message}`);

                // Mostra messaggio di errore moderno
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante l\'eliminazione');
                }
            }
        })
        .catch(error => {
            updateStatus('Errore eliminazione', 'error');
            addLog(`❌ Errore eliminazione: ${error.message}`);

            // Mostra messaggio di errore moderno
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante l\'eliminazione');
            }
        });
    }

    // Funzioni utility
    function updateStatus(text, type) {
        exportStatusText.textContent = text;
        exportStatusText.className = type === 'success' ? 'text-success' :
                                    type === 'error' ? 'text-danger' :
                                    type === 'warning' ? 'text-warning' : '';
    }

    function addLog(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `<small class="text-white-50">[${timestamp}]</small> ${message}`;
        logEntry.style.marginBottom = '5px';

        exportLog.appendChild(logEntry);
        exportLog.scrollTop = exportLog.scrollHeight;
    }

    function resetExportForm() {
        exportForm.reset();
        uploadedData = null;
        validatedData = null;
        validateBtn.disabled = true;
        exportBtn.disabled = true;
        rowsCount.textContent = '0';

        previewContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                <p class="mb-0">Carica un file per vedere l'anteprima</p>
            </div>
        `;
    }

    // Event listener per il checkbox "Forza pallino verde" - VERSIONE SEMPLICE
    setTimeout(function() {
        console.log('🔧 Inizializzazione checkbox FORZA PALLINO VERDE EXPORT...');

        const checkbox = document.getElementById('force_export_green');
        console.log('Checkbox export trovato:', !!checkbox);

        if (checkbox) {
            checkbox.addEventListener('change', function() {
                console.log('Checkbox export cambiato:', this.checked);

                if (this.checked) {
                    // Forza pallino verde
                    const pallino = document.getElementById('export-status');
                    if (pallino) {
                        pallino.className = 'status-indicator status-green';
                        pallino.style.backgroundColor = '#28a745';
                        pallino.style.boxShadow = '0 0 5px rgba(40, 167, 69, 0.5)';
                        console.log('Pallino export forzato a verde');
                        addLog('🟢 Pallino EXPORT forzato a verde');
                        updateStatus('Stato forzato a verde', 'success');
                    }
                } else {
                    // Ricontrolla stato normale
                    console.log('Ricontrollo stato normale export');
                    loadExportData();
                    addLog('🔄 Controllo stato reale dati');
                }
            });
            console.log('Event listener export aggiunto');
        } else {
            console.error('Checkbox export non trovato!');
        }
    }, 1000);

    // Carica dati EXPORT esistenti all'avvio (aggiorna anche la lucina)
    loadExportData();
});

// Funzione globale per aggiornare la lucina EXPORT
function updateExportStatusIndicator(color) {
    const exportStatus = document.getElementById('export-status');
    if (exportStatus) {
        exportStatus.className = `status-indicator status-${color === 'green' ? 'green' : 'red'}`;
        console.log(`🔄 Lucina EXPORT aggiornata: ${color}`);

        // Controlla se mostrare il tab SOF
        if (typeof updateStatusAndCheck === 'function') {
            updateStatusAndCheck();
        }
    } else {
        console.error('❌ Elemento export-status non trovato');
    }
}

// Funzione per caricare i dati EXPORT
function loadExportData() {
    fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/export/data`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showExportData(data.data);

                // Controlla se il checkbox "forza verde" è spuntato
                const forceGreenCheckbox = document.getElementById('force_export_green');

                if (forceGreenCheckbox && forceGreenCheckbox.checked) {
                    // Se il checkbox è spuntato, mantieni il pallino verde
                    updateExportStatusIndicator('green');
                    console.log('🟢 Pallino EXPORT mantenuto verde (forzato)');
                } else {
                    // Altrimenti aggiorna lucina in base ai dati presenti
                    if (data.data && data.data.length > 0) {
                        updateExportStatusIndicator('green');
                        console.log(`✅ Dati EXPORT presenti: ${data.data.length} record`);
                    } else {
                        updateExportStatusIndicator('red');
                        console.log('❌ Nessun dato EXPORT presente');
                    }
                }
            } else {
                console.error('Errore caricamento dati EXPORT:', data.message);
                // Solo se il checkbox non è spuntato, metti rosso
                const forceGreenCheckbox = document.getElementById('force_export_green');
                if (!forceGreenCheckbox || !forceGreenCheckbox.checked) {
                    updateExportStatusIndicator('red');
                }
            }
        })
        .catch(error => {
            console.error('Errore rete caricamento EXPORT:', error);
            // Solo se il checkbox non è spuntato, metti rosso
            const forceGreenCheckbox = document.getElementById('force_export_green');
            if (!forceGreenCheckbox || !forceGreenCheckbox.checked) {
                updateExportStatusIndicator('red');
            }
        });
}

// Funzione per mostrare i dati EXPORT
function showExportData(exportData) {
    const container = document.getElementById('export_data_container');

    if (!exportData || exportData.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                <p class="mb-0">Nessun dato EXPORT presente</p>
            </div>
        `;
        return;
    }

    // Raggruppa i dati per POD
    const groupedByPod = {};
    exportData.forEach(row => {
        if (!groupedByPod[row.pod]) {
            groupedByPod[row.pod] = [];
        }
        groupedByPod[row.pod].push(row);
    });

    let html = '<div class="table-responsive">';
    html += '<table class="table table-sm table-striped table-hover">';

    // Header
    html += `
        <thead class="table-dark">
            <tr>
                <th class="small">POL</th>
                <th class="small">POD</th>
                <th class="small">QT</th>
                <th class="small">TYPE</th>
                <th class="small">DATA</th>
            </tr>
        </thead>
    `;

    // Righe dati raggruppate per POD
    html += '<tbody>';

    Object.keys(groupedByPod).forEach(pod => {
        const podRows = groupedByPod[pod];

        podRows.forEach((row, index) => {
            html += `
                <tr>
                    <td class="small fw-bold text-primary">${row.pol}</td>
                    <td class="small fw-bold text-success ${index === 0 ? 'border-top-3' : ''}"
                        style="${index === 0 ? 'border-top: 3px solid #28a745 !important;' : ''}">${row.pod}</td>
                    <td class="small text-center">${row.qt}</td>
                    <td class="small">${row.type}</td>
                    <td class="small text-muted">${row.created_at}</td>
                </tr>
            `;
        });

        // Aggiungi riga separatrice tra gruppi POD diversi
        if (Object.keys(groupedByPod).indexOf(pod) < Object.keys(groupedByPod).length - 1) {
            html += `
                <tr style="height: 8px; background-color: #f8f9fa;">
                    <td colspan="5" style="border: none; padding: 2px;"></td>
                </tr>
            `;
        }
    });

    html += '</tbody></table>';

    // Statistiche
    const totalQt = exportData.reduce((sum, row) => sum + row.qt, 0);
    const uniquePods = Object.keys(groupedByPod).length;
    html += `
        <div class="mt-2 p-2 bg-light rounded">
            <small class="text-muted">
                <strong>Totale record:</strong> ${exportData.length} |
                <strong>POD diversi:</strong> ${uniquePods} |
                <strong>Quantità totale:</strong> ${totalQt}
            </small>
        </div>
    `;

    html += '</div>';
    container.innerHTML = html;
}
