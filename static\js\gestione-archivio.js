/**
 * JavaScript per la gestione dell'archiviazione SOF
 * File separato per evitare conflitti di sintassi
 */

console.log('🔧 Gestione Archivio JS caricato - VERSIONE SEPARATA 2024-06-24-16:00');

// Gestione campi dinamici per tipo periodo
function aggiornaCampiPeriodo() {
    console.log('🔄 aggiornaCampiPeriodo() chiamata');
    
    const tipoPeriodo = document.getElementById('tipoPeriodo').value;
    const campiPeriodo = document.getElementById('campiPeriodo');
    const btnArchivia = document.getElementById('btnArchiviaPeriodo');

    if (!campiPeriodo) {
        console.error('❌ Elemento campiPeriodo non trovato');
        return;
    }

    campiPeriodo.innerHTML = '';
    if (btnArchivia) {
        btnArchivia.disabled = true;
    }

    if (tipoPeriodo === 'anno') {
        // Genera anni dinamicamente in JavaScript
        const currentYear = new Date().getFullYear();
        const anni = [];
        for (let i = currentYear + 1; i >= currentYear - 10; i--) {
            anni.push(i);
        }
        
        let opzioniAnni = '<option value="">Seleziona anno</option>';
        anni.forEach(anno => {
            opzioniAnni += `<option value="${anno}">${anno}</option>`;
        });
        
        campiPeriodo.innerHTML = `
            <div class="mb-3">
                <label class="form-label">Anno</label>
                <select class="form-select" id="annoSelezionato" onchange="validaCampiPeriodo()">
                    ${opzioniAnni}
                </select>
            </div>
        `;
        console.log('✅ Campi anno generati');
        
    } else if (tipoPeriodo === 'mese') {
        // Genera anni dinamicamente in JavaScript
        const currentYear = new Date().getFullYear();
        const anni = [];
        for (let i = currentYear + 1; i >= currentYear - 10; i--) {
            anni.push(i);
        }
        
        let opzioniAnni = '<option value="">Anno</option>';
        anni.forEach(anno => {
            opzioniAnni += `<option value="${anno}">${anno}</option>`;
        });
        
        campiPeriodo.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <label class="form-label">Anno</label>
                    <select class="form-select" id="annoMese" onchange="validaCampiPeriodo()">
                        ${opzioniAnni}
                    </select>
                </div>
                <div class="col-6">
                    <label class="form-label">Mese</label>
                    <select class="form-select" id="meseSelezionato" onchange="validaCampiPeriodo()">
                        <option value="">Mese</option>
                        <option value="1">Gennaio</option>
                        <option value="2">Febbraio</option>
                        <option value="3">Marzo</option>
                        <option value="4">Aprile</option>
                        <option value="5">Maggio</option>
                        <option value="6">Giugno</option>
                        <option value="7">Luglio</option>
                        <option value="8">Agosto</option>
                        <option value="9">Settembre</option>
                        <option value="10">Ottobre</option>
                        <option value="11">Novembre</option>
                        <option value="12">Dicembre</option>
                    </select>
                </div>
            </div>
        `;
        console.log('✅ Campi mese generati');
        
    } else if (tipoPeriodo === 'trimestre') {
        // Genera anni dinamicamente in JavaScript
        const currentYear = new Date().getFullYear();
        const anni = [];
        for (let i = currentYear + 1; i >= currentYear - 10; i--) {
            anni.push(i);
        }
        
        let opzioniAnni = '<option value="">Anno</option>';
        anni.forEach(anno => {
            opzioniAnni += `<option value="${anno}">${anno}</option>`;
        });
        
        campiPeriodo.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <label class="form-label">Anno</label>
                    <select class="form-select" id="annoTrimestre" onchange="validaCampiPeriodo()">
                        ${opzioniAnni}
                    </select>
                </div>
                <div class="col-6">
                    <label class="form-label">Trimestre</label>
                    <select class="form-select" id="trimestreSelezionato" onchange="validaCampiPeriodo()">
                        <option value="">Trimestre</option>
                        <option value="1">Q1 (Gen-Mar)</option>
                        <option value="2">Q2 (Apr-Giu)</option>
                        <option value="3">Q3 (Lug-Set)</option>
                        <option value="4">Q4 (Ott-Dic)</option>
                    </select>
                </div>
            </div>
        `;
        console.log('✅ Campi trimestre generati');
        
    } else if (tipoPeriodo === 'giorni') {
        campiPeriodo.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <label class="form-label">Data Inizio</label>
                    <input type="date" class="form-control" id="dataInizio" onchange="validaCampiPeriodo()">
                </div>
                <div class="col-6">
                    <label class="form-label">Data Fine</label>
                    <input type="date" class="form-control" id="dataFine" onchange="validaCampiPeriodo()">
                </div>
            </div>
        `;
        console.log('✅ Campi range giorni generati');
    }
}

// Validazione campi periodo
function validaCampiPeriodo() {
    const tipoPeriodo = document.getElementById('tipoPeriodo').value;
    const btnArchivia = document.getElementById('btnArchiviaPeriodo');
    let valido = false;

    if (tipoPeriodo === 'anno') {
        const anno = document.getElementById('annoSelezionato');
        valido = anno && anno.value !== '';
    } else if (tipoPeriodo === 'mese') {
        const anno = document.getElementById('annoMese');
        const mese = document.getElementById('meseSelezionato');
        valido = anno && anno.value !== '' && mese && mese.value !== '';
    } else if (tipoPeriodo === 'trimestre') {
        const anno = document.getElementById('annoTrimestre');
        const trimestre = document.getElementById('trimestreSelezionato');
        valido = anno && anno.value !== '' && trimestre && trimestre.value !== '';
    } else if (tipoPeriodo === 'giorni') {
        const dataInizio = document.getElementById('dataInizio');
        const dataFine = document.getElementById('dataFine');
        valido = dataInizio && dataInizio.value !== '' && dataFine && dataFine.value !== '';
    }

    if (btnArchivia) {
        btnArchivia.disabled = !valido;
    }
    
    console.log(`🔧 Validazione: ${valido ? 'OK' : 'Campi incompleti'}`);
}

// Funzioni per selezione viaggi
function selezionaTutti() {
    console.log('✅ selezionaTutti() chiamata');
    const checkboxes = document.querySelectorAll('input[name="viaggi_selezionati"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    aggiornaContatoreSelezione();
}

function deselezionaTutti() {
    console.log('✅ deselezionaTutti() chiamata');
    const checkboxes = document.querySelectorAll('input[name="viaggi_selezionati"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    aggiornaContatoreSelezione();
}

function aggiornaContatoreSelezione() {
    const checkboxes = document.querySelectorAll('input[name="viaggi_selezionati"]:checked');
    const contatore = document.getElementById('contatoreSelezionati');
    const btnArchivia = document.getElementById('btnArchiviaSelezionati');
    
    if (contatore) {
        contatore.textContent = checkboxes.length;
    }
    
    if (btnArchivia) {
        btnArchivia.disabled = checkboxes.length === 0;
    }
}

// Test connessione API
async function testConnessioneAPI() {
    console.log('🧪 testConnessioneAPI() chiamata');
    
    try {
        const response = await fetch('/api/operativo/sof/archivia-json/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                periodo_tipo: 'anno',
                periodo_valore: '2024'
            })
        });
        
        console.log(`📡 Response status: ${response.status}`);
        const result = await response.json();
        console.log(`📊 Response:`, result);
        
        if (response.status === 200 && result.success) {
            alert('✅ Test API riuscito! Endpoint funzionante.');
        } else {
            alert(`⚠️ Test API - Errore ${response.status}: ${result.message || 'Errore sconosciuto'}`);
        }
        
    } catch (error) {
        console.error('❌ Errore test API:', error);
        alert(`❌ Errore connessione API: ${error.message}`);
    }
}

// Archiviazione per periodo
async function archiviaPerPeriodo() {
    console.log('🗂️ archiviaPerPeriodo() chiamata');

    const tipoPeriodo = document.getElementById('tipoPeriodo').value;
    if (!tipoPeriodo) {
        alert('⚠️ Seleziona un tipo di periodo');
        return;
    }

    // Raccogli parametri in base al tipo periodo
    let parametri = { periodo_tipo: tipoPeriodo };
    let descrizioneOperazione = '';

    if (tipoPeriodo === 'anno') {
        const anno = document.getElementById('annoSelezionato').value;
        if (!anno) {
            alert('⚠️ Seleziona un anno');
            return;
        }
        parametri.periodo_valore = anno;
        descrizioneOperazione = `tutti i viaggi dell'anno ${anno}`;

    } else if (tipoPeriodo === 'mese') {
        const anno = document.getElementById('annoMese').value;
        const mese = document.getElementById('meseSelezionato').value;
        if (!anno || !mese) {
            alert('⚠️ Seleziona anno e mese');
            return;
        }
        parametri.anno = anno;
        parametri.mese = mese;
        const nomiMesi = ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
                         'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
        descrizioneOperazione = `tutti i viaggi di ${nomiMesi[mese-1]} ${anno}`;

    } else if (tipoPeriodo === 'trimestre') {
        const anno = document.getElementById('annoTrimestre').value;
        const trimestre = document.getElementById('trimestreSelezionato').value;
        if (!anno || !trimestre) {
            alert('⚠️ Seleziona anno e trimestre');
            return;
        }
        parametri.anno = anno;
        parametri.trimestre = trimestre;
        descrizioneOperazione = `tutti i viaggi del Q${trimestre} ${anno}`;

    } else if (tipoPeriodo === 'giorni') {
        const dataInizio = document.getElementById('dataInizio').value;
        const dataFine = document.getElementById('dataFine').value;
        if (!dataInizio || !dataFine) {
            alert('⚠️ Seleziona data inizio e fine');
            return;
        }
        parametri.data_inizio = dataInizio;
        parametri.data_fine = dataFine;
        descrizioneOperazione = `tutti i viaggi dal ${dataInizio} al ${dataFine}`;
    }

    // Conferma operazione
    const conferma = confirm(
        `⚠️ ATTENZIONE: Stai per archiviare ${descrizioneOperazione}.\n\n` +
        `Questa operazione:\n` +
        `• Cancellerà DEFINITIVAMENTE i viaggi dal database\n` +
        `• Creerà un file JSON di backup\n` +
        `• NON può essere annullata\n\n` +
        `Sei sicuro di voler continuare?`
    );

    if (!conferma) {
        console.log('❌ Operazione annullata dall\'utente');
        return;
    }

    console.log('📊 Parametri archiviazione:', parametri);
    await eseguiArchiviazione(parametri, descrizioneOperazione);
}

// Archiviazione selettiva
async function archiviaSelezionati() {
    console.log('🗂️ archiviaSelezionati() chiamata');

    const checkboxes = document.querySelectorAll('input[name="viaggi_selezionati"]:checked');
    const viaggiIds = Array.from(checkboxes).map(cb => cb.value);

    if (viaggiIds.length === 0) {
        alert('⚠️ Seleziona almeno un viaggio');
        return;
    }

    const conferma = confirm(
        `⚠️ ATTENZIONE: Stai per archiviare ${viaggiIds.length} viaggi selezionati.\n\n` +
        `Questa operazione:\n` +
        `• Cancellerà DEFINITIVAMENTE i viaggi dal database\n` +
        `• Creerà un file JSON di backup\n` +
        `• NON può essere annullata\n\n` +
        `Sei sicuro di voler continuare?`
    );

    if (!conferma) {
        console.log('❌ Operazione annullata dall\'utente');
        return;
    }

    const parametri = {
        periodo_tipo: 'specifici',
        viaggi_ids: viaggiIds
    };

    const descrizioneOperazione = `${viaggiIds.length} viaggi selezionati`;
    await eseguiArchiviazione(parametri, descrizioneOperazione);
}

// Esecuzione archiviazione con chiamata API
async function eseguiArchiviazione(parametri, descrizioneOperazione) {
    console.log('🚀 Inizio archiviazione:', parametri);

    const btnArchiviaPeriodo = document.getElementById('btnArchiviaPeriodo');
    const btnArchiviaSelezionati = document.getElementById('btnArchiviaSelezionati');

    // Disabilita pulsanti durante l'operazione
    if (btnArchiviaPeriodo) {
        btnArchiviaPeriodo.disabled = true;
        btnArchiviaPeriodo.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Archiviazione in corso...';
    }
    if (btnArchiviaSelezionati) {
        btnArchiviaSelezionati.disabled = true;
        btnArchiviaSelezionati.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Archiviazione in corso...';
    }

    try {
        console.log('📡 Chiamata API archiviazione...');

        const response = await fetch('/api/operativo/sof/archivia-json', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(parametri)
        });

        console.log(`📊 Response status: ${response.status}`);
        const result = await response.json();
        console.log('📋 Response data:', result);

        if (response.ok && result.success) {
            // Successo
            console.log('✅ Archiviazione completata con successo');

            const successMessage =
                `✅ Archiviazione completata!\n\n` +
                `📁 File creato: ${result.filename || 'N/A'}\n` +
                `📊 Viaggi archiviati: ${result.viaggi_archiviati || 0}\n` +
                `💾 Percorso: ${result.file_path || 'N/A'}\n\n` +
                `La pagina verrà ricaricata per aggiornare i dati.`;

            alert(successMessage);

            // Ricarica la pagina per aggiornare i dati
            setTimeout(() => {
                window.location.reload();
            }, 1000);

        } else {
            // Errore dall'API
            console.error('❌ Errore API:', result);

            const errorMessage =
                `❌ Errore durante l'archiviazione\n\n` +
                `Messaggio: ${result.message || 'Errore sconosciuto'}\n` +
                `Status: ${response.status}\n\n` +
                `Controlla la console per maggiori dettagli.`;

            alert(errorMessage);
        }

    } catch (error) {
        console.error('❌ Errore archiviazione:', error);

        const errorMessage =
            `❌ Impossibile completare l'archiviazione\n\n` +
            `Errore: ${error.message}\n\n` +
            `Verifica la connessione e riprova.\n` +
            `Controlla la console per maggiori dettagli.`;

        alert(errorMessage);

    } finally {
        // Ripristina pulsanti
        if (btnArchiviaPeriodo) {
            btnArchiviaPeriodo.disabled = false;
            btnArchiviaPeriodo.innerHTML = '<i class="fas fa-archive me-2"></i>Archivia per Periodo';
        }
        if (btnArchiviaSelezionati) {
            btnArchiviaSelezionati.disabled = false;
            btnArchiviaSelezionati.innerHTML = '<i class="fas fa-archive me-2"></i>Archivia Selezionati';
        }

        console.log('🔄 Operazione archiviazione terminata');
    }
}

// Inizializzazione
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Gestione Archivio - Inizializzazione...');
    
    // Verifica che tutte le funzioni siano definite
    const funzioniRichieste = [
        'aggiornaCampiPeriodo', 'validaCampiPeriodo', 'testConnessioneAPI',
        'archiviaPerPeriodo', 'selezionaTutti', 'deselezionaTutti'
    ];
    
    let funzioniMancanti = [];
    funzioniRichieste.forEach(nome => {
        if (typeof window[nome] !== 'function') {
            funzioniMancanti.push(nome);
        }
    });
    
    if (funzioniMancanti.length > 0) {
        console.error('❌ Funzioni mancanti:', funzioniMancanti);
    } else {
        console.log('✅ Tutte le funzioni di gestione archivio sono definite');
    }
    
    console.log('✅ Gestione Archivio - Inizializzazione completata');
});
