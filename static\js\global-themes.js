/* ===== SISTEMA TEMI GLOBALE SNIP ===== */

(function() {
    'use strict';
    
    console.log('🎨 Inizializzazione sistema temi globale...');
    
    // Configurazione temi
    const THEMES = {
        maritime: {
            name: '<PERSON><PERSON><PERSON>',
            class: 'theme-maritime',
            description: 'Tema blu marittimo (default)'
        },
        dark: {
            name: '<PERSON><PERSON>',
            class: 'theme-dark',
            description: 'Tema scuro per ridurre affaticamento visivo'
        },
        light: {
            name: 'Chiaro',
            class: 'theme-light',
            description: 'Tema chiaro e minimalista'
        }
    };
    
    // Chiave localStorage per le preferenze
    const STORAGE_KEY = 'snip_interface_preferences';
    
    // Carica e applica tema salvato
    function loadAndApplyTheme() {
        try {
            const preferences = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
            const savedTheme = preferences.theme || 'maritime';
            
            console.log(`🎨 Tema salvato trovato: ${savedTheme}`);
            applyTheme(savedTheme);
            
            // Applica anche altre preferenze se disponibili
            if (preferences.animations === false) {
                document.body.classList.add('no-animations');
                console.log('🚫 Animazioni disabilitate');
            }
            
            return savedTheme;
        } catch (error) {
            console.warn('⚠️ Errore nel caricamento preferenze, uso tema default:', error);
            applyTheme('maritime');
            return 'maritime';
        }
    }
    
    // Applica tema
    function applyTheme(themeName) {
        const body = document.body;
        
        // Rimuovi tutte le classi tema esistenti
        Object.values(THEMES).forEach(theme => {
            body.classList.remove(theme.class);
        });
        
        // Applica nuovo tema
        if (THEMES[themeName]) {
            body.classList.add(THEMES[themeName].class);
            console.log(`✅ Tema applicato: ${THEMES[themeName].name} (${THEMES[themeName].class})`);
            
            // Salva tema corrente
            saveCurrentTheme(themeName);
            
            // Dispatch evento personalizzato per notificare il cambio tema
            const event = new CustomEvent('themeChanged', {
                detail: {
                    theme: themeName,
                    themeData: THEMES[themeName]
                }
            });
            document.dispatchEvent(event);
            
        } else {
            console.warn(`⚠️ Tema non trovato: ${themeName}, uso maritime`);
            body.classList.add(THEMES.maritime.class);
            saveCurrentTheme('maritime');
        }
    }
    
    // Salva tema corrente
    function saveCurrentTheme(themeName) {
        try {
            const preferences = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
            preferences.theme = themeName;
            preferences.lastUpdated = new Date().toISOString();
            
            localStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
            console.log(`💾 Tema salvato: ${themeName}`);
        } catch (error) {
            console.error('❌ Errore nel salvataggio tema:', error);
        }
    }
    
    // Ottieni tema corrente
    function getCurrentTheme() {
        try {
            const preferences = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
            return preferences.theme || 'maritime';
        } catch (error) {
            return 'maritime';
        }
    }
    
    // Cambia tema (funzione pubblica)
    function changeTheme(themeName) {
        if (THEMES[themeName]) {
            applyTheme(themeName);
            console.log(`🔄 Tema cambiato a: ${THEMES[themeName].name}`);
            return true;
        } else {
            console.error(`❌ Tema non valido: ${themeName}`);
            return false;
        }
    }
    
    // Cicla tra i temi (utile per debug o shortcut)
    function cycleTheme() {
        const currentTheme = getCurrentTheme();
        const themeNames = Object.keys(THEMES);
        const currentIndex = themeNames.indexOf(currentTheme);
        const nextIndex = (currentIndex + 1) % themeNames.length;
        const nextTheme = themeNames[nextIndex];
        
        changeTheme(nextTheme);
        return nextTheme;
    }
    
    // Gestione animazioni
    function toggleAnimations(enabled) {
        const body = document.body;
        
        if (enabled) {
            body.classList.remove('no-animations');
            console.log('✅ Animazioni abilitate');
        } else {
            body.classList.add('no-animations');
            console.log('🚫 Animazioni disabilitate');
        }
        
        // Salva preferenza
        try {
            const preferences = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
            preferences.animations = enabled;
            localStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
        } catch (error) {
            console.error('❌ Errore nel salvataggio preferenza animazioni:', error);
        }
    }
    
    // Inizializzazione
    function init() {
        console.log('🚀 Inizializzazione sistema temi...');
        
        // Carica tema salvato
        const currentTheme = loadAndApplyTheme();
        
        // Esponi funzioni globalmente per uso da altre pagine
        window.SNIPThemes = {
            apply: applyTheme,
            change: changeTheme,
            current: getCurrentTheme,
            cycle: cycleTheme,
            toggleAnimations: toggleAnimations,
            themes: THEMES,
            version: '1.0.0'
        };
        
        console.log('✅ Sistema temi globale inizializzato');
        console.log(`🎨 Tema corrente: ${THEMES[currentTheme].name}`);
        console.log('📚 Funzioni disponibili: window.SNIPThemes');
        
        // Debug info
        if (window.location.hostname === 'localhost') {
            console.log('🔧 Modalità debug attiva');
            console.log('💡 Usa SNIPThemes.cycle() per cambiare tema rapidamente');
            console.log('💡 Usa SNIPThemes.change("dark") per tema specifico');
        }
    }
    
    // Event listener per quando il DOM è pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // DOM già pronto
        init();
    }
    
    // Event listener per cambiamenti di tema da altre pagine
    window.addEventListener('storage', function(e) {
        if (e.key === STORAGE_KEY) {
            console.log('🔄 Preferenze cambiate in altra tab, ricarico...');
            loadAndApplyTheme();
        }
    });
    
    // Shortcut da tastiera per debug (Ctrl+Shift+T)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'T' && window.location.hostname === 'localhost') {
            const newTheme = cycleTheme();
            console.log(`🎨 Tema cambiato via shortcut: ${THEMES[newTheme].name}`);
        }
    });
    
})();

/* ===== UTILITY FUNCTIONS ===== */

// Funzione helper per componenti che devono reagire ai cambi tema
function onThemeChange(callback) {
    document.addEventListener('themeChanged', function(e) {
        callback(e.detail.theme, e.detail.themeData);
    });
}

// Funzione helper per ottenere il tema corrente
function getCurrentTheme() {
    return window.SNIPThemes ? window.SNIPThemes.current() : 'maritime';
}

// Funzione helper per verificare se un tema è attivo
function isThemeActive(themeName) {
    return getCurrentTheme() === themeName;
}

// Export per uso in moduli (se necessario)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        onThemeChange,
        getCurrentTheme,
        isThemeActive
    };
}
