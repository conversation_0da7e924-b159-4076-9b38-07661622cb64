/**
 * 🎨 SISTEMA UNIFICATO MESSAGGI SNIP - VERSIONE BELLISSIMA
 * Sostituisce TUTTI i sistemi di messaggi esistenti con uno spettacolare
 */

// Stili CSS per i messaggi - DESIGN SPETTACOLARE
const messageStyles = `
<style id="snip-messages-styles">
/* 🎨 MESSAGGI SNIP - DESIGN SPETTACOLARE UNIFICATO */
.snip-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 99999;
    min-width: 380px;
    max-width: 520px;
    border: none;
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.15),
        0 10px 20px rgba(0,0,0,0.1),
        inset 0 1px 0 rgba(255,255,255,0.2);
    backdrop-filter: blur(20px);
    animation: snipSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    font-family: '<PERSON><PERSON><PERSON> UI', Tahom<PERSON>, Geneva, Verdana, sans-serif;
    font-weight: 600;
    margin-bottom: 15px;
    overflow: hidden;
    transform-origin: top right;
}

/* 🎨 TIPI DI MESSAGGIO - DESIGN SPETTACOLARE */
.snip-message.success {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #74b9ff 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    box-shadow:
        0 20px 40px rgba(0,184,148,0.3),
        0 10px 20px rgba(0,206,201,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

.snip-message.error {
    background: linear-gradient(135deg, #e17055 0%, #fd79a8 50%, #fdcb6e 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    box-shadow:
        0 20px 40px rgba(225,112,85,0.3),
        0 10px 20px rgba(253,121,168,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

.snip-message.warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #fd79a8 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    box-shadow:
        0 20px 40px rgba(253,203,110,0.3),
        0 10px 20px rgba(225,112,85,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

.snip-message.info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    box-shadow:
        0 20px 40px rgba(116,185,255,0.3),
        0 10px 20px rgba(9,132,227,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

/* 🎨 CONTENUTO MESSAGGIO - LAYOUT SPETTACOLARE */
.snip-message-content {
    padding: 25px;
    display: flex;
    align-items: flex-start;
    position: relative;
}

.snip-message-icon {
    font-size: 2.5em;
    margin-right: 20px;
    flex-shrink: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: snipIconBounce 0.8s ease-out 0.3s;
    transform: scale(0);
    animation-fill-mode: forwards;
}

.snip-message-text {
    flex-grow: 1;
    padding-right: 10px;
}

.snip-message-title {
    font-size: 1.3em;
    font-weight: 800;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    letter-spacing: 0.5px;
}

.snip-message-body {
    font-size: 1em;
    opacity: 0.95;
    line-height: 1.5;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

/* 🎨 PULSANTE CHIUSURA - DESIGN ELEGANTE */
.snip-message-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    font-size: 1.2em;
    cursor: pointer;
    opacity: 0.8;
    padding: 0;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    font-weight: bold;
    backdrop-filter: blur(10px);
}

.snip-message-close:hover {
    opacity: 1;
    background: rgba(255,255,255,0.3);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 🎨 ANIMAZIONI SPETTACOLARI */
@keyframes snipSlideIn {
    0% {
        transform: translateX(120%) scale(0.8) rotate(10deg);
        opacity: 0;
    }
    50% {
        transform: translateX(-10%) scale(1.05) rotate(-2deg);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0) scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes snipSlideOut {
    0% {
        transform: translateX(0) scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translateX(20%) scale(0.95) rotate(5deg);
        opacity: 0.5;
    }
    100% {
        transform: translateX(120%) scale(0.8) rotate(15deg);
        opacity: 0;
    }
}

@keyframes snipIconBounce {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(-90deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.snip-message.fade-out {
    animation: snipSlideOut 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 🎨 PROGRESS BAR SPETTACOLARE */
.snip-message-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg,
        rgba(255,255,255,0.8) 0%,
        rgba(255,255,255,0.6) 50%,
        rgba(255,255,255,0.4) 100%);
    border-radius: 0 0 20px 20px;
    transition: width linear;
    box-shadow:
        0 -2px 10px rgba(255,255,255,0.3),
        inset 0 1px 0 rgba(255,255,255,0.5);
}

/* 🎨 EFFETTI PARTICELLE (opzionale) */
.snip-message::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255,255,255,0.1) 50%,
        transparent 70%);
    border-radius: 22px;
    z-index: -1;
    animation: snipShimmer 3s ease-in-out infinite;
}

@keyframes snipShimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
    .snip-message {
        left: 10px;
        right: 10px;
        min-width: auto;
        max-width: none;
    }
}
</style>
`;

// Aggiungi gli stili al documento
if (!document.getElementById('snip-messages-styles')) {
    document.head.insertAdjacentHTML('beforeend', messageStyles);
}

/**
 * 🎨 CLASSE SNIP MESSAGES - SISTEMA UNIFICATO SPETTACOLARE
 * Sostituisce: Bootstrap alerts, SweetAlert, Toast, alert(), confirm(), etc.
 */
class SNIPMessages {
    constructor() {
        this.messages = [];
        this.maxMessages = 4;
        this.soundEnabled = true;
        this.init();
    }

    init() {
        // Sostituisci alert() nativo
        window.alert = (message) => this.info('Avviso', message);

        // Sostituisci confirm() nativo con versione asincrona
        window.confirmSNIP = (message, title = 'Conferma') => {
            return new Promise((resolve) => {
                this.confirm(title, message, resolve);
            });
        };

        // Nascondi tutti gli alert Bootstrap esistenti
        this.hideBootstrapAlerts();

        console.log('🎨 Sistema SNIP Messages inizializzato - Tutti i messaggi ora sono bellissimi!');
    }

    hideBootstrapAlerts() {
        // Nascondi alert Bootstrap esistenti
        const alerts = document.querySelectorAll('.alert, .toast, .swal2-container');
        alerts.forEach(alert => {
            alert.style.display = 'none';
        });
    }

    /**
     * 🎉 Mostra un messaggio di successo SPETTACOLARE
     */
    success(title, message, duration = 6000) {
        this.playSound('success');
        return this.showMessage('success', '🎉', title, message, duration);
    }

    /**
     * 💥 Mostra un messaggio di errore SPETTACOLARE
     */
    error(title, message, duration = 8000) {
        this.playSound('error');
        return this.showMessage('error', '💥', title, message, duration);
    }

    /**
     * ⚡ Mostra un messaggio di avviso SPETTACOLARE
     */
    warning(title, message, duration = 7000) {
        this.playSound('warning');
        return this.showMessage('warning', '⚡', title, message, duration);
    }

    /**
     * 💫 Mostra un messaggio informativo SPETTACOLARE
     */
    info(title, message, duration = 6000) {
        this.playSound('info');
        return this.showMessage('info', '💫', title, message, duration);
    }

    /**
     * 🤔 Mostra un dialogo di conferma SPETTACOLARE
     */
    confirm(title, message, callback) {
        const confirmEl = this.showMessage('warning', '🤔', title, message, 0, true);

        // Aggiungi pulsanti
        const buttonsHtml = `
            <div style="margin-top: 20px; text-align: center;">
                <button class="snip-confirm-btn snip-confirm-yes" style="
                    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
                    color: white; border: none; padding: 12px 25px; border-radius: 25px;
                    margin: 0 10px; font-weight: bold; cursor: pointer;
                    box-shadow: 0 5px 15px rgba(0,184,148,0.3);
                    transition: all 0.3s ease;
                ">✅ Conferma</button>
                <button class="snip-confirm-btn snip-confirm-no" style="
                    background: linear-gradient(135deg, #e17055 0%, #fd79a8 100%);
                    color: white; border: none; padding: 12px 25px; border-radius: 25px;
                    margin: 0 10px; font-weight: bold; cursor: pointer;
                    box-shadow: 0 5px 15px rgba(225,112,85,0.3);
                    transition: all 0.3s ease;
                ">❌ Annulla</button>
            </div>
        `;

        confirmEl.querySelector('.snip-message-text').insertAdjacentHTML('beforeend', buttonsHtml);

        // Gestisci click pulsanti
        confirmEl.querySelector('.snip-confirm-yes').onclick = () => {
            this.removeMessage(confirmEl);
            if (callback) callback(true);
        };

        confirmEl.querySelector('.snip-confirm-no').onclick = () => {
            this.removeMessage(confirmEl);
            if (callback) callback(false);
        };

        return confirmEl;
    }

    /**
     * 🔊 Riproduce suoni per feedback
     */
    playSound(type) {
        if (!this.soundEnabled) return;

        // Crea suoni con Web Audio API (opzionale)
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            const frequencies = {
                success: [523.25, 659.25, 783.99], // Do, Mi, Sol
                error: [220, 185, 165], // La, Fa#, Mi
                warning: [440, 554.37], // La, Do#
                info: [523.25, 659.25] // Do, Mi
            };

            const freq = frequencies[type] || frequencies.info;
            oscillator.frequency.setValueAtTime(freq[0], audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            // Fallback silenzioso se Web Audio non è supportato
        }
    }

    /**
     * 🎨 Metodo principale per mostrare messaggi SPETTACOLARI
     */
    showMessage(type, icon, title, message, duration, isConfirm = false) {
        // Rimuovi messaggi in eccesso
        if (this.messages.length >= this.maxMessages) {
            this.removeOldestMessage();
        }

        // Crea elemento messaggio
        const messageEl = document.createElement('div');
        messageEl.className = `snip-message ${type}`;

        // Calcola posizione top basata sui messaggi esistenti
        const topOffset = 20 + (this.messages.length * 100);
        messageEl.style.top = `${topOffset}px`;

        // HTML del messaggio con design spettacolare
        messageEl.innerHTML = `
            <div class="snip-message-content">
                <div class="snip-message-icon">${icon}</div>
                <div class="snip-message-text">
                    <div class="snip-message-title">${title}</div>
                    <div class="snip-message-body">${message}</div>
                </div>
                ${!isConfirm ? `<button class="snip-message-close" onclick="snipMessages.removeMessage(this.parentElement.parentElement)">×</button>` : ''}
            </div>
            ${duration > 0 ? '<div class="snip-message-progress" style="width: 100%;"></div>' : ''}
        `;

        // Aggiungi al DOM
        document.body.appendChild(messageEl);
        this.messages.push(messageEl);

        // Anima progress bar solo se non è un confirm
        if (duration > 0 && !isConfirm) {
            const progressBar = messageEl.querySelector('.snip-message-progress');
            setTimeout(() => {
                progressBar.style.width = '0%';
                progressBar.style.transition = `width ${duration}ms linear`;
            }, 200);

            // Auto-rimozione
            setTimeout(() => {
                this.removeMessage(messageEl);
            }, duration);
        }

        // Aggiungi effetti hover
        messageEl.addEventListener('mouseenter', () => {
            messageEl.style.transform = 'scale(1.02) translateX(-5px)';
            messageEl.style.boxShadow = '0 25px 50px rgba(0,0,0,0.2)';
        });

        messageEl.addEventListener('mouseleave', () => {
            messageEl.style.transform = 'scale(1) translateX(0)';
            messageEl.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';
        });

        return messageEl;
    }

    /**
     * Rimuove un messaggio specifico
     */
    removeMessage(messageEl) {
        if (!messageEl || !messageEl.parentNode) return;

        const index = this.messages.indexOf(messageEl);
        if (index > -1) {
            this.messages.splice(index, 1);
        }

        // Anima uscita
        messageEl.classList.add('fade-out');
        
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
            // Riposiziona messaggi rimanenti
            this.repositionMessages();
        }, 300);
    }

    /**
     * Rimuove il messaggio più vecchio
     */
    removeOldestMessage() {
        if (this.messages.length > 0) {
            this.removeMessage(this.messages[0]);
        }
    }

    /**
     * Riposiziona i messaggi dopo una rimozione
     */
    repositionMessages() {
        this.messages.forEach((messageEl, index) => {
            if (messageEl && messageEl.parentNode) {
                const newTop = 20 + (index * 80);
                messageEl.style.top = `${newTop}px`;
                messageEl.style.transition = 'top 0.3s ease';
            }
        });
    }

    /**
     * Rimuove tutti i messaggi
     */
    clearAll() {
        this.messages.forEach(messageEl => {
            if (messageEl && messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        });
        this.messages = [];
    }
}

// Crea istanza globale
window.snipMessages = new SNIPMessages();

// 🎨 FUNZIONI DI COMPATIBILITÀ - SOSTITUISCONO TUTTI I SISTEMI ESISTENTI
window.mostraSuccesso = function(messaggio, titolo = '🎉 Successo!') {
    return snipMessages.success(titolo, messaggio);
};

window.mostraErrore = function(messaggio, titolo = '💥 Errore!') {
    return snipMessages.error(titolo, messaggio);
};

window.mostraAvviso = function(messaggio, titolo = '⚡ Attenzione!') {
    return snipMessages.warning(titolo, messaggio);
};

window.mostraInfo = function(messaggio, titolo = '💫 Informazione') {
    return snipMessages.info(titolo, messaggio);
};

// Funzioni aggiuntive per compatibilità
window.showSuccess = window.mostraSuccesso;
window.showError = window.mostraErrore;
window.showWarning = window.mostraAvviso;
window.showInfo = window.mostraInfo;

// Sostituisci SweetAlert
window.Swal = {
    fire: function(options) {
        if (typeof options === 'string') {
            return snipMessages.info('Avviso', options);
        }

        const type = options.icon || 'info';
        const title = options.title || 'Avviso';
        const text = options.text || '';

        switch(type) {
            case 'success': return snipMessages.success(title, text);
            case 'error': return snipMessages.error(title, text);
            case 'warning': return snipMessages.warning(title, text);
            case 'info': return snipMessages.info(title, text);
            default: return snipMessages.info(title, text);
        }
    }
};

// Sostituisci Toast Bootstrap
window.showToast = function(message, type = 'info') {
    switch(type) {
        case 'success': return snipMessages.success('Notifica', message);
        case 'error': return snipMessages.error('Errore', message);
        case 'warning': return snipMessages.warning('Avviso', message);
        default: return snipMessages.info('Informazione', message);
    }
};

// Sostituisci alert Bootstrap
window.showAlert = function(message, type = 'info', title = '') {
    const titles = {
        success: '🎉 Successo',
        danger: '💥 Errore',
        warning: '⚡ Attenzione',
        info: '💫 Informazione'
    };

    const finalTitle = title || titles[type] || titles.info;

    switch(type) {
        case 'success': return snipMessages.success(finalTitle, message);
        case 'danger':
        case 'error': return snipMessages.error(finalTitle, message);
        case 'warning': return snipMessages.warning(finalTitle, message);
        default: return snipMessages.info(finalTitle, message);
    }
};

// Funzioni per messaggi specifici dell'app
window.messaggioViaggioCreato = function(viaggio, nave) {
    return snipMessages.success(
        '🚢 Viaggio Creato!',
        `Il viaggio ${viaggio} per la nave ${nave} è stato creato con successo.`
    );
};

window.messaggioViaggioEliminato = function(viaggio) {
    return snipMessages.success(
        '🗑️ Viaggio Eliminato!',
        `Il viaggio ${viaggio} è stato eliminato con successo.`
    );
};

window.messaggioSOFGenerato = function(viaggio) {
    return snipMessages.success(
        '📄 SOF Generato!',
        `Il documento SOF per il viaggio ${viaggio} è stato generato con successo.`
    );
};

window.messaggioErroreGenerico = function(operazione = 'operazione') {
    return snipMessages.error(
        '💥 Errore!',
        `Si è verificato un errore durante l'${operazione}. Riprova più tardi.`
    );
};

// ===== MIGRAZIONE AUTOMATICA SISTEMI LEGACY =====

// Sostituisci tutte le funzioni showError/showSuccess/showWarning/showInfo esistenti
window.showError = function(message, title = '💥 Errore!') {
    return snipMessages.error(title, message);
};

window.showSuccess = function(message, title = '🎉 Successo!') {
    return snipMessages.success(title, message);
};

window.showWarning = function(message, title = '⚡ Attenzione!') {
    return snipMessages.warning(title, message);
};

window.showInfo = function(message, title = '💫 Informazione') {
    return snipMessages.info(title, message);
};

// Sostituisci alert() nativo globalmente
const originalAlert = window.alert;
window.alert = function(message) {
    return snipMessages.info('📢 Avviso', message);
};

// Sostituisci confirm() nativo globalmente
const originalConfirm = window.confirm;
window.confirm = function(message) {
    return new Promise((resolve) => {
        snipMessages.confirm('🤔 Conferma', message, resolve);
    });
};

// Funzione per migrare automaticamente SweetAlert esistente
function migrateSweetAlert() {
    // Se SweetAlert è già caricato, sostituiscilo
    if (typeof window.Swal !== 'undefined' && window.Swal.fire) {
        const originalSwal = window.Swal.fire;

        window.Swal.fire = function(options) {
            // Se è una stringa semplice
            if (typeof options === 'string') {
                return snipMessages.info('📢 Avviso', options);
            }

            // Se è un oggetto con opzioni
            if (typeof options === 'object') {
                const type = options.icon || 'info';
                const title = options.title || 'Avviso';
                const text = options.text || options.html || '';

                // Gestisci conferme
                if (options.showCancelButton) {
                    return new Promise((resolve) => {
                        snipMessages.confirm(title, text, (result) => {
                            resolve({ isConfirmed: result, isDenied: !result, isDismissed: !result });
                        });
                    });
                }

                // Gestisci messaggi normali
                switch(type) {
                    case 'success': return snipMessages.success(title, text);
                    case 'error': return snipMessages.error(title, text);
                    case 'warning': return snipMessages.warning(title, text);
                    case 'info': return snipMessages.info(title, text);
                    default: return snipMessages.info(title, text);
                }
            }

            // Fallback al SweetAlert originale se necessario
            return originalSwal.apply(this, arguments);
        };

        console.log('🔄 SweetAlert migrato al sistema SNIP Messages');
    }
}

// Esegui migrazione quando il DOM è pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', migrateSweetAlert);
} else {
    migrateSweetAlert();
}

// Migra anche dopo un breve delay per catturare SweetAlert caricato in modo asincrono
setTimeout(migrateSweetAlert, 1000);

console.log('🎨 Sistema SNIP Messages SPETTACOLARE caricato!');
console.log('✅ Tutti i sistemi di messaggi esistenti sono stati sostituiti');
console.log('🔄 Migrazione automatica attivata per SweetAlert e alert() nativi');
console.log('🎉 Ora tutti i messaggi sono bellissimi e uniformi!');
