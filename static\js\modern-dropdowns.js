/* ===== JAVASCRIPT DROPDOWN MODERNI SNIP ===== */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Inizializzazione dropdown moderni...');

    // Trova tutti i dropdown nella navbar
    const dropdowns = document.querySelectorAll('.navbar .nav-item.dropdown');
    
    dropdowns.forEach(function(dropdown) {
        const button = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (button && menu) {
            console.log(`✅ Dropdown trovato: ${button.id || 'senza ID'}`);
            
            // Funzione per posizionare il dropdown intelligentemente
            function positionDropdown() {
                const rect = button.getBoundingClientRect();
                const menuWidth = menu.offsetWidth || 280;
                const menuHeight = menu.offsetHeight || 300;
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // FORZA NESSUNA BARRA DI SCORRIMENTO
                menu.style.overflow = 'visible';
                menu.style.overflowX = 'hidden';
                menu.style.overflowY = 'visible';
                menu.style.maxHeight = 'none';
                menu.style.height = 'auto';

                // Posizionamento orizzontale intelligente
                const rightSpace = viewportWidth - rect.right;
                const leftSpace = rect.left;
                const centerSpace = rect.left + (rect.width / 2);

                if (rightSpace >= menuWidth) {
                    // Spazio sufficiente a destra - allinea a sinistra del pulsante
                    menu.style.right = 'auto';
                    menu.style.left = '0';
                    menu.style.transform = 'translateX(0)';
                } else if (leftSpace >= menuWidth) {
                    // Spazio sufficiente a sinistra - allinea a destra del pulsante
                    menu.style.right = '0';
                    menu.style.left = 'auto';
                    menu.style.transform = 'translateX(0)';
                } else {
                    // Centra il dropdown se non c'è spazio
                    const offset = (menuWidth - rect.width) / 2;
                    menu.style.right = `${offset}px`;
                    menu.style.left = 'auto';
                    menu.style.transform = 'translateX(0)';
                }

                // Posizionamento verticale intelligente
                const bottomSpace = viewportHeight - rect.bottom;
                const topSpace = rect.top;

                if (bottomSpace >= menuHeight) {
                    // Spazio sufficiente sotto
                    menu.style.top = '100%';
                    menu.style.bottom = 'auto';
                    menu.style.marginTop = '12px';
                    menu.style.marginBottom = '0';
                } else if (topSpace >= menuHeight) {
                    // Spazio sufficiente sopra
                    menu.style.top = 'auto';
                    menu.style.bottom = '100%';
                    menu.style.marginTop = '0';
                    menu.style.marginBottom = '12px';
                } else {
                    // Forza sotto ma senza barre
                    menu.style.top = '100%';
                    menu.style.bottom = 'auto';
                    menu.style.marginTop = '12px';
                    menu.style.marginBottom = '0';
                    menu.style.maxHeight = 'none';
                    menu.style.overflow = 'visible';
                }

                // FORZA SEMPRE VISIBILITÀ COMPLETA
                menu.style.zIndex = '9999';
                menu.style.position = 'absolute';
            }

            // Event listener per apertura dropdown
            button.addEventListener('click', function(e) {
                console.log(`🖱️ Click su dropdown: ${button.id || 'senza ID'}`);
                
                setTimeout(function() {
                    if (menu.classList.contains('show')) {
                        positionDropdown();
                        console.log(`📍 Dropdown ${button.id || 'senza ID'} posizionato`);
                    }
                }, 10);
            });

            // Event listener per ridimensionamento finestra
            window.addEventListener('resize', function() {
                if (menu.classList.contains('show')) {
                    positionDropdown();
                }
            });

            // Event listener per scroll (mantieni posizione)
            window.addEventListener('scroll', function() {
                if (menu.classList.contains('show')) {
                    positionDropdown();
                }
            });

            // Forza posizionamento iniziale
            setTimeout(function() {
                positionDropdown();
            }, 100);
        }
    });

    console.log(`✅ ${dropdowns.length} dropdown moderni inizializzati`);
});

/* ===== EFFETTI SPECIALI ===== */

// Aggiungi effetto ripple ai dropdown items
document.addEventListener('DOMContentLoaded', function() {
    const dropdownItems = document.querySelectorAll('.navbar .dropdown-menu .dropdown-item');
    
    dropdownItems.forEach(function(item) {
        item.addEventListener('click', function(e) {
            // Crea effetto ripple
            const ripple = document.createElement('span');
            const rect = item.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple-effect');
            
            item.appendChild(ripple);
            
            setTimeout(function() {
                ripple.remove();
            }, 600);
        });
    });
});

/* ===== CSS DINAMICO PER EFFETTO RIPPLE ===== */
const rippleCSS = `
.dropdown-item {
    position: relative !important;
    overflow: hidden !important;
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Aggiungi CSS dinamicamente
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
