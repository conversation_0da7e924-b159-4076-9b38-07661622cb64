/**
 * Configurazione centralizzata per gli intervalli di aggiornamento
 * Evita loop di aggiornamento e migliora le performance
 */

// Configurazione intervalli (in millisecondi)
const REFRESH_INTERVALS = {
    // Intervalli normali (per uso normale)
    NOTIFICATIONS: 120000,      // 2 minuti (era 30 secondi)
    DASHBOARD: 120000,          // 2 minuti (era 30 secondi)
    USERS_ONLINE: 180000,       // 3 minuti (era 30 secondi)
    CONFIG_AUTOSAVE: 300000,    // 5 minuti (era 30 secondi)
    SYSTEM_STATS: 300000,       // 5 minuti
    
    // Intervalli rapidi (per dati critici)
    CRITICAL_ALERTS: 60000,     // 1 minuto
    SESSION_CHECK: 600000,      // 10 minuti
    
    // Intervalli lenti (per dati non critici)
    BACKGROUND_SYNC: 900000,    // 15 minuti
    CLEANUP_TASKS: 1800000,     // 30 minuti
    
    // Intervalli per debug (più lunghi)
    DEBUG_MODE: {
        NOTIFICATIONS: 300000,   // 5 minuti
        DASHBOARD: 300000,       // 5 minuti
        USERS_ONLINE: 600000,    // 10 minuti
    }
};

// Stato globale per gestire gli intervalli
const RefreshManager = {
    intervals: new Map(),
    isDebugMode: false,
    
    /**
     * Inizializza il manager degli aggiornamenti
     */
    init() {
        console.log('🔄 Inizializzazione RefreshManager...');
        
        // Rileva modalità debug
        this.isDebugMode = window.location.hostname === 'localhost' || 
                          window.location.search.includes('debug=true');
        
        if (this.isDebugMode) {
            console.log('🐛 Modalità DEBUG attiva - intervalli ridotti');
        }
        
        // Cleanup automatico quando la pagina viene chiusa
        window.addEventListener('beforeunload', () => {
            this.clearAll();
        });
        
        // Pausa aggiornamenti quando la tab non è visibile
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAll();
            } else {
                this.resumeAll();
            }
        });
    },
    
    /**
     * Ottiene l'intervallo configurato per un tipo di aggiornamento
     */
    getInterval(type) {
        if (this.isDebugMode && REFRESH_INTERVALS.DEBUG_MODE[type]) {
            return REFRESH_INTERVALS.DEBUG_MODE[type];
        }
        return REFRESH_INTERVALS[type] || REFRESH_INTERVALS.DASHBOARD;
    },
    
    /**
     * Registra un nuovo intervallo di aggiornamento
     */
    register(name, callback, type = 'DASHBOARD') {
        // Cancella intervallo esistente se presente
        this.clear(name);
        
        const interval = this.getInterval(type);
        const intervalId = setInterval(() => {
            try {
                callback();
            } catch (error) {
                console.error(`Errore in refresh ${name}:`, error);
                // In caso di errore, aumenta l'intervallo
                this.increaseInterval(name);
            }
        }, interval);
        
        this.intervals.set(name, {
            id: intervalId,
            callback: callback,
            type: type,
            interval: interval,
            paused: false
        });
        
        console.log(`✅ Registrato refresh '${name}' ogni ${interval/1000}s`);
        return intervalId;
    },
    
    /**
     * Cancella un intervallo specifico
     */
    clear(name) {
        const intervalData = this.intervals.get(name);
        if (intervalData) {
            clearInterval(intervalData.id);
            this.intervals.delete(name);
            console.log(`🗑️ Cancellato refresh '${name}'`);
        }
    },
    
    /**
     * Cancella tutti gli intervalli
     */
    clearAll() {
        this.intervals.forEach((data, name) => {
            clearInterval(data.id);
        });
        this.intervals.clear();
        console.log('🧹 Tutti i refresh cancellati');
    },
    
    /**
     * Mette in pausa tutti gli aggiornamenti
     */
    pauseAll() {
        this.intervals.forEach((data, name) => {
            if (!data.paused) {
                clearInterval(data.id);
                data.paused = true;
            }
        });
        console.log('⏸️ Tutti i refresh in pausa');
    },
    
    /**
     * Riprende tutti gli aggiornamenti
     */
    resumeAll() {
        this.intervals.forEach((data, name) => {
            if (data.paused) {
                data.id = setInterval(data.callback, data.interval);
                data.paused = false;
            }
        });
        console.log('▶️ Tutti i refresh ripresi');
    },
    
    /**
     * Aumenta l'intervallo in caso di errori
     */
    increaseInterval(name) {
        const data = this.intervals.get(name);
        if (data) {
            clearInterval(data.id);
            data.interval = Math.min(data.interval * 1.5, 600000); // Max 10 minuti
            data.id = setInterval(data.callback, data.interval);
            console.log(`⚠️ Aumentato intervallo '${name}' a ${data.interval/1000}s`);
        }
    },
    
    /**
     * Ottiene statistiche sui refresh attivi
     */
    getStats() {
        const stats = {
            total: this.intervals.size,
            active: 0,
            paused: 0,
            intervals: []
        };
        
        this.intervals.forEach((data, name) => {
            if (data.paused) {
                stats.paused++;
            } else {
                stats.active++;
            }
            
            stats.intervals.push({
                name: name,
                type: data.type,
                interval: data.interval,
                paused: data.paused
            });
        });
        
        return stats;
    }
};

// Funzioni di utilità per compatibilità con codice esistente
function registerRefresh(name, callback, type = 'DASHBOARD') {
    return RefreshManager.register(name, callback, type);
}

function clearRefresh(name) {
    RefreshManager.clear(name);
}

function getRefreshInterval(type) {
    return RefreshManager.getInterval(type);
}

// Inizializza automaticamente quando il DOM è pronto
document.addEventListener('DOMContentLoaded', function() {
    RefreshManager.init();
});

// Esporta per uso globale
window.RefreshManager = RefreshManager;
window.registerRefresh = registerRefresh;
window.clearRefresh = clearRefresh;
window.getRefreshInterval = getRefreshInterval;
window.REFRESH_INTERVALS = REFRESH_INTERVALS;
