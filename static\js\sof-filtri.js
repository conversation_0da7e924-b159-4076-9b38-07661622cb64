// JavaScript per gestione filtri SOF da realizzare
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 SOF Filtri JavaScript caricato');

    // Elementi del DOM
    const portoSelect = document.getElementById('porto_gestione');
    const filtroForm = document.getElementById('filtroForm');

    // Auto-submit del form quando cambia il porto
    if (portoSelect) {
        portoSelect.addEventListener('change', function() {
            console.log('📍 Porto selezionato:', this.value);
            
            // Mostra indicatore di caricamento
            const submitBtn = filtroForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Filtrando...';
            submitBtn.disabled = true;
            
            // Submit automatico del form
            setTimeout(() => {
                filtroForm.submit();
            }, 300);
        });
    }

    // Gestione URL parameters per mantenere lo stato del filtro
    const urlParams = new URLSearchParams(window.location.search);
    const portoParam = urlParams.get('porto_gestione');
    
    if (portoParam && portoSelect) {
        portoSelect.value = portoParam;
    }

    // Animazione delle righe della tabella
    animateTableRows();
});

// Funzione per animare le righe della tabella
function animateTableRows() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// Funzione per ricerca dinamica (AJAX) - opzionale
function searchViaggiDinamico(porto) {
    console.log('🔍 Ricerca dinamica per porto:', porto);
    
    const url = `/api/sof/da-realizzare/search?porto_gestione=${encodeURIComponent(porto)}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ Risultati ricerca:', data.viaggi.length);
                updateTableContent(data.viaggi);
                updateResultsInfo(data.viaggi.length, porto);
            } else {
                console.error('❌ Errore ricerca:', data.message);
                mostraErrore('Errore durante la ricerca: ' + data.message);
            }
        })
        .catch(error => {
            console.error('❌ Errore AJAX:', error);
            mostraErrore('Errore di connessione durante la ricerca');
        });
}

// Funzione per aggiornare il contenuto della tabella
function updateTableContent(viaggi) {
    const tbody = document.querySelector('table tbody');
    
    if (viaggi.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-search fa-3x mb-3 text-warning"></i>
                        <h5>Nessun viaggio trovato</h5>
                        <p>Non ci sono viaggi per il filtro applicato.<br>
                        Prova a modificare i criteri di ricerca.</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    viaggi.forEach(viaggio => {
        html += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-barcode me-2 text-primary"></i>
                        <strong>${viaggio.viaggio || 'N/A'}</strong>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-ship me-2 text-info"></i>
                        ${viaggio.nome_nave || 'N/A'}
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-anchor me-2 text-success"></i>
                        ${viaggio.nome_porto || 'N/A'}
                    </div>
                </td>
                <td>
                    ${viaggio.porto_arrivo_nome ?
                        `<span class="badge bg-info text-dark">
                            <i class="fas fa-map-marker-alt me-1"></i>${viaggio.porto_arrivo_nome}
                        </span>` :
                        `<span class="text-muted">
                            <i class="fas fa-minus me-1"></i>Non specificato
                        </span>`
                    }
                </td>
                <td>
                    ${viaggio.porto_destinazione_nome ?
                        `<span class="badge bg-warning text-dark">
                            <i class="fas fa-flag-checkered me-1"></i>${viaggio.porto_destinazione_nome}
                        </span>` :
                        `<span class="text-muted">
                            <i class="fas fa-minus me-1"></i>Non specificato
                        </span>`
                    }
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-alt me-2 text-warning"></i>
                        ${viaggio.data_arrivo || 'N/A'}
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-alt me-2 text-danger"></i>
                        ${viaggio.data_partenza || 'N/A'}
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <a href="/operativo/sof/viaggio/${viaggio.id}" class="btn btn-sm btn-outline-primary" title="Gestisci Viaggio">
                            <i class="fas fa-cog me-1"></i>Gestisci
                        </a>
                        <button class="btn btn-sm btn-outline-warning" title="Modifica Viaggio"
                                onclick="modificaViaggio(${viaggio.id})">
                            <i class="fas fa-edit me-1"></i>Modifica
                        </button>
                        <button class="btn btn-sm btn-outline-danger" title="Elimina Viaggio"
                                onclick="eliminaViaggio(${viaggio.id}, '${viaggio.viaggio || ''}')">
                            <i class="fas fa-trash me-1"></i>Elimina
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
    animateTableRows();
}

// Funzione per aggiornare le informazioni sui risultati
function updateResultsInfo(count, porto) {
    const infoElement = document.querySelector('.card-header small');
    if (infoElement) {
        if (porto) {
            infoElement.innerHTML = `
                <i class="fas fa-route me-1"></i>
                Mostrando ${count} viaggi per ${porto}
            `;
        } else {
            infoElement.innerHTML = `
                <i class="fas fa-route me-1"></i>
                Mostrando tutti i ${count} viaggi del sistema
            `;
        }
    }
}

// Funzioni di utilità per messaggi (se non già definite)
function mostraErrore(messaggio) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Errore',
            text: messaggio,
            confirmButtonColor: '#dc3545'
        });
    } else {
        alert('Errore: ' + messaggio);
    }
}

function mostraSuccesso(messaggio) {
    // 🎨 Usa il sistema SNIP Messages spettacolare se disponibile
    if (typeof window.snipMessages !== 'undefined') {
        return window.snipMessages.success('🎉 Successo!', messaggio);
    }

    // Fallback: usa SweetAlert se disponibile
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Successo',
            text: messaggio,
            confirmButtonColor: '#198754'
        });
    } else {
        // Ultimo fallback: alert nativo
        alert('Successo: ' + messaggio);
    }
}

/**
 * 🚀 OTTIENI NOME PORTO NUCLEARE
 * Versione potenziata per la soluzione nucleare
 */
async function ottieniNomePortoNucleare(codicePoroto) {
    if (!codicePoroto || codicePoroto.trim() === '') {
        console.log('⚠️ Codice porto nucleare vuoto');
        return 'N/A';
    }

    console.log('🔍 Ricerca nucleare porto:', codicePoroto);

    try {
        const response = await fetch(`/api/atlas?limit=2000`); // Aumentato limite per sicurezza nucleare
        const data = await response.json();

        if (data.success && data.data) {
            const porto = data.data.find(p => p.id_cod === codicePoroto.trim());
            if (porto) {
                console.log('✅ Porto nucleare trovato:', porto.porto);
                return porto.porto;
            } else {
                console.log('⚠️ Porto nucleare non trovato per codice:', codicePoroto);
                return codicePoroto; // Restituisci il codice se non trovi il nome
            }
        }
        console.log('⚠️ Risposta API Atlas nucleare non valida');
        return codicePoroto; // Fallback al codice
    } catch (error) {
        console.error('💥 Errore nucleare nel caricamento nome porto:', error);
        return codicePoroto; // Fallback al codice in caso di errore
    }
}

/**
 * Ottiene il nome del porto dall'API Atlas (versione legacy)
 */
async function ottieniNomePorto(codicePoroto) {
    return await ottieniNomePortoNucleare(codicePoroto);
}

/**
 * 🚀🚀🚀 AGGIORNAMENTO NUCLEARE RIGA VIAGGIO 🚀🚀🚀
 * SOLUZIONE DEFINITIVA che risolve il problema ATLANTIC STAR una volta per sempre!
 */
async function aggiornaRigaViaggio(viaggioId) {
    console.log('🔥🔥🔥 AGGIORNAMENTO NUCLEARE RIGA VIAGGIO:', viaggioId);

    try {
        // 🔍 TROVA LA RIGA NELLA TABELLA
        const riga = document.querySelector(`tr[data-viaggio-id="${viaggioId}"]`);
        if (!riga) {
            console.log('⚠️ Riga non trovata nella tabella, RICARICO NUCLEARE');
            window.location.reload(true); // Force reload
            return;
        }

        console.log('✅ Riga trovata nella tabella');

        // 🚀 OTTIENI DATI AGGIORNATI CON VERIFICA NUCLEARE
        console.log('🔄 Caricamento dati nucleari del viaggio...');
        const response = await fetch(`/api/viaggi/${viaggioId}`);
        const data = await response.json();

        if (!data.success) {
            console.error('❌ ERRORE NUCLEARE nel caricamento dati viaggio:', data.error);
            console.log('🔄 Fallback: ricarico pagina completa');
            window.location.reload(true);
            return;
        }

        const viaggio = data.data;
        console.log('📋 DATI VIAGGIO NUCLEARI RICEVUTI:', viaggio);
        console.log('🚢 NAVE NUCLEARE:', viaggio.nome_nave);
        console.log('🛳️ PORTO ARRIVO NUCLEARE:', viaggio.porto_arrivo);
        console.log('🛳️ PORTO DESTINAZIONE NUCLEARE:', viaggio.porto_destinazione);

        // 🔍 VERIFICA CRITICA NAVE
        if (!viaggio.nome_nave || viaggio.nome_nave === 'ATLANTIC STAR') {
            console.error('🚨 PROBLEMA NUCLEARE RILEVATO: Nave è ATLANTIC STAR o null!');
            console.log('🔄 Forzando ricarica completa per risolvere...');
            window.location.reload(true);
            return;
        }

        // 🎯 AGGIORNAMENTO NUCLEARE CELLE
        console.log('🔥 INIZIO AGGIORNAMENTO NUCLEARE CELLE...');

        // 🚢 AGGIORNAMENTO NUCLEARE CELLA NAVE
        const cellaNave = riga.querySelector('.cell-nave');
        if (cellaNave) {
            const nomeNaveNucleare = viaggio.nome_nave || 'N/A';
            cellaNave.innerHTML = `<strong>${nomeNaveNucleare}</strong>`;
            console.log('✅ CELLA NAVE AGGIORNATA NUCLEARE:', nomeNaveNucleare);

            // Verifica doppia per sicurezza nucleare
            if (cellaNave.textContent.includes('ATLANTIC STAR')) {
                console.error('🚨 ERRORE NUCLEARE: ATLANTIC STAR ancora presente dopo aggiornamento!');
                console.log('🔄 Ricarico pagina per risolvere definitivamente...');
                window.location.reload(true);
                return;
            }
        }

        // 🛳️ AGGIORNAMENTO NUCLEARE PORTO ARRIVO
        const cellaPortoArrivo = riga.querySelector('.cell-porto-arrivo');
        if (cellaPortoArrivo) {
            console.log('🔄 Aggiornamento nucleare porto arrivo...');

            if (viaggio.porto_arrivo && viaggio.porto_arrivo.trim()) {
                // Ottieni il nome del porto dall'API Atlas
                const portoArrivoNome = await ottieniNomePortoNucleare(viaggio.porto_arrivo);
                if (portoArrivoNome && portoArrivoNome !== 'N/A') {
                    cellaPortoArrivo.innerHTML = `<span class="badge bg-info text-dark">${portoArrivoNome}</span>`;
                    console.log('✅ PORTO ARRIVO AGGIORNATO NUCLEARE:', portoArrivoNome);
                } else {
                    cellaPortoArrivo.innerHTML = `<span class="text-muted">N/A</span>`;
                    console.log('⚠️ Porto arrivo nucleare non trovato, impostato N/A');
                }
            } else {
                cellaPortoArrivo.innerHTML = `<span class="text-muted">N/A</span>`;
                console.log('⚠️ Porto arrivo nucleare vuoto, impostato N/A');
            }
        }

        // 🛳️ AGGIORNAMENTO NUCLEARE PORTO DESTINAZIONE
        const cellaPortoDestinazione = riga.querySelector('.cell-porto-destinazione');
        if (cellaPortoDestinazione) {
            console.log('🔄 Aggiornamento nucleare porto destinazione...');

            if (viaggio.porto_destinazione && viaggio.porto_destinazione.trim()) {
                // Ottieni il nome del porto dall'API Atlas
                const portoDestNome = await ottieniNomePortoNucleare(viaggio.porto_destinazione);
                if (portoDestNome && portoDestNome !== 'N/A') {
                    cellaPortoDestinazione.innerHTML = `<span class="badge bg-warning text-dark">${portoDestNome}</span>`;
                    console.log('✅ PORTO DESTINAZIONE AGGIORNATO NUCLEARE:', portoDestNome);
                } else {
                    cellaPortoDestinazione.innerHTML = `<span class="text-muted">N/A</span>`;
                    console.log('⚠️ Porto destinazione nucleare non trovato, impostato N/A');
                }
            } else {
                cellaPortoDestinazione.innerHTML = `<span class="text-muted">N/A</span>`;
                console.log('⚠️ Porto destinazione nucleare vuoto, impostato N/A');
            }
        }

        // 🎨 EVIDENZIAZIONE NUCLEARE
        riga.style.backgroundColor = '#d4edda';
        riga.style.border = '2px solid #28a745';
        riga.style.transition = 'all 0.5s ease';

        setTimeout(() => {
            riga.style.backgroundColor = '';
            riga.style.border = '';
        }, 5000); // 5 secondi per vedere l'effetto nucleare

        console.log('🎉🎉🎉 AGGIORNAMENTO NUCLEARE COMPLETATO CON SUCCESSO! 🎉🎉🎉');

        // 🔍 VERIFICA FINALE NUCLEARE
        const naveFinale = cellaNave ? cellaNave.textContent : 'N/A';
        console.log('🔍 VERIFICA FINALE NUCLEARE - Nave mostrata:', naveFinale);

        if (naveFinale.includes('ATLANTIC STAR')) {
            console.error('🚨🚨🚨 ERRORE NUCLEARE CRITICO: ATLANTIC STAR ANCORA PRESENTE!');
            console.log('🔄 RICARICO PAGINA COME ULTIMA RISORSA...');
            window.location.reload(true);
        } else {
            console.log('✅✅✅ VERIFICA NUCLEARE SUPERATA: Nave corretta mostrata!');
        }

    } catch (error) {
        console.error('💥💥💥 ERRORE NUCLEARE CRITICO:', error);
        console.log('🔄 Fallback nucleare: ricarico pagina completa');
        window.location.reload(true);
    }
}

// Rendi le funzioni globalmente accessibili
window.aggiornaRigaViaggio = aggiornaRigaViaggio;
