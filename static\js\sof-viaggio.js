// JavaScript per gestione SOF (Statement of Facts) viaggio
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 SOF JavaScript caricato');
    console.log('🆔 VIAGGIO_ID:', typeof VIAGGIO_ID !== 'undefined' ? VIAGGIO_ID : 'NON DEFINITO');

    const generateSofBtn = document.getElementById('generate_sof_btn');
    const downloadSofBtn = document.getElementById('download_sof_btn');
    const downloadCompletedSofBtn = document.getElementById('download_completed_sof_btn');
    const sofStatusText = document.getElementById('sof_status_text');
    const sofLastUpdate = document.getElementById('sof_last_update');
    const sofPreviewContainer = document.getElementById('sof_preview_container');

    console.log('🔍 Elementi trovati:');
    console.log('  - generateSofBtn:', !!generateSofBtn);
    console.log('  - downloadSofBtn:', !!downloadSofBtn);
    console.log('  - downloadCompletedSofBtn:', !!downloadCompletedSofBtn);
    console.log('  - sofStatusText:', !!sofStatusText);

    let sofData = null;

    // Gestione generazione SOF (solo per viaggi visibili)
    if (generateSofBtn) {
        generateSofBtn.addEventListener('click', function() {
            generateSOF();
        });
    }

    // Gestione download SOF (per viaggi in corso di generazione)
    if (downloadSofBtn) {
        downloadSofBtn.addEventListener('click', function() {
            downloadSOF();
        });
    }

    // Gestione download SOF completato (per viaggi con SOF già generato)
    if (downloadCompletedSofBtn) {
        console.log('✅ Event listener aggiunto al pulsante download SOF completato');
        downloadCompletedSofBtn.addEventListener('click', function() {
            console.log('🖱️ Pulsante SCARICA SOF COMPLETATO cliccato');
            downloadCompletedSOF();
        });
    } else {
        console.log('❌ Pulsante download SOF completato non trovato');
    }

    // Funzione per generare il SOF
    function generateSOF() {
        updateSofStatus('Generazione in corso...', 'warning');
        generateSofBtn.disabled = true;
        generateSofBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Generazione...';

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/sof/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                sofData = data.sof_data;
                updateSofStatus('SOF generato con successo', 'success');
                showSofPreview(data.sof_data);
                updateStatistics(data.statistics);
                downloadSofBtn.disabled = false;

                // Aggiorna timestamp
                const now = new Date();
                sofLastUpdate.textContent = now.toLocaleString('it-IT');

                // Mostra messaggio di successo
                if (typeof mostraSuccesso === 'function') {
                    mostraSuccesso('SOF generato con successo! Ora puoi scaricarlo in PDF.');
                }
            } else {
                updateSofStatus('Errore generazione SOF', 'error');

                // Mostra messaggio di errore
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante la generazione del SOF');
                }
            }
        })
        .catch(error => {
            updateSofStatus('Errore generazione SOF', 'error');

            // Mostra messaggio di errore
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante la generazione del SOF');
            }
        })
        .finally(() => {
            generateSofBtn.disabled = false;
            generateSofBtn.innerHTML = '<span style="font-size: 1.1em; margin-right: 8px;">🔄</span>GENERA SOF';
        });
    }

    // Funzione per scaricare il SOF in DOCX (per viaggi in corso di generazione)
    function downloadSOF() {
        if (!sofData) {
            if (typeof mostraErrore === 'function') {
                mostraErrore('Genera prima il SOF per poterlo scaricare');
            }
            return;
        }

        updateSofStatus('Download DOCX in corso...', 'warning');
        downloadSofBtn.disabled = true;
        downloadSofBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Download...';

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/sof/download`, {
            method: 'POST'
        })
        .then(response => {
            if (response.ok) {
                return response.blob().then(blob => {
                    // Estrai il filename dall'header Content-Disposition
                    const contentDisposition = response.headers.get('content-disposition');
                    let filename = `SOF_Viaggio_${VIAGGIO_ID}.docx`;

                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename=(.+)/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    return { blob, filename };
                });
            } else {
                throw new Error('Errore durante il download del SOF DOCX');
            }
        })
        .then(({ blob, filename }) => {
            // Avvia il download del DOCX
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // Aggiorna lo stato
            updateSofStatus('SOF DOCX scaricato con successo', 'success');

            // Mostra messaggio di successo
            if (typeof mostraSuccesso === 'function') {
                mostraSuccesso(`SOF DOCX scaricato: ${filename}`);
            }
        })
        .catch(error => {
            updateSofStatus('Errore download SOF DOCX', 'error');

            // Mostra messaggio di errore
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore durante il download del SOF DOCX');
            }
        })
        .finally(() => {
            downloadSofBtn.disabled = false;
            downloadSofBtn.innerHTML = '<span style="font-size: 1.1em; margin-right: 8px;">📥</span>SCARICA SOF';
        });
    }

    // Funzione per scaricare SOF già completato (per viaggi con SOF già generato)
    function downloadCompletedSOF() {
        console.log('🚀 Avvio download SOF completato per viaggio:', VIAGGIO_ID);

        downloadCompletedSofBtn.disabled = true;
        downloadCompletedSofBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Download...';

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/sof/download`, {
            method: 'POST'
        })
        .then(response => {
            if (response.ok) {
                return response.blob().then(blob => {
                    // Estrai il filename dall'header Content-Disposition
                    const contentDisposition = response.headers.get('content-disposition');
                    let filename = `SOF_Viaggio_${VIAGGIO_ID}_Completato.docx`;

                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename=(.+)/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    return { blob, filename };
                });
            } else {
                throw new Error('Errore durante il download del SOF completato');
            }
        })
        .then(({ blob, filename }) => {
            // Avvia il download del DOCX
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // Mostra messaggio di successo
            if (typeof mostraSuccesso === 'function') {
                mostraSuccesso(`SOF completato scaricato: ${filename}`);
            }
        })
        .catch(error => {
            // Mostra messaggio di errore
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore durante il download del SOF completato');
            }
        })
        .finally(() => {
            downloadCompletedSofBtn.disabled = false;
            downloadCompletedSofBtn.innerHTML = '<span style="font-size: 1.1em; margin-right: 8px;">📥</span>SCARICA SOF COMPLETATO';
        });
    }

    // Funzione per mostrare l'anteprima del SOF
    function showSofPreview(sofData) {
        if (!sofData || !sofData.content) {
            sofPreviewContainer.innerHTML = `
                <div class="sof-preview-placeholder">
                    <div style="font-size: 2em; margin-bottom: 10px;">❌</div>
                    <p class="mb-0">Errore nel caricamento dell'anteprima</p>
                </div>
            `;
            return;
        }

        // Mostra le prime righe del SOF
        const lines = sofData.content.split('\n');
        const previewLines = lines.slice(0, 20); // Prime 20 righe

        let html = '<div class="sof-preview-container sof-preview-content p-3 rounded">';
        previewLines.forEach(line => {
            html += `<div>${line || '&nbsp;'}</div>`;
        });

        if (lines.length > 20) {
            html += `<div class="text-muted mt-2">... e altre ${lines.length - 20} righe</div>`;
        }

        html += '</div>';

        sofPreviewContainer.innerHTML = html;

        // Forza l'applicazione degli stili per il tema marittimo
        setTimeout(() => {
            const previewContainer = sofPreviewContainer.querySelector('.sof-preview-container');
            if (previewContainer && document.body.classList.contains('theme-maritime')) {
                console.log('🎨 Applicazione forzata stili tema marittimo per anteprima SOF');
                previewContainer.style.backgroundColor = '#ffffff';
                previewContainer.style.color = '#212529';
                previewContainer.style.border = '3px solid #ffd700';
                previewContainer.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.3)';

                // Forza il colore per tutti i figli
                const children = previewContainer.querySelectorAll('*');
                children.forEach(child => {
                    child.style.color = '#212529';
                    child.style.backgroundColor = 'transparent';
                });
            }
        }, 100);
    }

    // Funzione per aggiornare le statistiche
    function updateStatistics(stats) {
        if (!stats) return;

        document.getElementById('stat_import_records').textContent = stats.import_records || '0';
        document.getElementById('stat_export_records').textContent = stats.export_records || '0';
        document.getElementById('stat_total_qt').textContent = stats.total_qt || '0';
        document.getElementById('stat_unique_ports').textContent = stats.unique_ports || '0';
    }

    // Funzione per aggiornare lo stato del SOF
    function updateSofStatus(text, type) {
        sofStatusText.textContent = text;
        sofStatusText.className = type === 'success' ? 'text-success' :
                                  type === 'error' ? 'text-danger' :
                                  type === 'warning' ? 'text-warning' : '';
    }

    // Carica statistiche iniziali
    loadInitialStatistics();

    function loadInitialStatistics() {
        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/sof/statistics`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatistics(data.statistics);
                }
            })
            .catch(error => {
                console.error('Errore caricamento statistiche:', error);
            });
    }
});

// Funzione globale per controllare se tutte le lucine sono verdi
function checkAllStatusIndicators() {
    const orariStatus = document.getElementById('orari-status');
    const importStatus = document.getElementById('import-status');
    const exportStatus = document.getElementById('export-status');
    const sofTabContainer = document.getElementById('sof-tab-container');

    if (!orariStatus || !importStatus || !exportStatus || !sofTabContainer) {
        console.error('❌ Elementi status indicator non trovati');
        return;
    }

    // Controlla se tutte le lucine sono verdi
    const orariGreen = orariStatus.classList.contains('status-green');
    const importGreen = importStatus.classList.contains('status-green');
    const exportGreen = exportStatus.classList.contains('status-green');

    const allGreen = orariGreen && importGreen && exportGreen;

    console.log(`🔍 Controllo lucine: ORARI=${orariGreen}, IMPORT=${importGreen}, EXPORT=${exportGreen} → SOF=${allGreen}`);

    // Mostra/nasconde il tab SOF
    if (allGreen) {
        sofTabContainer.style.display = 'block';
        console.log('✅ Tutte le lucine sono verdi - Tab SOF mostrato');

        // Mostra notifica
        if (typeof mostraSuccesso === 'function') {
            mostraSuccesso('🎉 Tutti i dati sono completi! Tab SOF disponibile.');
        }
    } else {
        sofTabContainer.style.display = 'none';
        console.log('❌ Non tutte le lucine sono verdi - Tab SOF nascosto');
    }
}

// Funzione per aggiornare lo stato e controllare le lucine
function updateStatusAndCheck() {
    // Aspetta un momento per assicurarsi che le lucine siano aggiornate
    setTimeout(checkAllStatusIndicators, 100);
}

// Osserva i cambiamenti nelle lucine usando MutationObserver
function setupStatusObserver() {
    const orariStatus = document.getElementById('orari-status');
    const importStatus = document.getElementById('import-status');
    const exportStatus = document.getElementById('export-status');

    if (!orariStatus || !importStatus || !exportStatus) {
        console.error('❌ Impossibile configurare observer per status indicators');
        return;
    }

    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                console.log('🔄 Cambiamento rilevato in una lucina');
                updateStatusAndCheck();
            }
        });
    });

    // Osserva i cambiamenti di classe per tutte le lucine
    observer.observe(orariStatus, { attributes: true, attributeFilter: ['class'] });
    observer.observe(importStatus, { attributes: true, attributeFilter: ['class'] });
    observer.observe(exportStatus, { attributes: true, attributeFilter: ['class'] });

    console.log('👁️ Observer configurato per monitorare le lucine');
}

// Inizializza l'observer quando il DOM è pronto
document.addEventListener('DOMContentLoaded', function() {
    // Aspetta un momento per assicurarsi che tutti gli elementi siano caricati
    setTimeout(() => {
        setupStatusObserver();
        checkAllStatusIndicators(); // Controllo iniziale
    }, 1000);
});
