/**
 * Funzionalità per la rigenerazione automatica e forzata dei SOF
 */

class SOFRegenerationManager {
    constructor() {
        this.baseUrl = window.location.origin;
        this.init();
    }

    init() {
        // Aggiungi listener per i pulsanti di rigenerazione
        this.attachRegenerateButtons();
        
        // Aggiungi controlli automatici per modifiche dati
        this.setupAutoRegeneration();
    }

    /**
     * Aggiunge i pulsanti di rigenerazione forzata ai SOF esistenti
     */
    attachRegenerateButtons() {
        // Trova tutti i SOF nella pagina e aggiungi pulsanti di rigenerazione
        const sofElements = document.querySelectorAll('[data-viaggio-id]');
        
        sofElements.forEach(element => {
            const viaggioId = element.getAttribute('data-viaggio-id');
            if (viaggioId && !element.querySelector('.regenerate-sof-btn')) {
                this.addRegenerateButton(element, viaggioId);
            }
        });
    }

    /**
     * Aggiunge un pulsante di rigenerazione per un viaggio specifico
     */
    addRegenerateButton(container, viaggioId) {
        const button = document.createElement('button');
        button.className = 'btn btn-warning btn-sm regenerate-sof-btn ms-2';
        button.innerHTML = '<i class="fas fa-sync-alt"></i> Rigenera SOF';
        button.title = 'Forza la rigenerazione del SOF con i dati aggiornati';
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            this.forceRegenerateSof(viaggioId);
        });
        
        // Trova il posto migliore per inserire il pulsante
        const downloadButton = container.querySelector('.download-sof-btn, .btn-primary');
        if (downloadButton && downloadButton.parentNode) {
            downloadButton.parentNode.insertBefore(button, downloadButton.nextSibling);
        } else {
            container.appendChild(button);
        }
    }

    /**
     * Forza la rigenerazione di un SOF
     */
    async forceRegenerateSof(viaggioId) {
        const button = document.querySelector(`[data-viaggio-id="${viaggioId}"] .regenerate-sof-btn`);
        
        if (button) {
            // Disabilita il pulsante e mostra loading
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Rigenerando...';
        }

        try {
            const response = await fetch(`${this.baseUrl}/operativo/sof/viaggio/${viaggioId}/regenerate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin' // Include cookies di sessione
            });

            const result = await response.json();

            if (response.ok && result.success) {
                // Successo
                this.showNotification('success', 'SOF eliminato con successo! Ora puoi rigenerarlo con i dati aggiornati.');
                
                // Ricarica la pagina dopo un breve delay per mostrare il viaggio come disponibile
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } else {
                // Errore
                const errorMsg = result.message || 'Errore durante la rigenerazione';
                this.showNotification('error', errorMsg);
                console.error('Errore rigenerazione SOF:', result);
            }

        } catch (error) {
            console.error('Errore rete durante rigenerazione SOF:', error);
            this.showNotification('error', 'Errore di connessione durante la rigenerazione');
        } finally {
            // Ripristina il pulsante
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync-alt"></i> Rigenera SOF';
            }
        }
    }

    /**
     * Configura il controllo automatico per la rigenerazione
     */
    setupAutoRegeneration() {
        // Aggiungi listener per modifiche ai form di orari e dati
        const forms = document.querySelectorAll('form[data-auto-regenerate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const viaggioId = form.getAttribute('data-viaggio-id');
                if (viaggioId) {
                    // Mostra avviso che il SOF potrebbe essere rigenerato automaticamente
                    this.showAutoRegenerationNotice(viaggioId);
                }
            });
        });
    }

    /**
     * Mostra un avviso sulla rigenerazione automatica
     */
    showAutoRegenerationNotice(viaggioId) {
        this.showNotification('info', 
            'I dati sono stati modificati. Se esiste un SOF per questo viaggio, verrà automaticamente rigenerato con i nuovi dati al prossimo download.',
            5000
        );
    }

    /**
     * Verifica se un SOF necessita rigenerazione
     */
    async checkSOFStatus(viaggioId) {
        try {
            const response = await fetch(`${this.baseUrl}/operativo/sof/viaggio/${viaggioId}/sof/statistics`);
            const result = await response.json();
            
            if (response.ok && result.success) {
                return result.statistics;
            }
        } catch (error) {
            console.error('Errore verifica stato SOF:', error);
        }
        return null;
    }

    /**
     * Mostra una notifica all'utente
     */
    showNotification(type, message, duration = 4000) {
        // Crea elemento notifica
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Rimuovi automaticamente dopo il timeout
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    /**
     * Aggiunge indicatori visivi per SOF che potrebbero necessitare rigenerazione
     */
    addRegenerationIndicators() {
        const sofElements = document.querySelectorAll('[data-viaggio-id][data-sof-exists="true"]');
        
        sofElements.forEach(async (element) => {
            const viaggioId = element.getAttribute('data-viaggio-id');
            const stats = await this.checkSOFStatus(viaggioId);
            
            if (stats) {
                // Aggiungi badge con informazioni
                const badge = document.createElement('span');
                badge.className = 'badge bg-info ms-2';
                badge.innerHTML = `<i class="fas fa-chart-bar"></i> ${stats.import_records + stats.export_records} record`;
                badge.title = `Import: ${stats.import_records}, Export: ${stats.export_records}, QT totale: ${stats.total_qt}`;
                
                const titleElement = element.querySelector('h5, h6, .card-title');
                if (titleElement) {
                    titleElement.appendChild(badge);
                }
            }
        });
    }
}

// Inizializza il manager quando il DOM è pronto
document.addEventListener('DOMContentLoaded', () => {
    window.sofRegenerationManager = new SOFRegenerationManager();
    
    // Aggiungi indicatori dopo un breve delay
    setTimeout(() => {
        window.sofRegenerationManager.addRegenerationIndicators();
    }, 1000);
});

// Funzioni globali per compatibilità
window.forceRegenerateSof = (viaggioId) => {
    if (window.sofRegenerationManager) {
        window.sofRegenerationManager.forceRegenerateSof(viaggioId);
    }
};

window.checkSOFStatus = (viaggioId) => {
    if (window.sofRegenerationManager) {
        return window.sofRegenerationManager.checkSOFStatus(viaggioId);
    }
    return null;
};
