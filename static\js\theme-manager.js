// ===== SNIP THEME MANAGER =====
// Sistema di gestione temi per l'applicazione SNIP

class SNIPThemeManager {
    constructor() {
        this.currentTheme = 'maritime'; // Default theme
        this.themes = {
            light: 'Tema <PERSON>',
            dark: 'Tema Sc<PERSON>',
            maritime: 'Te<PERSON>'
        };
        
        this.isAuthenticated = false;
        this.init();
    }
    
    init() {
        // Verifica se l'utente è autenticato
        this.checkAuthentication();
        
        // Carica tema salvato
        this.loadUserTheme();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('🎨 SNIP Theme Manager inizializzato');
    }
    
    checkAuthentication() {
        // Verifica se siamo in una pagina autenticata (presenza di elementi specifici)
        this.isAuthenticated = document.querySelector('.user-dropdown') !== null ||
                              document.querySelector('#userDropdown') !== null ||
                              document.querySelector('.notification-bell') !== null;
        
        console.log('🔐 Utente autenticato:', this.isAuthenticated);
    }
    
    async loadUserTheme() {
        // Prima controlla se il tema è già impostato nel body (dal server)
        const bodyTheme = document.body.getAttribute('data-theme');
        if (bodyTheme && this.isThemeValid(bodyTheme)) {
            this.currentTheme = bodyTheme;
            console.log('🎨 Tema caricato dal server (body):', this.currentTheme);
            this.applyTheme(this.currentTheme);
            return;
        }

        if (this.isAuthenticated) {
            try {
                // Carica tema dal server per utenti autenticati
                const response = await fetch('/api/user/theme', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    this.currentTheme = data.theme || 'maritime';
                    console.log('🎨 Tema utente caricato dal server API:', this.currentTheme);
                } else {
                    console.warn('⚠️ Errore caricamento tema utente, uso default');
                    this.currentTheme = 'maritime';
                }
            } catch (error) {
                console.warn('⚠️ Errore connessione server per tema, uso default:', error);
                this.currentTheme = 'maritime';
            }
        } else {
            // Per utenti non autenticati, usa localStorage
            this.currentTheme = localStorage.getItem('snip-theme') || 'light';
            console.log('🎨 Tema caricato da localStorage:', this.currentTheme);
        }

        // Applica il tema caricato
        this.applyTheme(this.currentTheme);
    }
    
    async saveUserTheme(theme) {
        if (this.isAuthenticated) {
            try {
                // Salva tema sul server per utenti autenticati
                const response = await fetch('/api/user/theme', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ theme: theme })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Tema salvato sul server:', data.message);
                    return true;
                } else {
                    console.error('❌ Errore salvataggio tema sul server');
                    return false;
                }
            } catch (error) {
                console.error('❌ Errore connessione server per salvataggio tema:', error);
                return false;
            }
        } else {
            // Per utenti non autenticati, usa localStorage
            localStorage.setItem('snip-theme', theme);
            console.log('💾 Tema salvato in localStorage:', theme);
            return true;
        }
    }
    
    applyTheme(theme) {
        // ⚠️ NON applicare tema dinamicamente per evitare conflitti tra utenti
        // Il tema viene applicato dal server nel template

        // Aggiorna solo currentTheme per tracking
        this.currentTheme = theme;

        // Aggiorna UI se presente
        this.updateThemeUI();

        console.log(`🎨 Tema registrato (non applicato dinamicamente): ${theme}`);
        console.log(`💡 Il tema sarà applicato al prossimo caricamento pagina`);
    }
    
    async change(newTheme) {
        if (!this.themes[newTheme]) {
            console.error('❌ Tema non valido:', newTheme);
            return false;
        }

        // Salva il nuovo tema
        const saved = await this.saveUserTheme(newTheme);

        if (saved) {
            // Mostra notifica di successo
            this.showThemeChangeNotification(newTheme);

            // Ricarica la pagina per applicare il nuovo tema dal server
            setTimeout(() => {
                console.log('🔄 Ricaricamento pagina per applicare nuovo tema...');
                window.location.reload();
            }, 1500); // Aspetta che la notifica sia visibile

            return true;
        } else {
            console.error('❌ Errore salvataggio tema');
            return false;
        }
    }
    
    updateThemeUI() {
        // Aggiorna selettori tema se presenti
        const themeSelectors = document.querySelectorAll('[data-theme-selector]');
        themeSelectors.forEach(selector => {
            if (selector.dataset.themeSelector === this.currentTheme) {
                selector.classList.add('active');
            } else {
                selector.classList.remove('active');
            }
        });
        
        // Aggiorna dropdown tema se presente
        const themeDropdown = document.querySelector('#themeDropdown');
        if (themeDropdown) {
            themeDropdown.textContent = this.themes[this.currentTheme];
        }
    }
    
    showThemeChangeNotification(theme) {
        // Crea notifica temporanea
        const notification = document.createElement('div');
        notification.className = 'theme-change-notification';
        notification.innerHTML = `
            <i class="fas fa-palette me-2"></i>
            Tema cambiato a: ${this.themes[theme]}
            <br><small><i class="fas fa-sync-alt me-1"></i>Ricaricamento in corso...</small>
        `;
        
        // Stili inline per la notifica
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: '9999',
            fontSize: '14px',
            fontWeight: '500',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(notification);
        
        // Animazione entrata
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Rimozione automatica
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    setupEventListeners() {
        // Event listener per selettori tema
        document.addEventListener('click', (e) => {
            if (e.target.dataset.themeSelector) {
                e.preventDefault();
                this.change(e.target.dataset.themeSelector);
            }
        });
        
        // Event listener per dropdown tema
        document.addEventListener('change', (e) => {
            if (e.target.id === 'themeSelect') {
                this.change(e.target.value);
            }
        });
    }
    
    // Metodi pubblici per compatibilità
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    getAvailableThemes() {
        return this.themes;
    }
    
    isThemeValid(theme) {
        return this.themes.hasOwnProperty(theme);
    }
}

// Inizializza il theme manager quando il DOM è pronto
document.addEventListener('DOMContentLoaded', () => {
    window.SNIPThemes = new SNIPThemeManager();
});

// Export per compatibilità
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SNIPThemeManager;
}
