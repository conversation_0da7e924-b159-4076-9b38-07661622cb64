// JavaScript per gestione utenti completa al 100%
let allUsers = [];
let filteredUsers = [];
let currentPage = 1;
let usersPerPage = 10;
let sortField = 'id_user';
let sortDirection = 'asc';

// Configurazioni di sicurezza dinamiche
let securityConfig = {
    password_min_length: 6,
    password_max_length: 128,
    password_uppercase: false,
    password_numbers: false,
    password_special: false
};

// Carica configurazioni di sicurezza
async function loadSecurityConfig() {
    try {
        const response = await fetch('/api/security-config');
        const data = await response.json();

        if (data.success) {
            securityConfig = data.config;
            console.log('🔧 Configurazioni sicurezza caricate per gestione utenti:', securityConfig);
        }
    } catch (error) {
        console.log('⚠️ Usando configurazioni predefinite per password');
    }
}

// Validazione password dinamica
async function validatePasswordDynamic(password) {
    if (!password) {
        return { valid: false, message: 'Password richiesta' };
    }

    if (password.length < securityConfig.password_min_length) {
        return { valid: false, message: `Password troppo corta (minimo ${securityConfig.password_min_length} caratteri)` };
    }

    if (password.length > securityConfig.password_max_length) {
        return { valid: false, message: `Password troppo lunga (massimo ${securityConfig.password_max_length} caratteri)` };
    }

    if (securityConfig.password_uppercase && !/[A-Z]/.test(password)) {
        return { valid: false, message: 'Password deve contenere almeno una maiuscola' };
    }

    if (securityConfig.password_numbers && !/[0-9]/.test(password)) {
        return { valid: false, message: 'Password deve contenere almeno un numero' };
    }

    if (securityConfig.password_special && !/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
        return { valid: false, message: 'Password deve contenere almeno un carattere speciale' };
    }

    return { valid: true, message: 'Password valida' };
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 User Management JavaScript caricato');
    // Carica prima le configurazioni, poi gli utenti
    loadSecurityConfig().then(() => {
        loadUsers();
    });
});

// Carica tutti gli utenti
async function loadUsers() {
    console.log('📥 Caricamento utenti...');
    
    try {
        showLoading();
        
        const response = await fetch('/admin/api/users', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            allUsers = data.users || [];
            filteredUsers = [...allUsers];
            
            console.log(`✅ Caricati ${allUsers.length} utenti`);
            
            updateUsersCount();
            renderUsersTable();
            renderPagination();
        } else {
            throw new Error(`Errore HTTP: ${response.status}`);
        }
    } catch (error) {
        console.error('❌ Errore caricamento utenti:', error);
        showError('Errore durante il caricamento degli utenti: ' + error.message);
    }
}

// Mostra loading nella tabella
function showLoading() {
    const tbody = document.getElementById('users-table-body');
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
                <div class="mt-2">Caricamento utenti...</div>
            </td>
        </tr>
    `;
}

// Renderizza la tabella utenti
function renderUsersTable() {
    const tbody = document.getElementById('users-table-body');
    
    if (filteredUsers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h5>Nessun utente trovato</h5>
                        <p>Non ci sono utenti che corrispondono ai criteri di ricerca.</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const pageUsers = filteredUsers.slice(startIndex, endIndex);
    
    let html = '';
    pageUsers.forEach(user => {
        const statusBadge = user.visibile === 'si' 
            ? '<span class="badge bg-success">Attivo</span>'
            : '<span class="badge bg-danger">Inattivo</span>';
            
        const roleBadge = getRoleBadge(user.ruolo);
        const lastAccess = user.ultimo_accesso ? formatDate(user.ultimo_accesso) : 'N/A';
        
        html += `
            <tr>
                <td>
                    <input type="checkbox" class="user-checkbox" value="${user.id_user}" onchange="updateBulkActions()">
                </td>
                <td><strong>${user.id_user}</strong></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                            ${user.nome ? user.nome.charAt(0).toUpperCase() : 'U'}
                        </div>
                        <div>
                            <div class="fw-bold">${user.nome || ''} ${user.cognome || ''}</div>
                            <small class="text-muted">ID: ${user.id_user}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div>
                        <i class="fas fa-envelope me-1 text-muted"></i>
                        ${user.email || 'N/A'}
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">${user.reparto || 'N/A'}</span>
                </td>
                <td>${roleBadge}</td>
                <td>${statusBadge}</td>
                <td>
                    <small class="text-muted">${lastAccess}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editUser(${user.id_user})" title="Modifica">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewUserDetails(${user.id_user})" title="Dettagli">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="resetUserPassword(${user.id_user})" title="Reset Password">
                            <i class="fas fa-key"></i>
                        </button>
                        <button class="btn btn-outline-${user.visibile === 'si' ? 'secondary' : 'success'}" 
                                onclick="toggleUserStatus(${user.id_user})" 
                                title="${user.visibile === 'si' ? 'Disattiva' : 'Attiva'}">
                            <i class="fas fa-${user.visibile === 'si' ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id_user})" title="Elimina">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// Ottieni badge per ruolo
function getRoleBadge(ruolo) {
    const badges = {
        'SUPER_ADMIN': '<span class="badge bg-danger">Super Admin</span>',
        'ADMIN': '<span class="badge bg-warning">Admin</span>',
        'USER': '<span class="badge bg-primary">User</span>',
        'VISITOR': '<span class="badge bg-secondary">Visitor</span>'
    };
    return badges[ruolo] || '<span class="badge bg-light text-dark">N/A</span>';
}

// Formatta data
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT') + ' ' + date.toLocaleTimeString('it-IT', {hour: '2-digit', minute: '2-digit'});
}

// Aggiorna contatore utenti
function updateUsersCount() {
    document.getElementById('users-count').textContent = filteredUsers.length;
}

// Ricerca live
function searchUsersLive() {
    const searchTerm = document.getElementById('user-search').value.toLowerCase();
    
    if (searchTerm === '') {
        filteredUsers = [...allUsers];
    } else {
        filteredUsers = allUsers.filter(user => 
            (user.nome && user.nome.toLowerCase().includes(searchTerm)) ||
            (user.cognome && user.cognome.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm)) ||
            (user.reparto && user.reparto.toLowerCase().includes(searchTerm))
        );
    }
    
    currentPage = 1;
    updateUsersCount();
    renderUsersTable();
    renderPagination();
}

// Pulisci ricerca
function clearSearch() {
    document.getElementById('user-search').value = '';
    document.getElementById('filter-reparto').value = '';
    document.getElementById('filter-ruolo').value = '';
    document.getElementById('filter-stato').value = '';
    
    filteredUsers = [...allUsers];
    currentPage = 1;
    updateUsersCount();
    renderUsersTable();
    renderPagination();
}

// Filtra utenti
function filterUsers() {
    const repartoFilter = document.getElementById('filter-reparto').value;
    const ruoloFilter = document.getElementById('filter-ruolo').value;
    const statoFilter = document.getElementById('filter-stato').value;
    const searchTerm = document.getElementById('user-search').value.toLowerCase();
    
    filteredUsers = allUsers.filter(user => {
        const matchesSearch = !searchTerm || 
            (user.nome && user.nome.toLowerCase().includes(searchTerm)) ||
            (user.cognome && user.cognome.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm));
            
        const matchesReparto = !repartoFilter || user.reparto === repartoFilter;
        const matchesRuolo = !ruoloFilter || user.ruolo === ruoloFilter;
        const matchesStato = !statoFilter || user.visibile === statoFilter;
        
        return matchesSearch && matchesReparto && matchesRuolo && matchesStato;
    });
    
    currentPage = 1;
    updateUsersCount();
    renderUsersTable();
    renderPagination();
}

// Ordina utenti
function sortUsers(field) {
    if (sortField === field) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortField = field;
        sortDirection = 'asc';
    }
    
    filteredUsers.sort((a, b) => {
        let aVal = a[field] || '';
        let bVal = b[field] || '';
        
        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }
        
        if (sortDirection === 'asc') {
            return aVal > bVal ? 1 : -1;
        } else {
            return aVal < bVal ? 1 : -1;
        }
    });
    
    renderUsersTable();
    updateSortIcons();
}

// Aggiorna icone ordinamento
function updateSortIcons() {
    document.querySelectorAll('th i.fas').forEach(icon => {
        icon.className = 'fas fa-sort';
    });
    
    const currentHeader = document.querySelector(`th[onclick="sortUsers('${sortField}')"] i`);
    if (currentHeader) {
        currentHeader.className = `fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'}`;
    }
}

// Renderizza paginazione
function renderPagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const pagination = document.getElementById('users-pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // Previous
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // Pages
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // Next
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

// Cambia pagina
function changePage(page) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderUsersTable();
        renderPagination();
    }
}

// Toggle selezione tutti
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all-users');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

// Aggiorna azioni multiple
function updateBulkActions() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    
    if (selectedCheckboxes.length > 0) {
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }
    
    // Aggiorna stato select-all
    const allCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectAll = document.getElementById('select-all-users');
    
    if (selectedCheckboxes.length === allCheckboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else if (selectedCheckboxes.length > 0) {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    }
}

// Funzioni di utilità
function showError(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Errore',
            text: message,
            confirmButtonColor: '#dc3545'
        });
    } else {
        alert('Errore: ' + message);
    }
}

function showSuccess(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Successo',
            text: message,
            confirmButtonColor: '#198754'
        });
    } else {
        alert('Successo: ' + message);
    }
}

// Refresh utenti
function refreshUsers() {
    loadUsers();
}

// Esporta utenti
function exportUsers() {
    console.log('📤 Esportazione utenti...');
    // TODO: Implementare esportazione
    showSuccess('Funzionalità di esportazione in sviluppo');
}

// === CRUD OPERATIONS ===

// Mostra modal per nuovo utente
function showCreateUserModal() {
    console.log('➕ Apertura modal nuovo utente');

    // Reset form
    document.getElementById('userForm').reset();
    document.getElementById('user-id').value = '';

    // Aggiorna titolo
    document.getElementById('userModalLabel').innerHTML = '<i class="fas fa-user-plus me-2"></i>Nuovo Utente';

    // Mostra sezione password
    document.getElementById('password-section').style.display = 'block';
    document.getElementById('user-password').required = true;
    document.getElementById('user-confirm-password').required = true;

    // Mostra modal
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// Modifica utente
async function editUser(userId) {
    console.log('✏️ Modifica utente:', userId);

    try {
        const response = await fetch(`/admin/api/users/${userId}`);
        if (response.ok) {
            const user = await response.json();

            // Popola form
            document.getElementById('user-id').value = user.id_user;
            document.getElementById('user-nome').value = user.nome || '';
            document.getElementById('user-cognome').value = user.cognome || '';
            document.getElementById('user-email').value = user.email || '';
            document.getElementById('user-reparto').value = user.reparto || '';
            document.getElementById('user-ruolo').value = user.ruolo || '';
            document.getElementById('user-visibile').value = user.visibile || 'si';

            // Aggiorna titolo
            document.getElementById('userModalLabel').innerHTML = '<i class="fas fa-user-edit me-2"></i>Modifica Utente';

            // Nascondi sezione password per modifica
            document.getElementById('password-section').style.display = 'none';
            document.getElementById('user-password').required = false;
            document.getElementById('user-confirm-password').required = false;

            // Mostra modal
            const modal = new bootstrap.Modal(document.getElementById('userModal'));
            modal.show();
        } else {
            throw new Error('Utente non trovato');
        }
    } catch (error) {
        console.error('❌ Errore caricamento utente:', error);
        showError('Errore durante il caricamento dei dati utente');
    }
}

// Salva utente (create/update)
async function saveUser() {
    console.log('💾 Salvataggio utente...');

    const form = document.getElementById('userForm');
    const formData = new FormData(form);
    const userId = document.getElementById('user-id').value;

    // Validazione password per nuovo utente
    if (!userId) {
        const password = formData.get('password');
        const confirmPassword = formData.get('confirm_password');

        // Usa validazione dinamica
        const validation = await validatePasswordDynamic(password);
        if (!validation.valid) {
            showError(validation.message);
            return;
        }

        if (password !== confirmPassword) {
            showError('Le password non corrispondono');
            return;
        }
    }

    try {
        const url = userId ? `/admin/api/users/${userId}` : '/admin/api/users';
        const method = userId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(userId ? 'Utente aggiornato con successo' : 'Utente creato con successo');

            // Chiudi modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();

            // Ricarica utenti
            loadUsers();
        } else {
            const error = await response.json();
            throw new Error(error.message || 'Errore durante il salvataggio');
        }
    } catch (error) {
        console.error('❌ Errore salvataggio utente:', error);
        showError('Errore durante il salvataggio: ' + error.message);
    }
}

// Visualizza dettagli utente
async function viewUserDetails(userId) {
    console.log('👁️ Visualizza dettagli utente:', userId);

    try {
        const response = await fetch(`/admin/api/users/${userId}`);
        if (response.ok) {
            const user = await response.json();

            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>ID:</strong> ${user.id_user}</p>
                        <p><strong>Nome:</strong> ${user.nome || 'N/A'} ${user.cognome || ''}</p>
                        <p><strong>Email:</strong> ${user.email || 'N/A'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Reparto:</strong> ${user.reparto || 'N/A'}</p>
                        <p><strong>Ruolo:</strong> ${user.ruolo || 'N/A'}</p>
                        <p><strong>Stato:</strong> ${user.visibile === 'si' ? 'Attivo' : 'Inattivo'}</p>
                    </div>
                </div>
            `;

            Swal.fire({
                title: `<i class="fas fa-user me-2"></i>Dettagli Utente`,
                html: details,
                width: '600px',
                confirmButtonText: 'Chiudi',
                confirmButtonColor: '#6c757d'
            });
        } else {
            throw new Error('Utente non trovato');
        }
    } catch (error) {
        console.error('❌ Errore caricamento dettagli:', error);
        showError('Errore durante il caricamento dei dettagli utente');
    }
}

// Reset password utente
async function resetUserPassword(userId) {
    console.log('🔑 Reset password utente:', userId);

    const result = await Swal.fire({
        title: 'Reset Password',
        text: 'Sei sicuro di voler resettare la password di questo utente?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sì, resetta',
        cancelButtonText: 'Annulla'
    });

    if (result.isConfirmed) {
        try {
            const response = await fetch(`/admin/api/users/${userId}/reset-password`, {
                method: 'POST'
            });

            if (response.ok) {
                const result = await response.json();

                Swal.fire({
                    title: 'Password Resettata',
                    html: `La nuova password temporanea è: <br><strong style="font-family: monospace; font-size: 1.2em;">${result.new_password}</strong><br><br>L'utente dovrà cambiarla al primo accesso.`,
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                throw new Error('Errore durante il reset della password');
            }
        } catch (error) {
            console.error('❌ Errore reset password:', error);
            showError('Errore durante il reset della password');
        }
    }
}

// Toggle stato utente
async function toggleUserStatus(userId) {
    console.log('🔄 Toggle stato utente:', userId);

    try {
        const response = await fetch(`/admin/api/users/${userId}/toggle-status`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(`Utente ${result.status === 'si' ? 'attivato' : 'disattivato'} con successo`);
            loadUsers();
        } else {
            throw new Error('Errore durante il cambio stato');
        }
    } catch (error) {
        console.error('❌ Errore toggle stato:', error);
        showError('Errore durante il cambio stato utente');
    }
}

// Elimina utente
async function deleteUser(userId) {
    console.log('🗑️ Elimina utente:', userId);

    const result = await Swal.fire({
        title: 'Elimina Utente',
        text: 'Sei sicuro di voler eliminare questo utente? Questa azione non può essere annullata.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sì, elimina',
        cancelButtonText: 'Annulla'
    });

    if (result.isConfirmed) {
        try {
            const response = await fetch(`/admin/api/users/${userId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                showSuccess('Utente eliminato con successo');
                loadUsers();
            } else {
                throw new Error('Errore durante l\'eliminazione');
            }
        } catch (error) {
            console.error('❌ Errore eliminazione utente:', error);
            showError('Errore durante l\'eliminazione utente');
        }
    }
}

// === BULK OPERATIONS ===

// Attiva utenti selezionati
async function bulkActivateUsers() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) {
        showError('Seleziona almeno un utente');
        return;
    }

    console.log('✅ Attivazione multipla utenti:', selectedIds);

    try {
        const response = await fetch('/admin/api/users/bulk-activate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ user_ids: selectedIds })
        });

        if (response.ok) {
            showSuccess(`${selectedIds.length} utenti attivati con successo`);
            loadUsers();
        } else {
            throw new Error('Errore durante l\'attivazione multipla');
        }
    } catch (error) {
        console.error('❌ Errore attivazione multipla:', error);
        showError('Errore durante l\'attivazione multipla');
    }
}

// Disattiva utenti selezionati
async function bulkDeactivateUsers() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) {
        showError('Seleziona almeno un utente');
        return;
    }

    console.log('⏸️ Disattivazione multipla utenti:', selectedIds);

    try {
        const response = await fetch('/admin/api/users/bulk-deactivate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ user_ids: selectedIds })
        });

        if (response.ok) {
            showSuccess(`${selectedIds.length} utenti disattivati con successo`);
            loadUsers();
        } else {
            throw new Error('Errore durante la disattivazione multipla');
        }
    } catch (error) {
        console.error('❌ Errore disattivazione multipla:', error);
        showError('Errore durante la disattivazione multipla');
    }
}

// Cambia reparto utenti selezionati
async function bulkChangeReparto() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) {
        showError('Seleziona almeno un utente');
        return;
    }

    const { value: newReparto } = await Swal.fire({
        title: 'Cambia Reparto',
        input: 'select',
        inputOptions: {
            'OPERATIVO': 'Operativo',
            'AMMINISTRAZIONE': 'Amministrazione',
            'CONTABILITA': 'Contabilità',
            'SHORTSEA': 'Shortsea'
        },
        inputPlaceholder: 'Seleziona nuovo reparto',
        showCancelButton: true,
        confirmButtonText: 'Cambia',
        cancelButtonText: 'Annulla'
    });

    if (newReparto) {
        console.log('🏢 Cambio reparto multiplo:', selectedIds, 'a', newReparto);

        try {
            const response = await fetch('/admin/api/users/bulk-change-reparto', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_ids: selectedIds,
                    new_reparto: newReparto
                })
            });

            if (response.ok) {
                showSuccess(`Reparto cambiato per ${selectedIds.length} utenti`);
                loadUsers();
            } else {
                throw new Error('Errore durante il cambio reparto');
            }
        } catch (error) {
            console.error('❌ Errore cambio reparto multiplo:', error);
            showError('Errore durante il cambio reparto');
        }
    }
}

// Elimina utenti selezionati
async function bulkDeleteUsers() {
    const selectedIds = getSelectedUserIds();
    if (selectedIds.length === 0) {
        showError('Seleziona almeno un utente');
        return;
    }

    const result = await Swal.fire({
        title: 'Elimina Utenti',
        text: `Sei sicuro di voler eliminare ${selectedIds.length} utenti? Questa azione non può essere annullata.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sì, elimina tutti',
        cancelButtonText: 'Annulla'
    });

    if (result.isConfirmed) {
        console.log('🗑️ Eliminazione multipla utenti:', selectedIds);

        try {
            const response = await fetch('/admin/api/users/bulk-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_ids: selectedIds })
            });

            if (response.ok) {
                showSuccess(`${selectedIds.length} utenti eliminati con successo`);
                loadUsers();
            } else {
                throw new Error('Errore durante l\'eliminazione multipla');
            }
        } catch (error) {
            console.error('❌ Errore eliminazione multipla:', error);
            showError('Errore durante l\'eliminazione multipla');
        }
    }
}

// Ottieni IDs utenti selezionati
function getSelectedUserIds() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

// Toggle visibilità password
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        button.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        button.className = 'fas fa-eye';
    }
}
