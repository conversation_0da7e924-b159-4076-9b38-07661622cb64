// @ts-nocheck
/* eslint-disable */
/**
 * JavaScript per la gestione del dettaglio viaggio e orari
 * File: viaggio-dettaglio.js
 * Language: JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    const orariForm = document.getElementById('orariForm');
    const portoArrivoSelect = document.getElementById('porto_arrivo');
    const portoDestinazioneSelect = document.getElementById('porto_di_destinazione');
    const orariStatus = document.getElementById('orari-status');

    // Debug: mostra i dati ricevuti dal server
    console.log('ORARI_ESISTENTI ricevuti:', ORARI_ESISTENTI);

    // Carica i porti ATLAS per le dropdown e poi imposta i valori
    caricaPortiAtlas().then(() => {
        console.log('🎯 ATLAS caricato, inizio logica pre-popolamento...');
        console.log('🔍 ORARI_ESISTENTI:', ORARI_ESISTENTI);
        console.log('🔍 VIAGGIO_ID:', VIAGGIO_ID);

        // Imposta i valori esistenti se ci sono
        if (ORARI_ESISTENTI) {
            console.log('📋 Usando orari esistenti...');
            impostaValoriEsistenti();
        } else {
            console.log('🚢 Nessun orario esistente, pre-popolo dai dati viaggio...');
            // Se non ci sono orari esistenti, pre-popola con i dati del viaggio
            prePopulaPortiDaViaggio();
        }

        // Aggiorna lo stato iniziale DOPO che tutto è stato caricato
        aggiornaStatoOrari();
    }).catch(error => {
        console.error('❌ Errore caricamento ATLAS:', error);
        // Anche se ATLAS fallisce, prova il pre-popolamento
        console.log('🔄 Tentativo pre-popolamento senza ATLAS...');
        if (!ORARI_ESISTENTI) {
            prePopulaPortiDaViaggio();
        }
        aggiornaStatoOrari();
    });

    // Aggiungi listener per tutti i campi per aggiornare lo stato in tempo reale
    const campiOrari = orariForm.querySelectorAll('input, select');
    campiOrari.forEach(campo => {
        campo.addEventListener('change', aggiornaStatoOrari);
        campo.addEventListener('input', aggiornaStatoOrari);
    });

    // Gestione submit form
    orariForm.addEventListener('submit', function(e) {
        e.preventDefault();
        salvaOrari();
    });

    /**
     * Carica i porti dalla tabella ATLAS
     */
    async function caricaPortiAtlas() {
        try {
            const response = await fetch('/api/atlas?limit=1000');
            const data = await response.json();

            if (data.success) {
                // Popola entrambe le dropdown
                popolaSelectPorti(portoArrivoSelect, data.data);
                popolaSelectPorti(portoDestinazioneSelect, data.data);

                console.log('Porti caricati, impostazione valori esistenti...');

                // Se ci sono orari esistenti, imposta i valori selezionati
                if (ORARI_ESISTENTI) {
                    if (ORARI_ESISTENTI.porto_arrivo) {
                        portoArrivoSelect.value = ORARI_ESISTENTI.porto_arrivo;
                        console.log('Impostato porto_arrivo da orari esistenti:', ORARI_ESISTENTI.porto_arrivo);
                    }
                    if (ORARI_ESISTENTI.porto_di_destinazione) {
                        portoDestinazioneSelect.value = ORARI_ESISTENTI.porto_di_destinazione;
                        console.log('Impostato porto_di_destinazione da orari esistenti:', ORARI_ESISTENTI.porto_di_destinazione);
                    }
                } else {
                    // Se non ci sono orari esistenti, pre-popola con i dati del viaggio
                    prePopulaPortiDaViaggio();
                }

                return Promise.resolve();
            } else {
                console.error('Errore nel caricamento porti ATLAS:', data.error);
                mostraErrore('Errore nel caricamento dei porti');
                return Promise.reject(data.error);
            }
        } catch (error) {
            console.error('Errore nella richiesta porti ATLAS:', error);
            mostraErrore('Errore di connessione nel caricamento dei porti');
            return Promise.reject(error);
        }
    }

    /**
     * Popola una select con i porti
     */
    function popolaSelectPorti(selectElement, porti) {
        selectElement.innerHTML = '<option value="">Seleziona porto...</option>';
        porti.forEach(porto => {
            const option = document.createElement('option');
            option.value = porto.id_cod;
            option.textContent = porto.porto; // Solo il nome del porto
            selectElement.appendChild(option);
        });
    }

    /**
     * Pre-popola i campi porto con i dati del viaggio se disponibili
     */
    async function prePopulaPortiDaViaggio() {
        try {
            console.log('🚢 Pre-popolamento porti da dati viaggio...');
            console.log('🔍 VIAGGIO_ID:', VIAGGIO_ID);

            // Ottieni i dati del viaggio dal server
            const response = await fetch(`/api/viaggi/${VIAGGIO_ID}`);
            console.log('📡 Response status:', response.status);

            const data = await response.json();
            console.log('📋 Dati completi ricevuti:', JSON.stringify(data, null, 2));

            if (data.success && data.data) {
                const viaggio = data.data;
                console.log('📋 Dati viaggio estratti:', viaggio);
                console.log('🔍 Porto arrivo nel viaggio:', viaggio.porto_arrivo);
                console.log('🔍 Porto destinazione nel viaggio:', viaggio.porto_destinazione);
                console.log('🔍 Elementi DOM disponibili:', {
                    portoArrivoSelect: !!portoArrivoSelect,
                    portoDestinazioneSelect: !!portoDestinazioneSelect
                });

                // Pre-popola porto di arrivo se presente nel viaggio
                if (viaggio.porto_arrivo && portoArrivoSelect) {
                    console.log('🔧 Tentativo pre-popolamento porto_arrivo...');
                    console.log('   Valore da impostare:', viaggio.porto_arrivo);
                    console.log('   Opzioni disponibili:', Array.from(portoArrivoSelect.options).map(o => ({value: o.value, text: o.text})));

                    // PATCH: Aspetta un momento per essere sicuri che le opzioni siano caricate
                    setTimeout(() => {
                        portoArrivoSelect.value = viaggio.porto_arrivo;
                        console.log('   Valore impostato (dopo timeout):', portoArrivoSelect.value);

                        // Verifica se il valore è stato impostato correttamente
                        if (portoArrivoSelect.value === viaggio.porto_arrivo) {
                            console.log('✅ Pre-popolato porto_arrivo dal viaggio:', viaggio.porto_arrivo);

                            // Aggiungi un indicatore visivo che il campo è stato pre-popolato
                            portoArrivoSelect.style.backgroundColor = '#e8f5e8';
                            portoArrivoSelect.title = 'Campo pre-popolato dai dati del viaggio';
                        } else {
                            console.log('⚠️ Pre-popolamento porto_arrivo fallito - valore non trovato nelle opzioni');

                            // Prova a trovare un'opzione che contiene il valore
                            const options = Array.from(portoArrivoSelect.options);
                            const matchingOption = options.find(opt => opt.value === viaggio.porto_arrivo);

                            if (matchingOption) {
                                console.log('🔧 Opzione trovata, forzo selezione...');
                                matchingOption.selected = true;
                                portoArrivoSelect.style.backgroundColor = '#e8f5e8';
                            } else {
                                console.log('❌ Opzione non trovata per porto_arrivo:', viaggio.porto_arrivo);
                            }
                        }
                    }, 100);
                } else {
                    console.log('⚠️ Pre-popolamento porto_arrivo saltato:', {
                        hasPortoArrivo: !!viaggio.porto_arrivo,
                        hasElement: !!portoArrivoSelect,
                        portoArrivoValue: viaggio.porto_arrivo
                    });
                }

                // Pre-popola porto di destinazione se presente nel viaggio
                if (viaggio.porto_destinazione && portoDestinazioneSelect) {
                    console.log('🔧 Tentativo pre-popolamento porto_destinazione...');
                    console.log('   Valore da impostare:', viaggio.porto_destinazione);
                    console.log('   Opzioni disponibili:', Array.from(portoDestinazioneSelect.options).map(o => ({value: o.value, text: o.text})));

                    // PATCH: Aspetta un momento per essere sicuri che le opzioni siano caricate
                    setTimeout(() => {
                        portoDestinazioneSelect.value = viaggio.porto_destinazione;
                        console.log('   Valore impostato (dopo timeout):', portoDestinazioneSelect.value);

                        // Verifica se il valore è stato impostato correttamente
                        if (portoDestinazioneSelect.value === viaggio.porto_destinazione) {
                            console.log('✅ Pre-popolato porto_destinazione dal viaggio:', viaggio.porto_destinazione);

                            // Aggiungi un indicatore visivo che il campo è stato pre-popolato
                            portoDestinazioneSelect.style.backgroundColor = '#e8f5e8';
                            portoDestinazioneSelect.title = 'Campo pre-popolato dai dati del viaggio';
                        } else {
                            console.log('⚠️ Pre-popolamento porto_destinazione fallito - valore non trovato nelle opzioni');

                            // Prova a trovare un'opzione che contiene il valore
                            const options = Array.from(portoDestinazioneSelect.options);
                            const matchingOption = options.find(opt => opt.value === viaggio.porto_destinazione);

                            if (matchingOption) {
                                console.log('🔧 Opzione trovata, forzo selezione...');
                                matchingOption.selected = true;
                                portoDestinazioneSelect.style.backgroundColor = '#e8f5e8';
                            } else {
                                console.log('❌ Opzione non trovata per porto_destinazione:', viaggio.porto_destinazione);
                            }
                        }
                    }, 150); // Timeout leggermente diverso per evitare conflitti
                } else {
                    console.log('⚠️ Pre-popolamento porto_destinazione saltato:', {
                        hasPortoDestinazione: !!viaggio.porto_destinazione,
                        hasElement: !!portoDestinazioneSelect,
                        portoDestinazioneValue: viaggio.porto_destinazione
                    });
                }

                // Mostra un messaggio informativo se almeno un porto è stato pre-popolato
                if (viaggio.porto_arrivo || viaggio.porto_destinazione) {
                    console.log('🎯 Porti pre-popolati con successo dai dati del viaggio');

                    // Mostra un messaggio discreto
                    if (typeof mostraAvviso === 'function') {
                        mostraAvviso('I porti sono stati pre-popolati dai dati del viaggio. Puoi modificarli se necessario.');
                    }
                } else {
                    console.log('⚠️ Nessun porto da pre-popolare nel viaggio');
                }
            } else {
                console.log('⚠️ Nessun dato viaggio disponibile per pre-popolamento porti');
                console.log('   Success:', data.success);
                console.log('   Data:', data.data);
                console.log('   Error:', data.error);
            }
        } catch (error) {
            console.error('❌ Errore nel pre-popolamento porti da viaggio:', error);
            // Non mostrare errore all'utente, è solo un miglioramento UX
        }
    }

    /**
     * Imposta i valori esistenti nei campi
     */
    function impostaValoriEsistenti() {
        // I valori datetime sono già impostati nel template HTML
        // Qui gestiamo eventuali conversioni se necessarie

        // Formatta le date per i campi datetime-local se necessario
        const campiDatetime = ['sbe', 'pilota_arrivo', 'all_fast', 'soc', 'pilota_partenza', 'foc'];
        campiDatetime.forEach(campo => {
            const input = document.getElementById(campo);
            if (input && ORARI_ESISTENTI[campo]) {
                // Converte il formato datetime dal database al formato datetime-local
                const date = new Date(ORARI_ESISTENTI[campo]);
                if (!isNaN(date.getTime())) {
                    input.value = formatDatetimeLocal(date);
                }
            }
        });

        // Gestisce i campi numerici Tug (accetta anche il valore 0)
        const campiTug = ['tug_arrivo', 'tug_partenza'];
        campiTug.forEach(campo => {
            const input = document.getElementById(campo);
            if (input && ORARI_ESISTENTI && ORARI_ESISTENTI[campo] !== null && ORARI_ESISTENTI[campo] !== undefined) {
                input.value = ORARI_ESISTENTI[campo];
            } else if (input && (!ORARI_ESISTENTI || ORARI_ESISTENTI[campo] === null || ORARI_ESISTENTI[campo] === undefined)) {
                // Se non ci sono orari esistenti o il valore è null, imposta 0 come default
                input.value = '0';
            }
        });

        // Gestisce i campi carburanti (FO, DO, LO) con default 0.00
        const campiCarburanti = ['fo', 'do', 'lo'];
        campiCarburanti.forEach(campo => {
            const input = document.getElementById(campo);
            if (input && ORARI_ESISTENTI && ORARI_ESISTENTI[campo] !== null && ORARI_ESISTENTI[campo] !== undefined) {
                input.value = ORARI_ESISTENTI[campo];
            } else if (input && (!ORARI_ESISTENTI || ORARI_ESISTENTI[campo] === null || ORARI_ESISTENTI[campo] === undefined)) {
                // Se non ci sono orari esistenti o il valore è null, imposta 0.00 come default
                input.value = '0.00';
            }
        });
    }

    /**
     * Formatta una data per il campo datetime-local
     */
    function formatDatetimeLocal(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    /**
     * Aggiorna lo stato degli orari (indicatore verde/rosso)
     */
    function aggiornaStatoOrari() {
        const campiObbligatori = [
            'porto_arrivo', 'sbe', 'pilota_arrivo', 'all_fast',
            'tug_arrivo', 'draft', 'soc', 'porto_di_destinazione',
            'pilota_partenza', 'tug_partenza', 'foc', 'fo', 'do', 'lo'
        ];

        let tuttiCompilati = true;
        let campiVuoti = [];

        for (const campo of campiObbligatori) {
            const input = document.getElementById(campo);

            // Per i campi tug_arrivo e tug_partenza, accetta anche il valore 0
            if (campo === 'tug_arrivo' || campo === 'tug_partenza') {
                if (!input || input.value === '' || input.value === null || input.value === undefined) {
                    tuttiCompilati = false;
                    campiVuoti.push(`${campo}: "${input ? input.value : 'input non trovato'}"`);
                }
            }
            // Per i campi carburanti (fo, do, lo), accetta anche il valore 0.00
            else if (campo === 'fo' || campo === 'do' || campo === 'lo') {
                if (!input || input.value === '' || input.value === null || input.value === undefined) {
                    tuttiCompilati = false;
                    campiVuoti.push(`${campo}: "${input ? input.value : 'input non trovato'}"`);
                }
            } else {
                // Per gli altri campi, controlla che non siano vuoti
                if (!input || !input.value || input.value.trim() === '') {
                    tuttiCompilati = false;
                    campiVuoti.push(`${campo}: "${input ? input.value : 'input non trovato'}"`);
                }
            }
        }

        console.log('Validazione orari:', {
            tuttiCompilati,
            campiVuoti,
            tug_arrivo_value: document.getElementById('tug_arrivo')?.value,
            tug_partenza_value: document.getElementById('tug_partenza')?.value
        });

        // Log dettagliato di tutti i campi
        console.log('Dettaglio tutti i campi:');
        campiObbligatori.forEach(campo => {
            const input = document.getElementById(campo);
            console.log(`${campo}: "${input ? input.value : 'ELEMENTO NON TROVATO'}"`);
        });

        // Aggiorna l'indicatore di stato
        orariStatus.className = tuttiCompilati ? 'status-indicator status-green' : 'status-indicator status-red';

        // Controlla se mostrare il tab SOF
        if (typeof updateStatusAndCheck === 'function') {
            updateStatusAndCheck();
        }
    }

    /**
     * Salva gli orari
     */
    async function salvaOrari() {
        console.log('Inizio salvataggio orari');
        const formData = new FormData(orariForm);
        const submitBtn = orariForm.querySelector('button[type="submit"]');

        console.log('Pulsante trovato:', submitBtn);

        // Definisci il testo originale una volta sola
        const originalHTML = '<i class="fas fa-save me-2"></i><br>SALVA<br>ORARI';

        // Funzione per ripristinare il pulsante
        function ripristinaPulsante() {
            console.log('Ripristino pulsante');
            if (submitBtn) {
                submitBtn.innerHTML = originalHTML;
                submitBtn.disabled = false;
            }
        }

        try {
            // Mostra loading
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><br>SALVATAGGIO<br>IN CORSO...';
            submitBtn.disabled = true;

            console.log('Invio richiesta al server');
            const response = await fetch(`/api/orari/${VIAGGIO_ID}`, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            console.log('Risposta ricevuta:', data);

            if (data.success) {
                console.log('Successo - mostro messaggio e ripristino pulsante');
                mostraSuccesso('Orari salvati con successo!');
                aggiornaStatoOrari(); // Aggiorna lo stato dopo il salvataggio

                // Ripristina il pulsante dopo un breve delay per assicurarsi che il messaggio sia visibile
                setTimeout(ripristinaPulsante, 100);
            } else {
                console.log('Errore dal server - ripristino pulsante');
                mostraErrore(data.error || 'Errore nel salvataggio degli orari');
                ripristinaPulsante();
            }
        } catch (error) {
            console.error('Errore nel salvataggio orari:', error);
            mostraErrore('Errore di connessione nel salvataggio degli orari');
            ripristinaPulsante();
        }
    }

    /**
     * 🎨 MESSAGGI SPETTACOLARI - Ora usano il sistema SNIP unificato
     */
    function mostraSuccesso(messaggio) {
        // Usa il sistema SNIP Messages se disponibile
        if (typeof window.snipMessages !== 'undefined') {
            return window.snipMessages.success('🎉 Successo!', messaggio);
        }

        // Fallback per compatibilità
        const alert = document.createElement('div');
        alert.className = 'alert alert-dismissible fade show position-fixed';
        alert.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 350px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
            font-weight: 900;
            backdrop-filter: blur(10px);
            animation: slideInRight 0.5s ease-out;
        `;
        alert.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="me-3" style="font-size: 1.5em;">✅</div>
                <div class="flex-grow-1">
                    <div style="font-size: 1.1em; margin-bottom: 2px;">Successo!</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">${messaggio}</div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"
                        style="filter: brightness(0) invert(1);"></button>
            </div>
        `;
        document.body.appendChild(alert);

        // Rimuovi automaticamente dopo 5 secondi
        setTimeout(() => {
            if (alert.parentNode) {
                alert.classList.add('fade-out');
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }
        }, 5000);
    }

    /**
     * Mostra un messaggio di errore con stile moderno
     */
    function mostraErrore(messaggio) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-dismissible fade show position-fixed';
        alert.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 350px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            color: white;
            font-weight: 900;
            backdrop-filter: blur(10px);
            animation: slideInRight 0.5s ease-out;
        `;
        alert.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="me-3" style="font-size: 1.5em;">❌</div>
                <div class="flex-grow-1">
                    <div style="font-size: 1.1em; margin-bottom: 2px;">Errore!</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">${messaggio}</div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"
                        style="filter: brightness(0) invert(1);"></button>
            </div>
        `;
        document.body.appendChild(alert);

        // Rimuovi automaticamente dopo 7 secondi
        setTimeout(() => {
            if (alert.parentNode) {
                alert.classList.add('fade-out');
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }
        }, 7000);
    }

    /**
     * 🎨 MESSAGGI AVVISO SPETTACOLARI - Ora usano il sistema SNIP unificato
     */
    function mostraAvviso(messaggio) {
        // Usa il sistema SNIP Messages se disponibile
        if (typeof window.snipMessages !== 'undefined') {
            return window.snipMessages.warning('⚡ Attenzione!', messaggio);
        }

        // Fallback per compatibilità
        const alert = document.createElement('div');
        alert.className = 'alert alert-dismissible fade show position-fixed';
        alert.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 350px;
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            color: #212529;
            font-weight: 900;
            backdrop-filter: blur(10px);
            animation: slideInRight 0.5s ease-out;
        `;
        alert.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="me-3" style="font-size: 1.5em;">⚠️</div>
                <div class="flex-grow-1">
                    <div style="font-size: 1.1em; margin-bottom: 2px;">Attenzione!</div>
                    <div style="font-size: 0.9em; opacity: 0.8;">${messaggio}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        document.body.appendChild(alert);

        // Rimuovi automaticamente dopo 6 secondi
        setTimeout(() => {
            if (alert.parentNode) {
                alert.classList.add('fade-out');
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }
        }, 6000);
    }

    /**
     * Mostra un dialog di conferma moderno per l'eliminazione
     */
    function mostraConfermaEliminazione(callback) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.style.cssText = 'z-index: 10000;';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border: none; border-radius: 20px; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.3);">
                    <div class="modal-header text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border: none;">
                        <div class="d-flex align-items-center">
                            <div class="me-3" style="font-size: 2em;">🗑️</div>
                            <div>
                                <h5 class="modal-title mb-0" style="font-weight: 900;">Conferma Eliminazione</h5>
                                <small style="opacity: 0.9;">Operazione irreversibile</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-body" style="padding: 2rem;">
                        <div class="text-center mb-4">
                            <div style="font-size: 4em; margin-bottom: 1rem;">⚠️</div>
                            <h6 style="color: #dc3545; font-weight: 900; margin-bottom: 1rem;">Attenzione!</h6>
                            <p style="font-size: 1.1em; color: #495057; line-height: 1.6;">
                                Sei sicuro di voler eliminare <strong>TUTTI i dati IMPORT</strong> di questo viaggio?
                            </p>
                            <div class="alert alert-warning" style="border-radius: 15px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border: none;">
                                <strong>⚠️ Questa operazione non può essere annullata!</strong><br>
                                <small>Tutti i record IMPORT verranno eliminati definitivamente.</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="border: none; padding: 1.5rem 2rem;">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                style="border-radius: 15px; padding: 10px 25px; font-weight: 900;">
                            <span style="margin-right: 8px;">❌</span>Annulla
                        </button>
                        <button type="button" class="btn btn-danger" id="confirm-delete-btn"
                                style="border-radius: 15px; padding: 10px 25px; font-weight: 900; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border: none;">
                            <span style="margin-right: 8px;">🗑️</span>Elimina Tutto
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Inizializza modal Bootstrap
        const bsModal = new bootstrap.Modal(modal);

        // Event listener per il pulsante conferma
        modal.querySelector('#confirm-delete-btn').addEventListener('click', function() {
            bsModal.hide();
            callback();
        });

        // Rimuovi modal dal DOM quando viene chiuso
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });

        // Mostra modal
        bsModal.show();
    }

    // Rendi le funzioni globali per l'uso in altri file
    window.mostraSuccesso = mostraSuccesso;
    window.mostraErrore = mostraErrore;
    window.mostraAvviso = mostraAvviso;
    window.mostraConfermaEliminazione = mostraConfermaEliminazione;
});
