<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestione Notifiche per Reparto - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .notification-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
        }
        
        .notification-card.info { border-left-color: #17a2b8; }
        .notification-card.warning { border-left-color: #ffc107; }
        .notification-card.success { border-left-color: #28a745; }
        .notification-card.error { border-left-color: #dc3545; }
        .notification-card.urgent { border-left-color: #6f42c1; }
        
        .btn-create {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
        }
        
        .btn-create:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        
        .badge-priority {
            font-size: 0.8em;
            padding: 5px 10px;
            border-radius: 15px;
        }
        
        .priority-1 { background-color: #6c757d; }
        .priority-2 { background-color: #17a2b8; }
        .priority-3 { background-color: #ffc107; color: #000; }
        .priority-4 { background-color: #dc3545; }
        
        .user-menu {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Menu utente -->
    <div class="user-menu">
        <div class="dropdown">
            <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-user"></i> {{ current_user.Cognome }}
            </button>
            <ul class="dropdown-menu">
                {% if current_user.ruolo.value == 'SUPER_ADMIN' %}
                <li><a class="dropdown-item" href="/dashboard/amministrazione"><i class="fas fa-tachometer-alt"></i> Dashboard Admin</a></li>
                {% endif %}
                <li><a class="dropdown-item" href="/admin/notifiche"><i class="fas fa-bell"></i> Gestione Notifiche</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-bell"></i> Gestione Notifiche per Reparto</h1>
                <p class="mb-0">Crea e gestisci notifiche specifiche per ogni reparto</p>

                <!-- Statistiche -->
                <div class="row mt-4" id="statsSection">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="mb-1" id="totalNotifications">-</h3>
                            <small>Totale Notifiche</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="mb-1" id="activeNotifications">-</h3>
                            <small>Attive</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="mb-1" id="oldNotifications">-</h3>
                            <small>Vecchie (>30gg)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="mb-1" id="unreadNotifications">-</h3>
                            <small>Non Lette</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sezione creazione notifica -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-plus"></i> Crea Nuova Notifica</h5>
                        </div>
                        <div class="card-body">
                            <form id="createNotificationForm">
                                <div class="mb-3">
                                    <label class="form-label">Titolo</label>
                                    <input type="text" class="form-control" id="title" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Messaggio</label>
                                    <textarea class="form-control" id="message" rows="4" required></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Tipo</label>
                                            <select class="form-select" id="type" required>
                                                <option value="INFO">📋 Info</option>
                                                <option value="WARNING">⚠️ Avviso</option>
                                                <option value="SUCCESS">✅ Successo</option>
                                                <option value="ERROR">❌ Errore</option>
                                                <option value="URGENT">🚨 Urgente</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Reparto</label>
                                            <select class="form-select" id="reparto" required>
                                                <option value="OPERATIVO">🚢 Operativo</option>
                                                <option value="AMMINISTRAZIONE">🏢 Amministrazione</option>
                                                <option value="CONTABILITA">💰 Contabilità</option>
                                                <option value="SHORTSEA">⚓ Shortsea</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Priorità</label>
                                            <select class="form-select" id="priority">
                                                <option value="1">1 - Bassa</option>
                                                <option value="2" selected>2 - Media</option>
                                                <option value="3">3 - Alta</option>
                                                <option value="4">4 - Urgente</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Scadenza (giorni)</label>
                                            <input type="number" class="form-control" id="expires_days" min="1" max="365" placeholder="Opzionale">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_email">
                                        <label class="form-check-label" for="send_email">
                                            📧 Invia anche via email agli utenti del reparto
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-create w-100">
                                    <i class="fas fa-paper-plane"></i> Crea Notifica
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sezione notifiche esistenti -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> Notifiche Esistenti</h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-danger" onclick="showDeleteAllModal()">
                                    <i class="fas fa-trash-alt"></i> Elimina Tutte
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="showCleanupModal()">
                                    <i class="fas fa-broom"></i> Pulizia
                                </button>
                                <button class="btn btn-sm btn-light" onclick="loadNotifications()">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Filtri -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm" id="filterReparto" onchange="loadNotifications()">
                                        <option value="">🏢 Tutti i Reparti</option>
                                        <option value="OPERATIVO">🚢 Operativo</option>
                                        <option value="AMMINISTRAZIONE">🏢 Amministrazione</option>
                                        <option value="CONTABILITA">💰 Contabilità</option>
                                        <option value="SHORTSEA">⚓ Shortsea</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm" id="filterType" onchange="loadNotifications()">
                                        <option value="">📋 Tutti i Tipi</option>
                                        <option value="INFO">📋 Info</option>
                                        <option value="WARNING">⚠️ Avviso</option>
                                        <option value="SUCCESS">✅ Successo</option>
                                        <option value="ERROR">❌ Errore</option>
                                        <option value="URGENT">🚨 Urgente</option>
                                    </select>
                                </div>
                            </div>

                            <div class="loading" id="loadingNotifications">
                                <i class="fas fa-spinner fa-spin"></i> Caricamento...
                            </div>
                            <div id="notificationsList"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Elimina Tutte le Notifiche -->
    <div class="modal fade" id="deleteAllModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="fas fa-trash-alt"></i> Elimina Tutte le Notifiche</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>ATTENZIONE!</strong> Questa operazione eliminerà <strong>TUTTE</strong> le notifiche dal sistema in modo permanente.
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Filtra per reparto (opzionale):</label>
                        <select class="form-select" id="deleteAllReparto">
                            <option value="">🗑️ TUTTE le notifiche di TUTTI i reparti</option>
                            <option value="OPERATIVO">🚢 Solo notifiche OPERATIVO</option>
                            <option value="AMMINISTRAZIONE">🏢 Solo notifiche AMMINISTRAZIONE</option>
                            <option value="CONTABILITA">💰 Solo notifiche CONTABILITÀ</option>
                            <option value="SHORTSEA">⚓ Solo notifiche SHORTSEA</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmDeleteAll">
                            <label class="form-check-label text-danger fw-bold" for="confirmDeleteAll">
                                ✅ Confermo di voler eliminare TUTTE le notifiche selezionate
                            </label>
                        </div>
                    </div>

                    <div id="deleteAllPreview" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle"></i>
                        <span id="deleteAllPreviewText"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-info" onclick="previewDeleteAll()">
                        <i class="fas fa-search"></i> Anteprima
                    </button>
                    <button type="button" class="btn btn-danger" onclick="executeDeleteAll()" disabled id="deleteAllBtn">
                        <i class="fas fa-trash-alt"></i> ELIMINA TUTTE
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Pulizia Notifiche -->
    <div class="modal fade" id="cleanupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title"><i class="fas fa-broom"></i> Pulizia Notifiche Vecchie</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Attenzione!</strong> Questa operazione eliminerà definitivamente le notifiche più vecchie del periodo selezionato.
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Elimina notifiche più vecchie di:</label>
                        <select class="form-select" id="cleanupDays">
                            <option value="7">7 giorni</option>
                            <option value="15">15 giorni</option>
                            <option value="30" selected>30 giorni</option>
                            <option value="60">60 giorni</option>
                            <option value="90">90 giorni</option>
                            <option value="180">6 mesi</option>
                            <option value="365">1 anno</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Filtra per reparto (opzionale):</label>
                        <select class="form-select" id="cleanupReparto">
                            <option value="">Tutti i reparti</option>
                            <option value="OPERATIVO">🚢 Operativo</option>
                            <option value="AMMINISTRAZIONE">🏢 Amministrazione</option>
                            <option value="CONTABILITA">💰 Contabilità</option>
                            <option value="SHORTSEA">⚓ Shortsea</option>
                        </select>
                    </div>

                    <div id="cleanupPreview" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle"></i>
                        <span id="cleanupPreviewText"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-warning" onclick="previewCleanup()">
                        <i class="fas fa-search"></i> Anteprima
                    </button>
                    <button type="button" class="btn btn-danger" onclick="executeCleanup()">
                        <i class="fas fa-trash"></i> Elimina
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Carica notifiche esistenti con filtri
        async function loadNotifications() {
            document.getElementById('loadingNotifications').style.display = 'block';

            try {
                // Ottieni filtri
                const filterReparto = document.getElementById('filterReparto').value;
                const filterType = document.getElementById('filterType').value;

                // Costruisci URL con parametri
                let url = '/admin/api/notifications';
                const params = new URLSearchParams();
                if (filterReparto) params.append('reparto', filterReparto);
                if (filterType) params.append('type', filterType);
                if (params.toString()) url += '?' + params.toString();

                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    displayNotifications(data.notifications);
                    updateStats(data.stats || {});
                } else {
                    console.error('Errore caricamento notifiche:', data.message);
                }
            } catch (error) {
                console.error('Errore:', error);
            } finally {
                document.getElementById('loadingNotifications').style.display = 'none';
            }
        }

        // Aggiorna statistiche
        function updateStats(stats) {
            document.getElementById('totalNotifications').textContent = stats.total || 0;
            document.getElementById('activeNotifications').textContent = stats.active || 0;
            document.getElementById('oldNotifications').textContent = stats.old || 0;
            document.getElementById('unreadNotifications').textContent = stats.unread || 0;
        }

        // Visualizza notifiche
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationsList');
            
            if (notifications.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">Nessuna notifica presente</p>';
                return;
            }
            
            container.innerHTML = notifications.map(notif => `
                <div class="notification-card ${notif.type.toLowerCase()}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${getTypeIcon(notif.type)} ${notif.title}</h6>
                            <p class="mb-2 text-muted small">${notif.message.substring(0, 100)}${notif.message.length > 100 ? '...' : ''}</p>
                            <div class="d-flex gap-2 flex-wrap">
                                <span class="badge bg-secondary">${notif.reparto}</span>
                                <span class="badge badge-priority priority-${notif.priority}">Priorità ${notif.priority}</span>
                                ${notif.send_email ? '<span class="badge bg-info">📧 Email</span>' : ''}
                                <span class="badge bg-light text-dark">${notif.read_count} letture</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${formatDate(notif.created_at)}</small><br>
                            <small class="text-muted">da ${notif.creator_name}</small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Icone per tipo
        function getTypeIcon(type) {
            const icons = {
                'INFO': '📋',
                'WARNING': '⚠️',
                'SUCCESS': '✅',
                'ERROR': '❌',
                'URGENT': '🚨'
            };
            return icons[type] || '📋';
        }

        // Formatta data
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('it-IT', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Gestione form creazione
        document.getElementById('createNotificationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                title: document.getElementById('title').value,
                message: document.getElementById('message').value,
                type: document.getElementById('type').value,
                reparto: document.getElementById('reparto').value,
                priority: parseInt(document.getElementById('priority').value),
                expires_days: document.getElementById('expires_days').value || null,
                send_email: document.getElementById('send_email').checked
            };
            
            try {
                const response = await fetch('/admin/api/notifications/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('✅ Notifica creata con successo!');
                    document.getElementById('createNotificationForm').reset();
                    loadNotifications(); // Ricarica la lista
                } else {
                    alert('❌ Errore: ' + data.message);
                }
            } catch (error) {
                console.error('Errore:', error);
                alert('❌ Errore durante la creazione della notifica');
            }
        });

        // Mostra modal elimina tutte
        function showDeleteAllModal() {
            const modal = new bootstrap.Modal(document.getElementById('deleteAllModal'));
            modal.show();

            // Reset form
            document.getElementById('deleteAllReparto').value = '';
            document.getElementById('confirmDeleteAll').checked = false;
            document.getElementById('deleteAllBtn').disabled = true;
            document.getElementById('deleteAllPreview').style.display = 'none';
        }

        // Abilita/disabilita pulsante elimina tutte
        document.addEventListener('DOMContentLoaded', function() {
            const checkbox = document.getElementById('confirmDeleteAll');
            const deleteBtn = document.getElementById('deleteAllBtn');

            if (checkbox && deleteBtn) {
                checkbox.addEventListener('change', function() {
                    deleteBtn.disabled = !this.checked;
                });
            }
        });

        // Anteprima elimina tutte
        async function previewDeleteAll() {
            const reparto = document.getElementById('deleteAllReparto').value;

            try {
                const params = new URLSearchParams();
                if (reparto) params.append('reparto', reparto);

                const response = await fetch(`/admin/api/notifications/delete-all/preview?${params}`);
                const data = await response.json();

                if (data.success) {
                    const previewDiv = document.getElementById('deleteAllPreview');
                    const previewText = document.getElementById('deleteAllPreviewText');

                    previewText.innerHTML = `
                        Verranno eliminate <strong>${data.count}</strong> notifiche
                        ${reparto ? ` del reparto <strong>${reparto}</strong>` : ' da tutti i reparti'}.
                        <br><small>⚠️ Questa operazione è irreversibile!</small>
                    `;
                    previewDiv.style.display = 'block';
                } else {
                    alert('❌ Errore anteprima: ' + data.message);
                }
            } catch (error) {
                console.error('Errore anteprima:', error);
                alert('❌ Errore durante l\'anteprima');
            }
        }

        // Esegui elimina tutte
        async function executeDeleteAll() {
            const reparto = document.getElementById('deleteAllReparto').value;
            const confirmed = document.getElementById('confirmDeleteAll').checked;

            if (!confirmed) {
                alert('⚠️ Devi confermare l\'eliminazione spuntando la casella.');
                return;
            }

            const confirmText = reparto
                ? `⚠️ Sei ASSOLUTAMENTE sicuro di voler eliminare TUTTE le notifiche del reparto ${reparto}?`
                : `⚠️ Sei ASSOLUTAMENTE sicuro di voler eliminare TUTTE le notifiche di TUTTI i reparti?`;

            if (!confirm(confirmText + '\n\n🚨 QUESTA OPERAZIONE NON PUÒ ESSERE ANNULLATA!')) {
                return;
            }

            // Doppia conferma per sicurezza
            if (!confirm('🚨 ULTIMA CONFERMA: Eliminare TUTTE le notifiche selezionate?')) {
                return;
            }

            try {
                const response = await fetch('/admin/api/notifications/delete-all', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reparto: reparto || null })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`✅ Eliminazione completata! Eliminate ${data.deleted_count} notifiche.`);
                    bootstrap.Modal.getInstance(document.getElementById('deleteAllModal')).hide();
                    loadNotifications(); // Ricarica la lista
                } else {
                    alert('❌ Errore eliminazione: ' + data.message);
                }
            } catch (error) {
                console.error('Errore eliminazione:', error);
                alert('❌ Errore durante l\'eliminazione');
            }
        }

        // Mostra modal pulizia
        function showCleanupModal() {
            const modal = new bootstrap.Modal(document.getElementById('cleanupModal'));
            modal.show();
        }

        // Anteprima pulizia
        async function previewCleanup() {
            const days = document.getElementById('cleanupDays').value;
            const reparto = document.getElementById('cleanupReparto').value;

            try {
                const params = new URLSearchParams({ days });
                if (reparto) params.append('reparto', reparto);

                const response = await fetch(`/admin/api/notifications/cleanup/preview?${params}`);
                const data = await response.json();

                if (data.success) {
                    const previewDiv = document.getElementById('cleanupPreview');
                    const previewText = document.getElementById('cleanupPreviewText');

                    previewText.innerHTML = `
                        Verranno eliminate <strong>${data.count}</strong> notifiche più vecchie di ${days} giorni
                        ${reparto ? ` del reparto <strong>${reparto}</strong>` : ' da tutti i reparti'}.
                        <br><small>Spazio liberato stimato: ~${data.estimated_size || 'N/A'}</small>
                    `;
                    previewDiv.style.display = 'block';
                } else {
                    alert('❌ Errore anteprima: ' + data.message);
                }
            } catch (error) {
                console.error('Errore anteprima:', error);
                alert('❌ Errore durante l\'anteprima');
            }
        }

        // Esegui pulizia
        async function executeCleanup() {
            const days = document.getElementById('cleanupDays').value;
            const reparto = document.getElementById('cleanupReparto').value;

            if (!confirm(`⚠️ Sei sicuro di voler eliminare le notifiche più vecchie di ${days} giorni? Questa operazione non può essere annullata.`)) {
                return;
            }

            try {
                const response = await fetch('/admin/api/notifications/cleanup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ days: parseInt(days), reparto: reparto || null })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`✅ Pulizia completata! Eliminate ${data.deleted_count} notifiche.`);
                    bootstrap.Modal.getInstance(document.getElementById('cleanupModal')).hide();
                    loadNotifications(); // Ricarica la lista
                } else {
                    alert('❌ Errore pulizia: ' + data.message);
                }
            } catch (error) {
                console.error('Errore pulizia:', error);
                alert('❌ Errore durante la pulizia');
            }
        }

        // Carica notifiche all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
        });
    </script>
</body>
</html>
