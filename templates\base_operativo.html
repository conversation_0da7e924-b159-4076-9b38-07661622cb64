<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}M.A.P. - Reparto Operativo{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/custom.css" rel="stylesheet">
    <link href="/static/css/global-themes.css" rel="stylesheet">
    <link href="/static/css/light-theme-contrast-fixes.css" rel="stylesheet">
    <link href="/static/css/maritime-theme-contrast-fixes.css" rel="stylesheet">
    <link href="/static/css/dark-theme-contrast-fixes.css" rel="stylesheet">

    <!-- CSS personalizzato per la menu bar moderna -->
    <style>
        /* Menu bar moderna condivisa */
        .navbar-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            backdrop-filter: blur(10px);
            border-bottom: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand-modern {
            color: white !important;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .navbar-brand-modern:hover {
            transform: scale(1.05);
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
        }

        .nav-link-modern {
            color: white !important;
            font-weight: bold;
            margin: 0 10px;
            padding: 10px 20px !important;
            border-radius: 25px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.1);
        }

        .nav-link-modern:hover {
            background: rgba(255,255,255,0.2) !important;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white !important;
        }

        .nav-link-active {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
            box-shadow: 0 4px 15px rgba(255,193,7,0.3);
        }

        .nav-link-active:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255,193,7,0.4) !important;
        }



        /* Stili personalizzati per dropdown - versione potenziata */
        .dropdown {
            position: relative !important;
        }

        .dropdown-menu {
            border-radius: 10px !important;
            border: none !important;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
            background: white !important;
            min-width: 200px !important;
            z-index: 1050 !important;
            display: none !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            margin: 0.125rem 0 0 !important;
            padding: 0.5rem 0 !important;
        }

        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        .dropdown-item {
            font-weight: bold !important;
            transition: all 0.3s ease !important;
            display: block !important;
            width: 100% !important;
            padding: 0.375rem 1rem !important;
            clear: both !important;
            color: #212529 !important;
            text-decoration: none !important;
            white-space: nowrap !important;
            background-color: transparent !important;
            border: 0 !important;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            background-color: #007bff !important;
            color: white !important;
        }

        /* Fix specifico per dropdown toggle */
        .dropdown-toggle::after {
            display: inline-block !important;
            margin-left: 0.255em !important;
            vertical-align: 0.255em !important;
            content: "" !important;
            border-top: 0.3em solid !important;
            border-right: 0.3em solid transparent !important;
            border-bottom: 0 !important;
            border-left: 0.3em solid transparent !important;
        }

        /* Debug temporaneo - rimuovere dopo test */
        .dropdown-menu {
            border: 2px solid red !important;
        }

        /* SOLUZIONE AGGRESSIVA PER DROPDOWN INVISIBILI */
        .dropdown:hover .dropdown-menu {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }

        /* Forza visibilità su click */
        .dropdown.show .dropdown-menu,
        .dropdown-menu[data-bs-popper] {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            z-index: 9999 !important;
            background: white !important;
            border: 2px solid red !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
            min-width: 200px !important;
            max-height: 400px !important;
            overflow-y: auto !important;
        }

        /* Forza stili dropdown item */
        .dropdown-item {
            display: block !important;
            width: 100% !important;
            padding: 8px 16px !important;
            margin: 0 !important;
            color: #333 !important;
            text-decoration: none !important;
            background: transparent !important;
            border: none !important;
            text-align: left !important;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            background: #007bff !important;
            color: white !important;
        }

        /* Fix per navbar dropdown specifico */
        .navbar .dropdown-menu {
            position: absolute !important;
            top: calc(100% + 5px) !important;
            left: 0 !important;
            right: auto !important;
            transform: none !important;
        }

        /* Assicura che il dropdown sia sempre sopra tutto */
        .navbar .dropdown {
            position: relative !important;
            z-index: 1000 !important;
        }

        .navbar .dropdown-menu {
            z-index: 1001 !important;
        }

        .navbar-toggler-modern {
            color: white;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            border: none;
        }

        .badge-attivo {
            margin-left: 8px;
            background: rgba(255,255,255,0.3);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7em;
        }

        /* Stili base per tutte le pagine */
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container-main {
            margin-top: 2rem;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="theme-{{ user_theme|default('maritime') }}" data-theme="{{ user_theme|default('maritime') }}">
    <!-- Navbar principale con menu utente -->
    {% include 'components/navbar.html' %}

    <!-- Contenuto principale -->
    <div class="container container-main">
        {% block content %}{% endblock %}
    </div>

    <!-- Scripts comuni -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Theme Manager -->
    <script src="/static/js/theme-manager.js"></script>

    <!-- Script per evidenziare menu attivo e forzare dropdown -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Inizializzazione dropdown SNIP...');

            const currentPath = window.location.pathname;
            console.log('📍 Percorso corrente:', currentPath);

            // Inizializza TUTTI i dropdown immediatamente
            function initAllDropdowns() {
                console.log('🔧 Inizializzazione dropdown...');

                const dropdownTriggers = document.querySelectorAll('[data-bs-toggle="dropdown"]');
                console.log(`📊 Trovati ${dropdownTriggers.length} dropdown triggers`);

                if (dropdownTriggers.length > 0) {
                    dropdownTriggers.forEach(function(trigger, index) {
                        try {
                            console.log(`🔄 Inizializzazione dropdown ${index}:`, trigger.id || trigger.className);

                            // Assicurati che Bootstrap sia disponibile
                            if (window.bootstrap && window.bootstrap.Dropdown) {
                                // Rimuovi istanza esistente se presente
                                const existingDropdown = bootstrap.Dropdown.getInstance(trigger);
                                if (existingDropdown) {
                                    existingDropdown.dispose();
                                }

                                // Crea nuova istanza
                                const dropdown = new bootstrap.Dropdown(trigger, {
                                    autoClose: true,
                                    boundary: 'clippingParents'
                                });

                                console.log(`✅ Dropdown ${index} inizializzato con successo`);

                                // Aggiungi event listener per debug
                                trigger.addEventListener('show.bs.dropdown', function() {
                                    console.log('🔽 Dropdown aperto:', trigger.id);
                                });

                                trigger.addEventListener('hide.bs.dropdown', function() {
                                    console.log('🔼 Dropdown chiuso:', trigger.id);
                                });

                            } else {
                                console.warn('⚠️ Bootstrap Dropdown non disponibile');

                                // Fallback manuale
                                trigger.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();

                                    const menu = trigger.nextElementSibling;
                                    if (menu && menu.classList.contains('dropdown-menu')) {
                                        // Chiudi altri dropdown
                                        document.querySelectorAll('.dropdown-menu.show').forEach(m => {
                                            if (m !== menu) m.classList.remove('show');
                                        });

                                        // Toggle questo dropdown
                                        menu.classList.toggle('show');
                                        console.log('🔄 Dropdown toggled manualmente:', menu.classList.contains('show'));
                                    }
                                });
                            }

                        } catch (error) {
                            console.error(`❌ Errore inizializzazione dropdown ${index}:`, error);
                        }
                    });

                    // Listener globale per chiudere dropdown
                    document.addEventListener('click', function(e) {
                        if (!e.target.closest('.dropdown')) {
                            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                                menu.classList.remove('show');
                            });
                        }
                    });

                    console.log('✅ Inizializzazione dropdown completata');
                } else {
                    console.warn('⚠️ Nessun dropdown trovato');
                }
            }

            // Inizializza dropdown con retry
            let initAttempts = 0;
            const maxAttempts = 3;

            function tryInitDropdowns() {
                initAttempts++;
                console.log(`🔄 Tentativo ${initAttempts}/${maxAttempts} inizializzazione dropdown...`);

                if (window.bootstrap) {
                    initAllDropdowns();
                } else if (initAttempts < maxAttempts) {
                    console.log('⏳ Bootstrap non ancora caricato, riprovo...');
                    setTimeout(tryInitDropdowns, 200);
                } else {
                    console.error('❌ Bootstrap non disponibile dopo 3 tentativi');
                    // Prova inizializzazione manuale
                    initAllDropdowns();
                }
            }

            // Avvia inizializzazione
            setTimeout(tryInitDropdowns, 100);

            // Evidenzia menu attivo
            setTimeout(function() {
                if (currentPath.includes('/operativo/')) {
                    const operativoDropdown = document.getElementById('operativoDropdown');
                    if (operativoDropdown) {
                        operativoDropdown.classList.add('active');
                        console.log('✅ Menu operativo evidenziato');
                    }
                }
            }, 200);

            // SOLUZIONE AGGRESSIVA: Forza apertura dropdown su hover e click
            setTimeout(function() {
                console.log('🔧 Applicazione soluzione aggressiva dropdown...');

                const dropdowns = document.querySelectorAll('.dropdown');
                console.log(`📊 Trovati ${dropdowns.length} dropdown per fix aggressivo`);

                dropdowns.forEach(function(dropdown, index) {
                    const trigger = dropdown.querySelector('[data-bs-toggle="dropdown"]');
                    const menu = dropdown.querySelector('.dropdown-menu');

                    if (trigger && menu) {
                        console.log(`🔧 Fix dropdown ${index}:`, trigger.id || trigger.textContent.trim());

                        // Rimuovi tutti gli event listener esistenti
                        const newTrigger = trigger.cloneNode(true);
                        trigger.parentNode.replaceChild(newTrigger, trigger);

                        // Aggiungi event listener personalizzati
                        newTrigger.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            console.log('🖱️ Click su dropdown:', newTrigger.id);

                            // Chiudi tutti gli altri dropdown
                            document.querySelectorAll('.dropdown-menu').forEach(function(otherMenu) {
                                if (otherMenu !== menu) {
                                    otherMenu.classList.remove('show');
                                    otherMenu.style.display = 'none';
                                }
                            });

                            // Toggle questo dropdown
                            const isVisible = menu.classList.contains('show') || menu.style.display === 'block';

                            if (isVisible) {
                                menu.classList.remove('show');
                                menu.style.display = 'none';
                                dropdown.classList.remove('show');
                                console.log('🔼 Dropdown chiuso');
                            } else {
                                menu.classList.add('show');
                                menu.style.display = 'block';
                                menu.style.position = 'absolute';
                                menu.style.top = '100%';
                                menu.style.left = '0';
                                menu.style.zIndex = '9999';
                                menu.style.background = 'white';
                                menu.style.border = '2px solid red';
                                menu.style.minWidth = '200px';
                                dropdown.classList.add('show');
                                console.log('🔽 Dropdown aperto forzatamente');
                            }
                        });

                        // Hover per debug
                        newTrigger.addEventListener('mouseenter', function() {
                            console.log('🖱️ Hover su dropdown:', newTrigger.id);
                        });

                        // Assicura posizionamento corretto
                        dropdown.style.position = 'relative';
                        menu.style.position = 'absolute';

                    } else {
                        console.warn(`⚠️ Dropdown ${index} incompleto:`, {
                            trigger: !!trigger,
                            menu: !!menu
                        });
                    }
                });

                // Listener globale per chiudere dropdown
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.dropdown')) {
                        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                            menu.classList.remove('show');
                            menu.style.display = 'none';
                        });
                        document.querySelectorAll('.dropdown.show').forEach(function(dropdown) {
                            dropdown.classList.remove('show');
                        });
                    }
                });

                console.log('✅ Soluzione aggressiva applicata');

            }, 500);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
