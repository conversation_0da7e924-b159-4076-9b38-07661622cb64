<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cambio Password Obbligatorio - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            background-attachment: fixed;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        /* Onde animate di sfondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23ffffff"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23ffffff"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23ffffff"></path></svg>') repeat-x;
            background-size: 1200px 120px;
            animation: wave 20s linear infinite;
            opacity: 0.1;
            z-index: 0;
        }

        @keyframes wave {
            0% { transform: translateX(0); }
            100% { transform: translateX(-1200px); }
        }

        .password-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 8px 32px rgba(30, 60, 114, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 50px;
            max-width: 550px;
            width: 100%;
            margin: 20px;
            position: relative;
            z-index: 1;
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .maritime-header {
            text-align: center;
            margin-bottom: 35px;
            position: relative;
        }

        .maritime-header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72, #4facfe, #1e3c72);
            border-radius: 2px;
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .maritime-header .anchor-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
            display: inline-block;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .maritime-header h1 {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .maritime-header .subtitle {
            color: #5a6c7d;
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.9;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 50%, #ffe082 100%);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow:
                0 8px 25px rgba(255, 193, 7, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .alert-warning::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .alert-warning .alert-icon {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff8f00, #ffc107);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-right: 15px;
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .form-floating {
            margin-bottom: 25px;
            position: relative;
        }

        .form-floating .form-control {
            border-radius: 20px;
            border: 2px solid rgba(30, 60, 114, 0.2);
            padding: 1.2rem 1rem;
            height: calc(3.8rem + 2px);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 1.1rem;
            box-shadow:
                0 4px 15px rgba(30, 60, 114, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .form-floating .form-control:focus {
            border-color: #4facfe;
            background: rgba(255, 255, 255, 1);
            box-shadow:
                0 0 0 0.3rem rgba(79, 172, 254, 0.25),
                0 8px 25px rgba(30, 60, 114, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .form-floating .form-control:hover:not(:focus) {
            border-color: rgba(30, 60, 114, 0.4);
            transform: translateY(-1px);
            box-shadow:
                0 6px 20px rgba(30, 60, 114, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .form-floating label {
            color: #5a6c7d;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating .form-control:focus ~ label,
        .form-floating .form-control:not(:placeholder-shown) ~ label {
            color: #1e3c72;
            transform: scale(0.9) translateY(-0.5rem);
        }

        .password-requirements {
            background: linear-gradient(135deg, rgba(30, 60, 114, 0.08), rgba(79, 172, 254, 0.12));
            border: 2px solid rgba(30, 60, 114, 0.15);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
            box-shadow:
                0 8px 25px rgba(30, 60, 114, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            position: relative;
            overflow: hidden;
        }

        .password-requirements::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #1e3c72, #4facfe, #1e3c72);
            border-radius: 20px 20px 0 0;
        }

        .password-requirements h6 {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 1rem;
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.3);
        }

        .requirement:hover {
            background: rgba(255, 255, 255, 0.5);
            transform: translateX(5px);
        }

        .requirement .check-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #dc3545;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .requirement.valid {
            background: rgba(40, 167, 69, 0.1);
            border-left: 3px solid #28a745;
        }

        .requirement.valid .check-icon {
            color: #28a745;
            animation: checkmark 0.5s ease-in-out;
        }

        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .btn-change-password {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            border: none;
            color: white;
            padding: 18px 40px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.2rem;
            width: 100%;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 25px rgba(30, 60, 114, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-change-password::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn-change-password:hover::before {
            left: 100%;
        }

        .btn-change-password:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 15px 40px rgba(30, 60, 114, 0.4),
                0 5px 15px rgba(79, 172, 254, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-change-password:active {
            transform: translateY(-1px) scale(1.01);
            transition: all 0.1s ease;
        }

        .btn-change-password:disabled {
            background: linear-gradient(135deg, #6c757d, #495057);
            transform: none;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.2);
            cursor: not-allowed;
            opacity: 0.7;
        }

        .btn-change-password:disabled::before {
            display: none;
        }

        .password-toggle {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(30, 60, 114, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: #5a6c7d;
            cursor: pointer;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 1.1rem;
        }

        .password-toggle:hover {
            color: #1e3c72;
            background: rgba(255, 255, 255, 1);
            border-color: #4facfe;
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.2);
        }

        .password-toggle:active {
            transform: translateY(-50%) scale(0.95);
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .btn-text {
            display: none;
        }

        /* Effetti aggiuntivi per migliorare l'aspetto */
        .form-floating .form-control::placeholder {
            color: transparent;
        }

        .form-floating .form-control:focus::placeholder {
            color: rgba(108, 117, 125, 0.5);
        }

        /* Animazione per i requisiti password */
        .password-requirements {
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Effetto particelle di sfondo */
        .password-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(30, 60, 114, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            border-radius: 25px;
            pointer-events: none;
            z-index: -1;
        }

        /* Miglioramento responsive */
        @media (max-width: 576px) {
            .password-container {
                padding: 30px 25px;
                margin: 10px;
                border-radius: 20px;
            }

            .maritime-header h1 {
                font-size: 1.8rem;
            }

            .maritime-header .anchor-icon {
                font-size: 3rem;
            }

            .btn-change-password {
                padding: 16px 30px;
                font-size: 1.1rem;
            }

            .form-floating .form-control {
                height: calc(3.5rem + 2px);
                padding: 1rem 0.75rem;
            }

            .password-toggle {
                width: 35px;
                height: 35px;
                right: 15px;
            }
        }

        /* Effetto focus migliorato per accessibilità */
        .form-floating .form-control:focus,
        .btn-change-password:focus,
        .password-toggle:focus {
            outline: 3px solid rgba(79, 172, 254, 0.3);
            outline-offset: 2px;
        }

        /* Animazione loading migliorata */
        .loading {
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: loadingShine 1.5s ease-in-out infinite;
            border-radius: 25px;
        }

        @keyframes loadingShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body>
    <div class="password-container">
        <!-- Header -->
        <div class="maritime-header">
            <div class="anchor-icon">⚓</div>
            <h1>Password Scaduta</h1>
            <div class="subtitle">Sistema SNIP • Cambio Password Obbligatorio</div>
        </div>

        <!-- Alert -->
        <div class="alert alert-warning">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle alert-icon"></i>
                <div>
                    <strong>La tua password è scaduta!</strong><br>
                    Per continuare ad utilizzare il sistema SNIP, devi cambiare la tua password.
                </div>
            </div>
        </div>

        <!-- Form -->
        <form id="changePasswordForm">
            <!-- Password Attuale -->
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="currentPassword" placeholder="Password attuale" required>
                <label for="currentPassword">
                    <i class="fas fa-lock me-2"></i>Password Attuale
                </label>
                <button type="button" class="password-toggle" onclick="togglePassword('currentPassword')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <!-- Nuova Password -->
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="newPassword" placeholder="Nuova password" required>
                <label for="newPassword">
                    <i class="fas fa-key me-2"></i>Nuova Password
                </label>
                <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <!-- Conferma Password -->
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="confirmPassword" placeholder="Conferma password" required>
                <label for="confirmPassword">
                    <i class="fas fa-check-double me-2"></i>Conferma Nuova Password
                </label>
                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <!-- Requisiti Password -->
            <div class="password-requirements">
                <h6><i class="fas fa-shield-alt me-2"></i>Requisiti Password</h6>
                <div class="requirement" id="req-length">
                    <i class="fas fa-times check-icon"></i>
                    <span>Minimo {{ security_config.password_min_length }} caratteri</span>
                </div>
                {% if security_config.password_uppercase %}
                <div class="requirement" id="req-uppercase">
                    <i class="fas fa-times check-icon"></i>
                    <span>Almeno una lettera maiuscola</span>
                </div>
                {% endif %}
                {% if security_config.password_numbers %}
                <div class="requirement" id="req-numbers">
                    <i class="fas fa-times check-icon"></i>
                    <span>Almeno un numero</span>
                </div>
                {% endif %}
                {% if security_config.password_special %}
                <div class="requirement" id="req-special">
                    <i class="fas fa-times check-icon"></i>
                    <span>Almeno un carattere speciale (!@#$%^&*)</span>
                </div>
                {% endif %}
                <div class="requirement" id="req-match">
                    <i class="fas fa-times check-icon"></i>
                    <span>Le password devono coincidere</span>
                </div>
                <div class="requirement" id="req-different">
                    <i class="fas fa-times check-icon"></i>
                    <span>La nuova password deve essere diversa da quella attuale</span>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-change-password" id="submitBtn" disabled>
                <span class="btn-text">
                    <i class="fas fa-key me-2"></i>Cambia Password
                </span>
                <span class="loading-spinner">
                    <i class="fas fa-spinner fa-spin me-2"></i>Aggiornamento...
                </span>
            </button>
        </form>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Configurazioni di sicurezza dal server
        const securityConfig = {
            password_min_length: {{ security_config.password_min_length }},
            password_uppercase: {{ security_config.password_uppercase|lower }},
            password_numbers: {{ security_config.password_numbers|lower }},
            password_special: {{ security_config.password_special|lower }}
        };

        // Elementi del form
        const currentPasswordInput = document.getElementById('currentPassword');
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const submitBtn = document.getElementById('submitBtn');
        const form = document.getElementById('changePasswordForm');

        // Funzione per mostrare/nascondere password
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Validazione password in tempo reale
        function validatePassword() {
            const currentPassword = currentPasswordInput.value;
            const password = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            let isValid = true;

            // Lunghezza minima
            const lengthValid = password.length >= securityConfig.password_min_length;
            updateRequirement('req-length', lengthValid);
            if (!lengthValid) isValid = false;

            // Maiuscole
            if (securityConfig.password_uppercase) {
                const uppercaseValid = /[A-Z]/.test(password);
                updateRequirement('req-uppercase', uppercaseValid);
                if (!uppercaseValid) isValid = false;
            }

            // Numeri
            if (securityConfig.password_numbers) {
                const numbersValid = /[0-9]/.test(password);
                updateRequirement('req-numbers', numbersValid);
                if (!numbersValid) isValid = false;
            }

            // Caratteri speciali
            if (securityConfig.password_special) {
                const specialValid = /[!@#$%^&*(),.?":{}|<>]/.test(password);
                updateRequirement('req-special', specialValid);
                if (!specialValid) isValid = false;
            }

            // Corrispondenza password
            const matchValid = password === confirmPassword && password.length > 0;
            updateRequirement('req-match', matchValid);
            if (!matchValid) isValid = false;

            // Nuova password diversa da quella attuale
            const differentValid = password !== currentPassword && password.length > 0 && currentPassword.length > 0;
            updateRequirement('req-different', differentValid);
            if (!differentValid && password.length > 0 && currentPassword.length > 0) isValid = false;

            // Abilita/disabilita pulsante
            submitBtn.disabled = !isValid || currentPasswordInput.value.length === 0;
        }

        function updateRequirement(reqId, isValid) {
            const req = document.getElementById(reqId);
            if (!req) return;
            
            const icon = req.querySelector('.check-icon');
            
            if (isValid) {
                req.classList.add('valid');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-check');
            } else {
                req.classList.remove('valid');
                icon.classList.remove('fa-check');
                icon.classList.add('fa-times');
            }
        }

        // Event listeners
        newPasswordInput.addEventListener('input', validatePassword);
        confirmPasswordInput.addEventListener('input', validatePassword);
        currentPasswordInput.addEventListener('input', validatePassword);

        // Submit form
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const currentPassword = currentPasswordInput.value;
            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                Swal.fire({
                    icon: 'error',
                    title: 'Errore',
                    text: 'Tutti i campi sono obbligatori'
                });
                return;
            }

            // Verifica che le password coincidano
            if (newPassword !== confirmPassword) {
                Swal.fire({
                    icon: 'error',
                    title: 'Errore',
                    text: 'Le password non coincidono'
                });
                return;
            }

            // Verifica che la nuova password sia diversa da quella attuale
            if (newPassword === currentPassword) {
                Swal.fire({
                    icon: 'error',
                    title: 'Errore',
                    text: 'La nuova password deve essere diversa da quella attuale'
                });
                return;
            }

            // Mostra loading
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            try {
                const response = await fetch('/api/change-password-required', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        current_password: currentPassword,
                        new_password: newPassword
                    })
                });

                const data = await response.json();

                if (data.success) {
                    await Swal.fire({
                        icon: 'success',
                        title: 'Password Cambiata!',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    // Reindirizza alla dashboard
                    window.location.href = data.redirect_url || '/dashboard';
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Errore',
                        text: data.message || 'Errore durante il cambio password'
                    });
                }
            } catch (error) {
                console.error('Errore:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Errore di Connessione',
                    text: 'Impossibile contattare il server. Riprova più tardi.'
                });
            } finally {
                // Nascondi loading
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
                validatePassword(); // Ricontrolla validazione
            }
        });

        // Validazione iniziale
        validatePassword();

        // Effetti aggiuntivi per migliorare UX
        document.addEventListener('DOMContentLoaded', function() {
            // Assicurati che tutti i campi password siano vuoti all'inizio
            currentPasswordInput.value = '';
            newPasswordInput.value = '';
            confirmPasswordInput.value = '';

            // Disabilita l'autocompletamento per i campi password
            currentPasswordInput.setAttribute('autocomplete', 'new-password');
            newPasswordInput.setAttribute('autocomplete', 'new-password');
            confirmPasswordInput.setAttribute('autocomplete', 'new-password');

            // Previeni il paste di password nel campo password attuale per sicurezza
            currentPasswordInput.addEventListener('paste', function(e) {
                // Permetti il paste ma mostra un avviso se necessario
                setTimeout(() => {
                    validatePassword();
                }, 10);
            });

            // Focus automatico sul primo campo
            setTimeout(() => {
                currentPasswordInput.focus();
            }, 500);
            // Animazione di entrata per i campi form
            const formFields = document.querySelectorAll('.form-floating');
            formFields.forEach((field, index) => {
                field.style.opacity = '0';
                field.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    field.style.transition = 'all 0.5s ease';
                    field.style.opacity = '1';
                    field.style.transform = 'translateY(0)';
                }, 200 + (index * 100));
            });

            // Effetto focus migliorato
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // Feedback visivo per i requisiti password
            const requirements = document.querySelectorAll('.requirement');
            requirements.forEach(req => {
                req.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('valid')) {
                        this.style.background = 'rgba(220, 53, 69, 0.1)';
                    }
                });

                req.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('valid')) {
                        this.style.background = 'rgba(255, 255, 255, 0.3)';
                    }
                });
            });
        });
    </script>
</body>
</html>
