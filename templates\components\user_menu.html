<!-- <PERSON><PERSON> Utente Originale -->
<li class="nav-item dropdown user-dropdown">
    <a class="nav-link dropdown-toggle user-name" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-user-circle me-1"></i>
        {{ current_user.Cognome }}
        <span class="badge bg-primary ms-2">{{ current_user.ruolo.value if current_user.ruolo.value else current_user.ruolo }}</span>
    </a>
    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
        <!-- Informazioni Utente -->
        <li class="dropdown-header">
            <div class="d-flex align-items-center">
                <div class="avatar-circle me-2">
                    {{ current_user.Cognome[0] if current_user.Cognome else 'U' }}
                </div>
                <div>
                    <div class="fw-bold">{{ current_user.Cognome }}</div>
                    <small class="text-muted">{{ current_user.ruolo.value if current_user.ruolo.value else current_user.ruolo }}</small>
                </div>
            </div>
        </li>
        <li><hr class="dropdown-divider"></li>

        <!-- Dashboard -->
        <li>
            {% set reparto_url = current_user.reparto.value.lower() if current_user.reparto.value else current_user.reparto.lower() %}
            {% if reparto_url == 'contabilita\'' %}
                {% set reparto_url = 'contabilita' %}
            {% endif %}
            <a class="dropdown-item" href="/dashboard/{{ reparto_url }}">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </a>
        </li>

        <!-- Menu specifici per ruolo -->
        {% if current_user.ruolo.value in ['ADMIN', 'SUPER_ADMIN'] or current_user.ruolo in ['ADMIN', 'SUPER_ADMIN'] %}
        <li>
            <a class="dropdown-item" href="/dashboard/amministrazione">
                <i class="fas fa-cogs me-2"></i>Amministrazione
            </a>
        </li>
        {% endif %}

        <!-- Profilo -->
        <li>
            <a class="dropdown-item" href="/profile">
                <i class="fas fa-user me-2"></i>Profilo
            </a>
        </li>
        <li><hr class="dropdown-divider"></li>

        <!-- Logout -->
        <li>
            <form method="post" action="/logout" style="margin: 0;">
                <button type="submit" class="dropdown-item text-danger">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </button>
            </form>
        </li>
    </ul>
</li>

<style>
/* ===== MENU UTENTE MODERNO E BELLO ===== */

/* Pulsante utente nella navbar - Base */
#userDropdown {
    padding: 8px 16px !important;
    border-radius: 25px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    text-decoration: none !important;
}

/* Tema Marittimo e Scuro */
body.theme-maritime #userDropdown,
body:not([class*="theme-"]) #userDropdown,
body.theme-dark #userDropdown {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

body.theme-maritime #userDropdown:hover,
body:not([class*="theme-"]) #userDropdown:hover,
body.theme-dark #userDropdown:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

body.theme-maritime #userDropdown:focus,
body:not([class*="theme-"]) #userDropdown:focus,
body.theme-dark #userDropdown:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
    color: white !important;
}

/* Tema Chiaro */
body.theme-light #userDropdown {
    background: rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    color: #343a40 !important;
    font-weight: 500 !important;
}

body.theme-light #userDropdown:hover {
    background: rgba(0, 0, 0, 0.12) !important;
    border-color: rgba(0, 0, 0, 0.25) !important;
    color: #212529 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

body.theme-light #userDropdown:focus {
    box-shadow: 0 0 0 0.2rem rgba(73, 80, 87, 0.25) !important;
    color: #212529 !important;
}

/* Badge nel pulsante utente */
body.theme-maritime #userDropdown .badge,
body:not([class*="theme-"]) #userDropdown .badge,
body.theme-dark #userDropdown .badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    font-size: 0.7rem !important;
    padding: 3px 8px !important;
    border-radius: 12px !important;
    margin-left: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

body.theme-light #userDropdown .badge {
    background: #495057 !important;
    color: white !important;
    font-size: 0.7rem !important;
    padding: 3px 8px !important;
    border-radius: 12px !important;
    margin-left: 8px !important;
    border: 1px solid #495057 !important;
}

/* Dropdown menu moderno SENZA BARRE DI SCORRIMENTO */
#userDropdown + .dropdown-menu {
    backdrop-filter: blur(20px) !important;
    border-radius: 20px !important;
    margin-top: 12px !important;
    padding: 16px 8px !important;
    min-width: 280px !important;
    max-width: 320px !important;
    animation: userDropdownSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    transform-origin: top right !important;

    /* ELIMINA COMPLETAMENTE LE BARRE DI SCORRIMENTO */
    overflow: visible !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;
    max-height: none !important;
    height: auto !important;

    /* POSIZIONAMENTO INTELLIGENTE */
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    z-index: 9999 !important;

    /* LASCIA CHE BOOTSTRAP GESTISCA LA VISIBILITÀ */
    will-change: transform, opacity !important;
}

/* Tema Marittimo e Scuro */
body.theme-maritime #userDropdown + .dropdown-menu,
body:not([class*="theme-"]) #userDropdown + .dropdown-menu,
body.theme-dark #userDropdown + .dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Tema Chiaro */
body.theme-light #userDropdown + .dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1) !important;
}

@keyframes userDropdownSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== CONTROLLO VISIBILITÀ SOLO MENU UTENTE ===== */

/* SPECIFICO: Solo menu utente visibile con .show */
#userDropdown + .dropdown-menu.show {
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Header utente elegante */
#userDropdown + .dropdown-menu .dropdown-header {
    border-radius: 16px !important;
    padding: 16px !important;
    margin: 0 8px 16px 8px !important;
    border: none !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Tema Marittimo e Scuro */
body.theme-maritime #userDropdown + .dropdown-menu .dropdown-header,
body:not([class*="theme-"]) #userDropdown + .dropdown-menu .dropdown-header,
body.theme-dark #userDropdown + .dropdown-menu .dropdown-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* Tema Chiaro */
body.theme-light #userDropdown + .dropdown-menu .dropdown-header {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%) !important;
    color: white !important;
}

#userDropdown + .dropdown-menu .dropdown-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

#userDropdown + .dropdown-menu .dropdown-header:hover::before {
    left: 100%;
}

/* Avatar moderno */
.avatar-circle {
    width: 48px !important;
    height: 48px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-weight: 700 !important;
    font-size: 18px !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Info utente nel header */
.dropdown-header .d-flex {
    align-items: center !important;
}

.dropdown-header .fw-bold {
    font-size: 1.1rem !important;
    margin-bottom: 2px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-header .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.85rem !important;
    font-weight: 500 !important;
}

/* Divider elegante */
#userDropdown + .dropdown-menu .dropdown-divider {
    height: 1px !important;
    margin: 12px 16px !important;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent) !important;
    border: none !important;
}

/* Menu items moderni */
#userDropdown + .dropdown-menu .dropdown-item {
    padding: 12px 20px !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    border-radius: 12px !important;
    margin: 4px 8px !important;
    position: relative !important;
    overflow: hidden !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    border: 1px solid transparent !important;
    text-decoration: none !important;
}

/* Tema Marittimo e Scuro */
body.theme-maritime #userDropdown + .dropdown-menu .dropdown-item,
body:not([class*="theme-"]) #userDropdown + .dropdown-menu .dropdown-item,
body.theme-dark #userDropdown + .dropdown-menu .dropdown-item {
    color: #2c3e50 !important;
}

/* Tema Chiaro */
body.theme-light #userDropdown + .dropdown-menu .dropdown-item {
    color: #343a40 !important;
}

#userDropdown + .dropdown-menu .dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

#userDropdown + .dropdown-menu .dropdown-item:hover::before {
    left: 100%;
}

/* Hover effects per tema */
body.theme-maritime #userDropdown + .dropdown-menu .dropdown-item:hover,
body:not([class*="theme-"]) #userDropdown + .dropdown-menu .dropdown-item:hover,
body.theme-dark #userDropdown + .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateX(8px) translateY(-2px) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

body.theme-light #userDropdown + .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%) !important;
    color: white !important;
    transform: translateX(8px) translateY(-2px) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 8px 25px rgba(73, 80, 87, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

#userDropdown + .dropdown-menu .dropdown-item i {
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0 !important;
}

/* Icone per tema Marittimo e Scuro */
body.theme-maritime #userDropdown + .dropdown-menu .dropdown-item i,
body:not([class*="theme-"]) #userDropdown + .dropdown-menu .dropdown-item i,
body.theme-dark #userDropdown + .dropdown-menu .dropdown-item i {
    background: rgba(102, 126, 234, 0.15) !important;
    color: #5a67d8 !important;
}

/* Icone per tema Chiaro */
body.theme-light #userDropdown + .dropdown-menu .dropdown-item i {
    background: rgba(73, 80, 87, 0.15) !important;
    color: #495057 !important;
}

/* Hover icone per tutti i temi */
#userDropdown + .dropdown-menu .dropdown-item:hover i {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) rotate(5deg) !important;
    color: white !important;
}

/* Logout button speciale - tutti i temi */
#userDropdown + .dropdown-menu .dropdown-item.text-danger {
    color: #e74c3c !important;
}

#userDropdown + .dropdown-menu .dropdown-item.text-danger:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    color: white !important;
    box-shadow:
        0 8px 25px rgba(231, 76, 60, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

#userDropdown + .dropdown-menu .dropdown-item.text-danger i {
    background: rgba(231, 76, 60, 0.1) !important;
    color: #e74c3c !important;
}

#userDropdown + .dropdown-menu .dropdown-item.text-danger:hover i {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* ===== POSIZIONAMENTO INTELLIGENTE SENZA OVERFLOW ===== */

/* Forza il dropdown a rimanere sempre visibile */
.navbar .nav-item.dropdown {
    position: relative !important;
}

/* SOLO per il dropdown utente */
.navbar #userDropdown + .dropdown-menu {
    /* ELIMINA OGNI POSSIBILE BARRA DI SCORRIMENTO */
    overflow: visible !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;
    max-height: none !important;
    height: auto !important;

    /* POSIZIONAMENTO OTTIMALE */
    position: absolute !important;
    z-index: 99999 !important;

    /* PREVIENI OVERFLOW ORIZZONTALE */
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    white-space: nowrap !important;
}

/* Mostra SOLO il dropdown utente quando ha la classe .show */
.navbar #userDropdown + .dropdown-menu.show {
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Rimuoviamo questo CSS che interferisce */

/* Posizionamento dinamico per evitare overflow */
@media (max-width: 1200px) {
    #userDropdown + .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        transform: translateX(0) !important;
    }
}

@media (max-width: 991px) {
    #userDropdown + .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        min-width: 260px !important;
        max-width: 280px !important;
    }
}

@media (max-width: 768px) {
    #userDropdown + .dropdown-menu {
        min-width: 250px !important;
        max-width: 270px !important;
        margin-top: 8px !important;
        border-radius: 16px !important;
        right: 0 !important;
        left: auto !important;

        /* ASSICURA NESSUNA BARRA SU MOBILE */
        overflow: visible !important;
        max-height: none !important;
    }

    #userDropdown + .dropdown-menu .dropdown-item {
        padding: 10px 16px !important;
        font-size: 14px !important;
    }

    #userDropdown + .dropdown-menu .dropdown-item i {
        width: 20px !important;
        height: 20px !important;
        font-size: 14px !important;
    }
}

@media (max-width: 576px) {
    #userDropdown + .dropdown-menu {
        min-width: 220px !important;
        max-width: 250px !important;
        right: 0 !important;
        left: auto !important;

        /* MOBILE: NESSUNA BARRA GARANTITA */
        overflow: visible !important;
        overflow-y: visible !important;
        max-height: none !important;
        height: auto !important;
    }
}

/* ===== FORZA VISIBILITÀ COMPLETA ===== */

/* Assicura che il contenuto sia sempre visibile */
#userDropdown + .dropdown-menu * {
    box-sizing: border-box !important;
}

/* Previeni qualsiasi scroll interno */
#userDropdown + .dropdown-menu .dropdown-header,
#userDropdown + .dropdown-menu .dropdown-item {
    overflow: visible !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
}

/* Forza SOLO il dropdown utente a essere sempre sopra tutto */
.navbar .nav-item.dropdown#userDropdown {
    z-index: 1000 !important;
}

.navbar #userDropdown + .dropdown-menu {
    z-index: 99999 !important;
    position: absolute !important;
}

/* Debug rimosso */

/* ===== ALTEZZA AUTOMATICA SENZA LIMITI ===== */
#userDropdown + .dropdown-menu {
    /* DIMENSIONI DINAMICHE */
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;

    /* NESSUN OVERFLOW */
    overflow: visible !important;
    overflow-x: hidden !important;
    overflow-y: visible !important;

    /* LASCIA CHE BOOTSTRAP GESTISCA display */
    gap: 0 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 Inizializzazione dropdown utente senza barre...');

    const userDropdown = document.getElementById('userDropdown');
    const userDropdownMenu = userDropdown ? userDropdown.nextElementSibling : null;

    if (userDropdown && userDropdownMenu) {
        console.log('✅ Dropdown utente trovato');
        console.log('🔍 Debug dropdown:', {
            button: userDropdown,
            menu: userDropdownMenu,
            hasShowClass: userDropdownMenu.classList.contains('show'),
            computedDisplay: window.getComputedStyle(userDropdownMenu).display
        });

        // Funzione per posizionare il dropdown senza overflow
        function positionUserDropdown() {
            const rect = userDropdown.getBoundingClientRect();
            const menuWidth = userDropdownMenu.offsetWidth || 280;
            const menuHeight = userDropdownMenu.offsetHeight || 300;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            console.log('📊 Posizionamento dropdown:', {
                buttonRect: rect,
                menuWidth: menuWidth,
                menuHeight: menuHeight,
                viewport: { width: viewportWidth, height: viewportHeight }
            });

            // FORZA NESSUNA BARRA DI SCORRIMENTO
            userDropdownMenu.style.overflow = 'visible';
            userDropdownMenu.style.overflowX = 'hidden';
            userDropdownMenu.style.overflowY = 'visible';
            userDropdownMenu.style.maxHeight = 'none';
            userDropdownMenu.style.height = 'auto';

            // Posizionamento orizzontale intelligente
            const rightSpace = viewportWidth - rect.right;
            const leftSpace = rect.left;

            if (rightSpace >= menuWidth) {
                // Spazio sufficiente a destra
                userDropdownMenu.style.right = '0';
                userDropdownMenu.style.left = 'auto';
                userDropdownMenu.style.transform = 'translateX(0)';
                console.log('➡️ Dropdown posizionato a destra');
            } else if (leftSpace >= menuWidth) {
                // Spazio sufficiente a sinistra
                userDropdownMenu.style.right = 'auto';
                userDropdownMenu.style.left = '0';
                userDropdownMenu.style.transform = 'translateX(0)';
                console.log('⬅️ Dropdown posizionato a sinistra');
            } else {
                // Centra il dropdown se non c'è spazio
                const offset = (menuWidth - rect.width) / 2;
                userDropdownMenu.style.right = `${offset}px`;
                userDropdownMenu.style.left = 'auto';
                userDropdownMenu.style.transform = 'translateX(0)';
                console.log('🎯 Dropdown centrato');
            }

            // Posizionamento verticale intelligente
            const bottomSpace = viewportHeight - rect.bottom;
            const topSpace = rect.top;

            if (bottomSpace >= menuHeight) {
                // Spazio sufficiente sotto
                userDropdownMenu.style.top = '100%';
                userDropdownMenu.style.bottom = 'auto';
                userDropdownMenu.style.marginTop = '12px';
                userDropdownMenu.style.marginBottom = '0';
                console.log('⬇️ Dropdown mostrato sotto');
            } else if (topSpace >= menuHeight) {
                // Spazio sufficiente sopra
                userDropdownMenu.style.top = 'auto';
                userDropdownMenu.style.bottom = '100%';
                userDropdownMenu.style.marginTop = '0';
                userDropdownMenu.style.marginBottom = '12px';
                console.log('⬆️ Dropdown mostrato sopra');
            } else {
                // Forza sotto ma senza barre
                userDropdownMenu.style.top = '100%';
                userDropdownMenu.style.bottom = 'auto';
                userDropdownMenu.style.marginTop = '12px';
                userDropdownMenu.style.marginBottom = '0';
                userDropdownMenu.style.maxHeight = 'none';
                userDropdownMenu.style.overflow = 'visible';
                console.log('⬇️ Dropdown forzato sotto senza barre');
            }

            // GESTISCI VISIBILITÀ BASATA SU CLASSE .show
            userDropdownMenu.style.zIndex = '99999';
            userDropdownMenu.style.position = 'absolute';

            // NON forzare display - lascia che Bootstrap gestisca .show
            if (userDropdownMenu.classList.contains('show')) {
                userDropdownMenu.style.flexDirection = 'column';
            }
        }

        // Event listener per apertura dropdown
        userDropdown.addEventListener('click', function(e) {
            console.log('🖱️ Click su dropdown utente');
            console.log('🔍 Stato prima del click:', {
                hasShow: userDropdownMenu.classList.contains('show'),
                display: window.getComputedStyle(userDropdownMenu).display,
                visibility: window.getComputedStyle(userDropdownMenu).visibility
            });

            setTimeout(function() {
                console.log('🔍 Stato dopo il click:', {
                    hasShow: userDropdownMenu.classList.contains('show'),
                    display: window.getComputedStyle(userDropdownMenu).display,
                    visibility: window.getComputedStyle(userDropdownMenu).visibility
                });

                if (userDropdownMenu.classList.contains('show')) {
                    positionUserDropdown();
                    console.log('📍 Dropdown posizionato dopo apertura');
                } else {
                    console.warn('⚠️ Dropdown non ha classe .show dopo click');
                }
            }, 100);
        });

        // Event listener per ridimensionamento finestra
        window.addEventListener('resize', function() {
            if (userDropdownMenu.classList.contains('show')) {
                positionUserDropdown();
                console.log('📐 Dropdown riposizionato dopo resize');
            }
        });

        // Event listener per scroll (mantieni posizione)
        window.addEventListener('scroll', function() {
            if (userDropdownMenu.classList.contains('show')) {
                positionUserDropdown();
            }
        });

        // Forza posizionamento iniziale
        setTimeout(function() {
            positionUserDropdown();
            console.log('🎯 Posizionamento iniziale completato');
        }, 100);

        console.log('✅ Dropdown utente configurato senza barre di scorrimento');

    } else {
        console.warn('⚠️ Dropdown utente non trovato');
    }
});
</script>
