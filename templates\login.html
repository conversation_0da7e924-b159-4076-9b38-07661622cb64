<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚢 SNIP - Sistema Navale Integrato Portuale</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="/static/css/global-themes.css" rel="stylesheet">
    <link href="/static/css/light-theme-contrast-fixes.css" rel="stylesheet">
    <link href="/static/css/maritime-theme-contrast-fixes.css" rel="stylesheet">
    <link href="/static/css/dark-theme-contrast-fixes.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --shadow-soft: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-hover: 0 15px 35px rgba(31, 38, 135, 0.5);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Poppins', sans-serif;
            background:
                linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.95) 100%),
                url('/static/images/cargo-ship.jpg') center/cover no-repeat fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Overlay professionale */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            z-index: 1;
        }

        body::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.03" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat bottom;
            background-size: cover;
            z-index: 2;
        }

        /* Animazioni ridotte per professionalità */

        /* Particelle discrete per professionalità */
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 3;
            pointer-events: none;
            opacity: 0.3;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            animation: particleFloat 30s infinite linear;
            backdrop-filter: blur(1px);
        }

        .particle:nth-child(1) {
            width: 8px; height: 8px; left: 10%;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.2) 100%);
            animation-delay: 0s;
        }
        .particle:nth-child(2) {
            width: 12px; height: 12px; left: 20%;
            background: radial-gradient(circle, rgba(102,126,234,0.6) 0%, rgba(102,126,234,0.1) 100%);
            animation-delay: 3s;
        }
        .particle:nth-child(3) {
            width: 6px; height: 6px; left: 30%;
            background: radial-gradient(circle, rgba(118,75,162,0.7) 0%, rgba(118,75,162,0.1) 100%);
            animation-delay: 6s;
        }
        .particle:nth-child(4) {
            width: 10px; height: 10px; left: 40%;
            background: radial-gradient(circle, rgba(79,172,254,0.5) 0%, rgba(79,172,254,0.1) 100%);
            animation-delay: 9s;
        }
        .particle:nth-child(5) {
            width: 14px; height: 14px; left: 50%;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.1) 100%);
            animation-delay: 12s;
        }
        .particle:nth-child(6) {
            width: 7px; height: 7px; left: 60%;
            background: radial-gradient(circle, rgba(240,147,251,0.8) 0%, rgba(240,147,251,0.2) 100%);
            animation-delay: 15s;
        }
        .particle:nth-child(7) {
            width: 11px; height: 11px; left: 70%;
            background: radial-gradient(circle, rgba(102,126,234,0.4) 0%, rgba(102,126,234,0.1) 100%);
            animation-delay: 18s;
        }
        .particle:nth-child(8) {
            width: 9px; height: 9px; left: 80%;
            background: radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0.1) 100%);
            animation-delay: 21s;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: translateY(90vh) rotate(45deg) scale(1);
            }
            90% {
                opacity: 1;
                transform: translateY(10vh) rotate(315deg) scale(1);
            }
            100% {
                transform: translateY(-10vh) rotate(360deg) scale(0);
                opacity: 0;
            }
        }

        /* Container principale con layout a due colonne */
        .login-main-container {
            position: relative;
            z-index: 10;
            max-width: 1200px;
            width: 95%;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.3),
                0 8px 25px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: var(--transition);
            min-height: 600px;
            overflow: hidden;
        }

        .login-main-container:hover {
            transform: translateY(-3px);
            box-shadow:
                0 25px 70px rgba(0, 0, 0, 0.25),
                0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .login-content-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        /* Sezione form di login (sinistra) */
        .login-form-section {
            padding: 45px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        /* Sezione logo (destra) */
        .login-logo-section {
            background: linear-gradient(135deg,
                #1e40af 0%,
                #3b82f6 30%,
                #60a5fa 60%,
                #93c5fd 100%);
            border-radius: 0 20px 20px 0;
            padding: 45px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .login-logo-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(147, 197, 253, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 60% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="waves" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><path d="M0 50 Q25 25 50 50 T100 50 V100 H0 Z" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23waves)"/></svg>') center/cover;
            opacity: 0.8;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                transform: translateX(0) translateY(0) scale(1);
            }
            25% {
                transform: translateX(10px) translateY(-5px) scale(1.02);
            }
            50% {
                transform: translateX(-5px) translateY(10px) scale(0.98);
            }
            75% {
                transform: translateX(-10px) translateY(-10px) scale(1.01);
            }
        }

        .login-logo-section .logo-container {
            position: relative;
            z-index: 2;
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(59, 130, 246, 0.2) 100%);
            backdrop-filter: blur(15px);
            border-radius: 30px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .login-logo-section .logo-image {
            width: 160px;
            height: 160px;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        }

        .login-logo-section .logo-icon {
            font-size: 6rem;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .logo-text {
            position: relative;
            z-index: 2;
        }

        .logo-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .logo-description {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
            line-height: 1.6;
        }



        @keyframes slideUp {
            0% {
                opacity: 0;
                transform: translateY(60px) scale(0.95);
                filter: blur(10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        /* Header con logo aziendale */
        .header-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-container {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 250px;
            height: 250px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 35px;
            margin-bottom: 30px;
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.8);
            animation: logoFloat 3s ease-in-out infinite;
            transition: var(--transition);
        }

        .logo-container:hover {
            transform: scale(1.03);
            box-shadow:
                0 15px 50px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
        }

        .logo-image {
            width: 200px;
            height: 200px;
            object-fit: contain;
            filter: drop-shadow(0 4px 12px rgba(0,0,0,0.2));
        }

        .logo-icon {
            font-size: 7.5rem;
            color: #334155;
            filter: drop-shadow(0 2px 8px rgba(0,0,0,0.15));
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        .app-title {
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg,
                #1e40af 0%,
                #3b82f6 25%,
                #60a5fa 50%,
                #93c5fd 75%,
                #dbeafe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            letter-spacing: 2px;
            text-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
            font-family: 'Arial Black', 'Helvetica Neue', Arial, sans-serif;
            position: relative;
            display: inline-block;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        .app-title::before {
            content: 'SNIP';
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.1) 0%,
                rgba(147, 197, 253, 0.2) 50%,
                rgba(219, 234, 254, 0.1) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: blur(2px);
            z-index: -1;
            animation: titleBlur 4s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0% {
                filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
                transform: scale(1);
            }
            100% {
                filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
                transform: scale(1.02);
            }
        }

        @keyframes titleBlur {
            0%, 100% {
                opacity: 0.3;
                transform: translateY(0px);
            }
            50% {
                opacity: 0.6;
                transform: translateY(-2px);
            }
        }

        .app-title:hover {
            animation-duration: 0.5s;
            transform: scale(1.05) !important;
            filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8)) !important;
            cursor: default;
        }

        .app-title:hover::before {
            opacity: 0.8;
            filter: blur(3px);
        }

        /* Animazione lettere individuali */
        .letter {
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .letter:hover {
            transform: translateY(-8px) scale(1.1);
            filter: drop-shadow(0 8px 16px rgba(59, 130, 246, 0.4));
        }

        .letter:nth-child(1) { animation-delay: 0.1s; }
        .letter:nth-child(2) { animation-delay: 0.2s; }
        .letter:nth-child(3) { animation-delay: 0.3s; }
        .letter:nth-child(4) { animation-delay: 0.4s; }

        @keyframes letterBounce {
            0%, 100% {
                transform: translateY(0) scale(1);
            }
            50% {
                transform: translateY(-5px) scale(1.05);
            }
        }

        .app-title:hover .letter {
            animation: letterBounce 0.6s ease-in-out;
        }

        .app-subtitle {
            font-size: 1.1rem;
            color: #475569;
            font-weight: 500;
            margin-bottom: 0;
            opacity: 0.9;
        }

        /* Form styling moderno */
        .form-group {
            position: relative;
            margin-bottom: 28px;
            transition: var(--transition);
        }

        .input-wrapper {
            position: relative;
        }

        .form-label {
            font-weight: 600;
            color: #334155;
            margin-bottom: 10px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px 20px 16px 50px;
            font-size: 1rem;
            font-weight: 500;
            transition: var(--transition);
            background: #ffffff;
            color: #1e293b;
            width: 100%;
            box-shadow:
                inset 0 1px 3px rgba(0,0,0,0.1),
                0 1px 3px rgba(0,0,0,0.05);
            opacity: 0;
            transform: translateY(20px);
        }

        .form-control::placeholder {
            color: #94a3b8;
            opacity: 0.8;
        }

        .form-control:focus {
            border-color: #3b82f6;
            box-shadow:
                0 0 0 4px rgba(59, 130, 246, 0.1),
                inset 0 1px 3px rgba(0,0,0,0.1),
                0 4px 12px rgba(0,0,0,0.1);
            background: #ffffff;
            transform: translateY(-2px);
            outline: none;
        }

        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 1.2rem;
            transition: var(--transition);
            z-index: 1;
        }

        .form-control:focus + .input-icon {
            color: #3b82f6;
            transform: translateY(-50%) scale(1.1);
        }

        /* Pulsante login professionale */
        .btn-login {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            width: 100%;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 25px rgba(59, 130, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            letter-spacing: 0.3px;
            margin-top: 15px;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow:
                0 12px 30px rgba(59, 130, 246, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        }

        .btn-login:active {
            transform: translateY(-2px);
            transition: transform 0.1s ease;
        }

        .btn-login:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* Alert personalizzati moderni */
        .custom-alert {
            border-radius: var(--border-radius);
            padding: 16px 20px;
            margin-bottom: 24px;
            font-size: 0.95rem;
            font-weight: 500;
            animation: alertSlide 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .custom-alert.error {
            background: rgba(248, 113, 113, 0.15);
            border: 1px solid rgba(248, 113, 113, 0.3);
            color: #dc2626;
        }

        .custom-alert.warning {
            background: rgba(251, 191, 36, 0.15);
            border: 1px solid rgba(251, 191, 36, 0.3);
            color: #d97706;
        }

        .custom-alert.info {
            background: rgba(59, 130, 246, 0.15);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #2563eb;
        }

        .custom-alert.session-expired {
            background: rgba(168, 85, 247, 0.15);
            border: 1px solid rgba(168, 85, 247, 0.3);
            color: #7c3aed;
        }

        @keyframes alertSlide {
            0% {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .alert-icon {
            font-size: 1.3rem;
            flex-shrink: 0;
        }

        /* Link registrazione moderno */
        .register-link {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .register-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 1px;
            background: var(--primary-gradient);
        }

        .register-link a {
            color: #475569;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 10px;
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .register-link a:hover {
            color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.2);
        }

        /* Responsive Design Moderno */
        @media (max-width: 1024px) {
            .login-content-wrapper {
                grid-template-columns: 1fr;
            }

            .login-logo-section {
                border-radius: 20px 20px 0 0;
                order: -1;
                min-height: 300px;
            }

            .login-form-section {
                padding: 40px 30px;
            }
        }

        @media (max-width: 768px) {
            .login-main-container {
                max-width: 420px;
                margin: 20px;
            }

            .login-form-section {
                padding: 35px 25px;
            }

            .login-logo-section {
                padding: 35px 25px;
                min-height: 250px;
            }

            .app-title {
                font-size: 1.9rem;
            }

            .logo-container {
                width: 170px;
                height: 170px;
            }

            .logo-image {
                width: 130px;
                height: 130px;
            }

            .logo-icon {
                font-size: 5.5rem;
            }

            .form-control {
                padding: 14px 18px 14px 45px;
            }

            .btn-login {
                padding: 16px 28px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .login-main-container {
                margin: 10px;
                max-width: 100%;
            }

            .login-form-section {
                padding: 30px 20px;
            }

            .login-logo-section {
                padding: 30px 20px;
                min-height: 200px;
            }

            .app-title {
                font-size: 1.6rem;
            }

            .app-subtitle {
                font-size: 0.9rem;
            }

            .logo-container {
                width: 140px;
                height: 140px;
            }

            .logo-image {
                width: 105px;
                height: 105px;
            }

            .logo-icon {
                font-size: 4.5rem;
            }

            .form-control {
                padding: 12px 16px 12px 42px;
                font-size: 0.95rem;
            }

            .btn-login {
                padding: 14px 24px;
                font-size: 0.95rem;
            }

            .floating-particles {
                display: none; /* Nasconde le particelle su mobile per performance */
            }
        }

        @media (max-width: 320px) {
            .login-form-section {
                padding: 25px 15px;
            }

            .login-logo-section {
                padding: 25px 15px;
                min-height: 180px;
            }

            .app-title {
                font-size: 1.4rem;
            }

            .logo-container {
                width: 120px;
                height: 120px;
            }

            .logo-image {
                width: 90px;
                height: 90px;
            }

            .logo-icon {
                font-size: 4rem;
            }
        }

        /* Animazioni ridotte per dispositivi con preferenze di accessibilità */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .floating-particles {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Particelle fluttuanti -->
    <div class="floating-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Container principale con layout a due colonne -->
    <div class="login-main-container">
        <div class="login-content-wrapper">
            <!-- Colonna sinistra - Form di login -->
            <div class="login-form-section">
                <!-- Header titolo -->
                <div class="form-header">
                    <h1 class="app-title" id="animatedTitle">
                        <span class="letter" data-letter="S">S</span>
                        <span class="letter" data-letter="N">N</span>
                        <span class="letter" data-letter="I">I</span>
                        <span class="letter" data-letter="P">P</span>
                    </h1>
                    <p class="app-subtitle">Sistema Navale Integrato Portuale</p>
                </div>

                <!-- Alert messaggi -->
                {% if error %}
                <div class="custom-alert error">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <span>{{ error }}</span>
                </div>
                {% endif %}

                <!-- Gestione messaggi da URL parameters -->
                <div id="urlMessageContainer"></div>

                <!-- Form di login moderno -->
                <form method="post" action="/login" id="loginForm" novalidate>
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-envelope"></i>
                    Indirizzo Email
                </label>
                <div class="input-wrapper">
                    <input type="email" class="form-control" id="username" name="username"
                           required autofocus autocomplete="email"
                           placeholder="<EMAIL>">
                    <i class="fas fa-envelope input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <div class="input-wrapper">
                    <input type="password" class="form-control" id="password" name="password"
                           required autocomplete="current-password"
                           placeholder="Inserisci la tua password">
                    <i class="fas fa-lock input-icon"></i>
                </div>
            </div>

                <button type="submit" class="btn btn-login" id="loginButton">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    <span class="btn-text">Accedi al Sistema</span>
                </button>
            </form>

            <!-- Link registrazione moderno -->
            <div class="register-link">
                <a href="/register">
                    <i class="fas fa-user-plus"></i>
                    Non hai un account? Registrati qui
                </a>
            </div>
        </div>

        <!-- Colonna destra - Logo aziendale -->
        <div class="login-logo-section">
            <div class="logo-container">
                <!-- Logo principale SNIP -->
                <img src="/static/images/logo.png" alt="Logo SNIP" class="logo-image"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <i class="fas fa-anchor logo-icon" style="display: none;"></i>
            </div>
            <div class="logo-text">
                <h2 class="logo-title">Benvenuto</h2>
                <p class="logo-description">
                    Sistema integrato per la gestione delle operazioni portuali e navali.
                </p>
            </div>
        </div>
    </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/alerts.js"></script>

    <script>
        // Sistema di animazioni e interazioni moderne
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const inputs = form.querySelectorAll('.form-control');
            const loginButton = document.getElementById('loginButton');
            const loginContainer = document.querySelector('.login-main-container');

            // Animazione di entrata ritardata per gli elementi
            setTimeout(() => {
                loginContainer.style.opacity = '1';
                loginContainer.style.transform = 'translateY(0) scale(1)';
            }, 100);

            // Animazione lettere del titolo SNIP
            const letters = document.querySelectorAll('.letter');
            letters.forEach((letter, index) => {
                letter.style.opacity = '0';
                letter.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    letter.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    letter.style.opacity = '1';
                    letter.style.transform = 'translateY(0)';
                }, 500 + (index * 150)); // Ritardo progressivo per ogni lettera
            });

            // Gestione messaggi da URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const message = urlParams.get('message');
            const expired = urlParams.get('expired');
            const messageContainer = document.getElementById('urlMessageContainer');

            if (message) {
                let alertClass = 'info';
                let iconClass = 'fas fa-info-circle';
                let displayMessage = decodeURIComponent(message);

                // Determina il tipo di alert basato sul contenuto del messaggio
                if (expired === 'true' || message.toLowerCase().includes('scadut') || message.toLowerCase().includes('session')) {
                    alertClass = 'session-expired';
                    iconClass = 'fas fa-clock';

                    // Personalizza il messaggio per la sessione scaduta
                    if (message.includes('La tua sessione è scaduta')) {
                        displayMessage = '🔒 La tua sessione è scaduta per motivi di sicurezza. Effettua nuovamente l\'accesso per continuare.';
                    } else if (message.includes('Sessione scaduta')) {
                        displayMessage = '⏰ Sessione scaduta. Effettua nuovamente l\'accesso.';
                    } else if (message.includes('Accesso scaduto')) {
                        displayMessage = '📱 Accesso scaduto. Rieffettua il login.';
                    }
                } else if (message.toLowerCase().includes('error') || message.toLowerCase().includes('errore')) {
                    alertClass = 'error';
                    iconClass = 'fas fa-exclamation-triangle';
                } else if (message.toLowerCase().includes('warning') || message.toLowerCase().includes('attenzione')) {
                    alertClass = 'warning';
                    iconClass = 'fas fa-exclamation-triangle';
                }

                // Crea e mostra l'alert
                const alertDiv = document.createElement('div');
                alertDiv.className = `custom-alert ${alertClass}`;
                alertDiv.innerHTML = `
                    <i class="${iconClass} alert-icon"></i>
                    <span>${displayMessage}</span>
                `;
                messageContainer.appendChild(alertDiv);

                // Rimuovi i parametri dall'URL senza ricaricare la pagina
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }

            // Animazioni avanzate per gli input
            inputs.forEach((input, index) => {
                // Animazione di entrata ritardata
                setTimeout(() => {
                    input.style.opacity = '1';
                    input.style.transform = 'translateY(0)';
                }, 200 + (index * 100));

                // Effetti focus/blur migliorati
                input.addEventListener('focus', function() {
                    this.closest('.form-group').style.transform = 'scale(1.02)';
                    this.closest('.form-group').style.zIndex = '10';
                });

                input.addEventListener('blur', function() {
                    this.closest('.form-group').style.transform = 'scale(1)';
                    this.closest('.form-group').style.zIndex = '1';
                });

                // Validazione in tempo reale
                input.addEventListener('input', function() {
                    if (this.validity.valid) {
                        this.style.borderColor = 'rgba(34, 197, 94, 0.6)';
                    } else if (this.value.length > 0) {
                        this.style.borderColor = 'rgba(248, 113, 113, 0.6)';
                    } else {
                        this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    }
                });
            });

            // Validazione e animazione submit migliorata
            form.addEventListener('submit', function(e) {
                // VALIDAZIONE LATO CLIENT
                const emailInput = document.getElementById('username');
                const passwordInput = document.getElementById('password');

                // Rimuovi messaggi di errore precedenti
                const existingErrors = document.querySelectorAll('.validation-error');
                existingErrors.forEach(error => error.remove());

                let hasErrors = false;

                // Validazione email
                if (!emailInput.value.trim()) {
                    showFieldError(emailInput, '⚠️ Email richiesta');
                    hasErrors = true;
                } else if (!isValidEmail(emailInput.value.trim())) {
                    showFieldError(emailInput, '⚠️ Formato email non valido');
                    hasErrors = true;
                }

                // Validazione password
                if (!passwordInput.value.trim()) {
                    showFieldError(passwordInput, '⚠️ Password richiesta');
                    hasErrors = true;
                }

                // Se ci sono errori, blocca il submit
                if (hasErrors) {
                    e.preventDefault();
                    return false;
                }

                const btnText = loginButton.querySelector('.btn-text');
                const btnIcon = loginButton.querySelector('i');

                btnIcon.className = 'fas fa-spinner fa-spin me-2';
                btnText.textContent = 'Accesso in corso...';
                loginButton.disabled = true;
                loginButton.style.transform = 'scale(0.98)';

                // Animazione del container durante il login
                loginContainer.style.filter = 'blur(1px)';
                loginContainer.style.opacity = '0.8';
            });

            // Effetto hover discreto per professionalità
            loginContainer.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            loginContainer.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0px)';
            });

            // Prevenzione del doppio submit
            let isSubmitting = false;
            form.addEventListener('submit', function(e) {
                if (isSubmitting) {
                    e.preventDefault();
                    return false;
                }
                isSubmitting = true;
            });
        });

        // Funzioni helper per validazione
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showFieldError(field, message) {
            // Rimuovi errore esistente per questo campo
            const existingError = field.parentNode.querySelector('.validation-error');
            if (existingError) {
                existingError.remove();
            }

            // Crea nuovo messaggio di errore
            const errorDiv = document.createElement('div');
            errorDiv.className = 'validation-error';
            errorDiv.style.cssText = `
                color: #ef4444;
                font-size: 0.875rem;
                margin-top: 0.5rem;
                padding: 0.5rem;
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 0.375rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            `;
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i>${message}`;

            // Aggiungi dopo il campo
            field.parentNode.appendChild(errorDiv);

            // Evidenzia il campo con errore
            field.style.borderColor = '#ef4444';
            field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';

            // Focus sul campo con errore
            field.focus();

            // Rimuovi evidenziazione quando l'utente inizia a digitare
            field.addEventListener('input', function() {
                field.style.borderColor = '';
                field.style.boxShadow = '';
                if (errorDiv && errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, { once: true });
        }
    </script>
</body>
</html>