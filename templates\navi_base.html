<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestione Navi - Base</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Gestione Navi</h1>

        {% if error %}
        <div class="alert alert-danger">
            {{ error }}
        </div>
        {% endif %}

        <!-- Form per aggiungere una nuova nave -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Aggiungi Nuova Nave</h5>
            </div>
            <div class="card-body">
                <form action="/operativo/navi/add" method="post">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="nome_nave" class="form-label">Nome Nave</label>
                            <input type="text" class="form-control" id="nome_nave" name="nome_nave" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="codice_nave" class="form-label">Codice Nave</label>
                            <input type="text" class="form-control" id="codice_nave" name="codice_nave" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="prefisso_viaggio" class="form-label">Prefisso Viaggio</label>
                            <input type="text" class="form-control" id="prefisso_viaggio" name="prefisso_viaggio" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Aggiungi Nave</button>
                </form>
            </div>
        </div>

        <!-- Tabella delle navi -->
        <div class="card">
            <div class="card-header">
                <h5>Lista Navi</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome Nave</th>
                            <th>Codice Nave</th>
                            <th>Prefisso Viaggio</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for nave in navi %}
                        <tr>
                            <td>{{ nave.id }}</td>
                            <td>{{ nave.nave }}</td>
                            <td>{{ nave.codice_nave }}</td>
                            <td>{{ nave.prefisso_viaggio }}</td>
                        </tr>
                        {% endfor %}
                        {% if not navi %}
                        <tr>
                            <td colspan="4" class="text-center">Nessuna nave trovata</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
