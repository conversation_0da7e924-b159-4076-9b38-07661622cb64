<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifiche - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-content {
            margin-top: 20px;
        }
        .notifications-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .notification-item {
            border-left: 4px solid #007bff;
            background: rgba(0, 123, 255, 0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .notification-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .notification-info { border-left-color: #17a2b8; background: rgba(23, 162, 184, 0.05); }
        .notification-success { border-left-color: #28a745; background: rgba(40, 167, 69, 0.05); }
        .notification-warning { border-left-color: #ffc107; background: rgba(255, 193, 7, 0.05); }
        .notification-danger { border-left-color: #dc3545; background: rgba(220, 53, 69, 0.05); }
    </style>
</head>
<body>
    <!-- Navbar principale -->
    {% include 'components/navbar.html' %}

    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="notifications-card p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="fas fa-bell me-2"></i>Notifiche</h2>
                            <p class="text-muted">Le tue notifiche recenti</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary me-2">
                                <i class="fas fa-check-double me-2"></i>Segna tutte come lette
                            </button>
                            <button class="btn btn-outline-danger">
                                <i class="fas fa-trash me-2"></i>Elimina tutte
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        {% for notification in notifications %}
                        <div class="col-12 mb-3">
                            <div class="notification-item notification-{{ notification.type }} p-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            {% if notification.type == 'info' %}
                                                <i class="fas fa-info-circle text-info me-2"></i>
                                            {% elif notification.type == 'success' %}
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                            {% elif notification.type == 'warning' %}
                                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                            {% elif notification.type == 'danger' %}
                                                <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                            {% endif %}
                                            <h6 class="mb-0">{{ notification.title }}</h6>
                                        </div>
                                        <p class="mb-1">{{ notification.message }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>{{ notification.time }}
                                        </small>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>Segna come letta</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="fas fa-star me-2"></i>Importante</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Elimina</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    {% if not notifications %}
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Nessuna notifica</h4>
                        <p class="text-muted">Non hai notifiche al momento</p>
                    </div>
                    {% endif %}

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-filter me-2"></i>Filtri</h5>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="filter" id="all" checked>
                                <label class="btn btn-outline-primary" for="all">Tutte</label>

                                <input type="radio" class="btn-check" name="filter" id="unread">
                                <label class="btn btn-outline-primary" for="unread">Non lette</label>

                                <input type="radio" class="btn-check" name="filter" id="important">
                                <label class="btn btn-outline-primary" for="important">Importanti</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-cog me-2"></i>Impostazioni Notifiche</h5>
                            <a href="/settings" class="btn btn-outline-secondary">
                                <i class="fas fa-cog me-2"></i>Gestisci Preferenze
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
