{% extends "base_operativo.html" %}

{% block title %}Gestione Armatori - M.A.P.{% endblock %}

{% block extra_css %}
<link href="/static/css/armatori.css" rel="stylesheet">
{% endblock %}

{% block content %}
        <!-- Header della pagina migliorato -->
        <div class="page-header text-center">
            <h2><i class="fas fa-anchor me-3"></i>Gestione Armatori</h2>
            <p class="subtitle">Gestisci tutti gli armatori del sistema M.A.P.</p>
        </div>

        <!-- Messaggio di errore -->
        {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        <!-- Messaggio di successo -->
        {% if success %}
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ success }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        <!-- Form per aggiungere un nuovo armatore -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Aggiungi Nuovo Armatore</h5>
            </div>
            <div class="card-body">
                <form action="/operativo/armatori/add" method="post">
                    <div class="row align-items-end">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="nome_armatore" class="form-label">
                                    <i class="fas fa-building me-2"></i>Nome Armatore
                                </label>
                                <input type="text" class="form-control" id="nome_armatore" name="nome_armatore"
                                       placeholder="Inserisci il nome dell'armatore..." required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-save me-2"></i>Aggiungi Armatore
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabella degli armatori -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista Armatori</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-building me-2"></i>Nome Armatore</th>
                                <th class="text-center"><i class="fas fa-cogs me-2"></i>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for armatore in armatori %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="fas fa-anchor"></i>
                                        </div>
                                        <strong>{{ armatore.Nome_Armatore }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary" title="Modifica Armatore"
                                                data-bs-toggle="modal" data-bs-target="#editArmatoreModal"
                                                onclick="editArmatore({{ armatore.id }}, '{{ armatore.Nome_Armatore|replace("'", "\\'") }}')">
                                            <i class="fas fa-edit me-1"></i>Modifica
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Elimina Armatore"
                                                data-bs-toggle="modal" data-bs-target="#deleteArmatoreModal"
                                                onclick="deleteArmatore({{ armatore.id }}, '{{ armatore.Nome_Armatore|replace("'", "\\'") }}')">
                                            <i class="fas fa-trash me-1"></i>Elimina
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not armatori %}
                            <tr>
                                <td colspan="2" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <h5>Nessun armatore trovato</h5>
                                        <p>Aggiungi il primo armatore utilizzando il form sopra</p>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Modifica Armatore -->
    <div class="modal fade" id="editArmatoreModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Modifica Armatore
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form action="/operativo/armatori/edit" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="editArmatoreId" name="armatore_id">
                        <div class="mb-3">
                            <label for="editNomeArmatore" class="form-label">
                                <i class="fas fa-building me-2"></i>Nome Armatore
                            </label>
                            <input type="text" class="form-control" id="editNomeArmatore" name="nome_armatore"
                                   placeholder="Inserisci il nuovo nome..." required>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Modifica il nome dell'armatore e clicca "Salva Modifiche" per confermare.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salva Modifiche
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Elimina Armatore -->
    <div class="modal fade" id="deleteArmatoreModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-trash me-2"></i>Elimina Armatore
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form action="/operativo/armatori/delete" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="deleteArmatoreId" name="armatore_id">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
                            <h5>Conferma Eliminazione</h5>
                            <p class="mb-0">Sei sicuro di voler eliminare l'armatore:</p>
                            <h6 class="text-primary mt-2" id="deleteArmatoreNome"></h6>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Attenzione!</strong> Questa azione non può essere annullata.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Elimina Definitivamente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/alerts.js"></script>
    <script>
        function editArmatore(id, nome) {
            document.getElementById('editArmatoreId').value = id;
            document.getElementById('editNomeArmatore').value = nome;
        }

        function deleteArmatore(id, nome) {
            document.getElementById('deleteArmatoreId').value = id;
            document.getElementById('deleteArmatoreNome').textContent = nome;
        }
    </script>

{% endblock %}

{% block extra_js %}
<script src="/static/js/alerts.js"></script>
{% endblock %}
