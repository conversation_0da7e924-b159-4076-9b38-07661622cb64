<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Il Mio Profilo - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            font-family: 'Inter', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        /* Animazione sfondo gradiente */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Particelle fluttuanti di sfondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.06) 1.5px, transparent 1.5px);
            background-size: 100px 100px, 150px 150px, 200px 200px;
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }

        .main-content {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 8px 32px rgba(102, 126, 234, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 200% 0; }
            50% { background-position: -200% 0; }
        }

        .profile-card:hover {
            transform: translateY(-8px);
            box-shadow:
                0 32px 80px rgba(0, 0, 0, 0.2),
                0 16px 48px rgba(102, 126, 234, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        /* ===== STILI HEADER PROFILO MIGLIORATO ===== */
        .profile-header {
            position: relative;
            padding: 40px 20px;
        }

        .avatar-container {
            position: relative;
            display: inline-block;
            margin-bottom: 24px;
        }

        .avatar-wrapper {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        .avatar-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            box-shadow:
                0 16px 32px rgba(102, 126, 234, 0.3),
                0 8px 16px rgba(0, 0, 0, 0.1),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .avatar-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .avatar-image:hover::before {
            transform: translateX(100%);
        }

        .avatar-image:hover {
            transform: scale(1.05);
            box-shadow:
                0 20px 40px rgba(102, 126, 234, 0.4),
                0 12px 24px rgba(0, 0, 0, 0.15),
                inset 0 2px 4px rgba(255, 255, 255, 0.3);
        }

        .avatar-status {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: #10b981;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .avatar-edit-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(245, 87, 108, 0.4);
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
        }

        .avatar-container:hover .avatar-edit-btn {
            opacity: 1;
            transform: scale(1);
        }

        .avatar-edit-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(245, 87, 108, 0.6);
        }

        .profile-info {
            text-align: center;
        }

        .profile-name {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }

        .profile-role {
            font-size: 1.1rem;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 16px;
        }

        .profile-badges {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .badge-status {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .badge-department {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .badge i {
            font-size: 0.75rem;
        }

        /* ===== STILI SEZIONI INFORMAZIONI MIGLIORATE ===== */
        .info-section {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 20px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            height: 100%;
        }

        .info-section:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .title-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .section-title h5 {
            margin: 0;
            font-weight: 600;
            color: #374151;
            font-size: 1.1rem;
        }

        .info-fields {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .field-group {
            position: relative;
        }

        .field-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-input {
            position: relative;
            display: flex;
            align-items: center;
        }

        .field-icon {
            position: absolute;
            left: 20px !important;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 14px;
            z-index: 2;
            transition: all 0.3s ease;
            pointer-events: none;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modern-input {
            width: 100%;
            padding: 14px 16px 14px 60px !important;
            border: 2px solid rgba(209, 213, 219, 0.5);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            font-size: 0.95rem;
            font-weight: 500;
            color: #374151;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            text-indent: 0 !important;
            padding-left: 60px !important;
        }

        .modern-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .modern-input:focus + .field-icon,
        .field-input:hover .field-icon {
            color: #667eea;
            transform: translateY(-50%) scale(1.1);
        }

        /* Forza il padding per evitare sovrapposizioni */
        .field-input .modern-input {
            padding-left: 60px !important;
            text-indent: 0 !important;
        }

        .field-input {
            position: relative;
        }

        .field-input .field-icon {
            left: 20px !important;
            z-index: 10 !important;
        }

        /* Regole specifiche per tema chiaro */
        body.theme-light .field-input .modern-input,
        body.theme-maritime .field-input .modern-input,
        body:not(.theme-dark) .field-input .modern-input {
            padding-left: 60px !important;
            text-indent: 0 !important;
            padding: 14px 16px 14px 60px !important;
        }

        body.theme-light .field-input .field-icon,
        body.theme-maritime .field-input .field-icon,
        body:not(.theme-dark) .field-input .field-icon {
            left: 20px !important;
            z-index: 10 !important;
            position: absolute !important;
        }

        /* Regola universale di sicurezza per tutti i temi */
        .profile-card .field-input .modern-input {
            padding-left: 60px !important;
            text-indent: 0 !important;
        }

        .profile-card .field-input .field-icon {
            left: 20px !important;
            z-index: 10 !important;
            position: absolute !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }

        /* Override per form-control standard */
        .field-input .form-control.modern-input {
            padding-left: 60px !important;
            text-indent: 0 !important;
            box-sizing: border-box !important;
        }



        .status-display {
            padding: 12px 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: rgba(16, 185, 129, 0.1);
            border: 2px solid rgba(16, 185, 129, 0.2);
            border-radius: 12px;
            font-weight: 500;
            color: #065f46;
            transition: all 0.3s ease;
        }

        .status-indicator:hover {
            background: rgba(16, 185, 129, 0.15);
            border-color: rgba(16, 185, 129, 0.3);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% {
                box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 0 8px rgba(16, 185, 129, 0.1);
                transform: scale(1.05);
            }
        }

        /* ===== PULSANTI AZIONE MIGLIORATI ===== */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .modern-btn-primary,
        .modern-btn-secondary {
            position: relative;
            padding: 16px 32px;
            border-radius: 16px;
            font-weight: 600;
            font-size: 1rem;
            border: none;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 180px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow:
                0 8px 24px rgba(102, 126, 234, 0.3),
                0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .modern-btn-primary:hover {
            transform: translateY(-3px);
            box-shadow:
                0 12px 32px rgba(102, 126, 234, 0.4),
                0 8px 16px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .modern-btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.3);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .modern-btn-secondary:hover {
            transform: translateY(-3px);
            background: rgba(102, 126, 234, 0.05);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
        }

        .btn-content {
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            z-index: 2;
        }

        .btn-content i {
            font-size: 1.1rem;
            transition: transform 0.3s ease;
        }

        .modern-btn-primary:hover .btn-content i,
        .modern-btn-secondary:hover .btn-content i {
            transform: scale(1.1);
        }

        .btn-ripple {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .modern-btn-primary:active .btn-ripple {
            width: 300px;
            height: 300px;
        }

        .modern-btn-secondary:active .btn-ripple {
            width: 300px;
            height: 300px;
            background: rgba(102, 126, 234, 0.2);
        }

        /* Responsive per pulsanti */
        @media (max-width: 576px) {
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .modern-btn-primary,
            .modern-btn-secondary {
                width: 100%;
                max-width: 280px;
            }
        }

        /* ===== ANIMAZIONI DI ENTRATA ===== */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Applicazione animazioni */
        .profile-card {
            animation: scaleIn 0.6s ease-out;
        }

        .profile-header {
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .info-section:nth-child(1) {
            animation: fadeInLeft 0.8s ease-out 0.4s both;
        }

        .info-section:nth-child(2) {
            animation: fadeInRight 0.8s ease-out 0.6s both;
        }

        .action-buttons {
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        /* Animazioni hover per le sezioni */
        .field-group {
            transition: all 0.3s ease;
        }

        .field-group:hover {
            transform: translateX(4px);
        }

        .field-group:hover .field-icon {
            color: #667eea;
            transform: scale(1.2);
        }

        /* ===== STILI MODALE MODIFICA PROFILO MIGLIORATO ===== */
        .modern-modal {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .gradient-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 25px 30px;
            position: relative;
        }

        .gradient-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
            z-index: 1;
        }

        .icon-container {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .icon-container i {
            font-size: 20px;
            color: white;
        }

        .title-container h4 {
            color: white;
            font-weight: 600;
            margin: 0;
        }

        .modern-body {
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
        }

        /* Alert Informativo Migliorato */
        .info-alert {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(155, 89, 182, 0.1) 100%);
            border: 1px solid rgba(52, 152, 219, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .alert-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3498db, #9b59b6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .alert-icon i {
            color: white;
            font-size: 18px;
        }

        .alert-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .alert-text {
            color: #5a6c7d;
            margin: 0;
            line-height: 1.5;
        }

        /* Sezione Contatti Amministratore */
        .admin-contacts-section {
            margin-bottom: 25px;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .section-header i {
            color: #667eea;
            font-size: 20px;
        }

        .section-header h5 {
            color: #2c3e50;
            margin: 0;
            font-weight: 600;
        }

        .contacts-grid {
            display: grid;
            gap: 15px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .contact-icon {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .email-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .phone-icon {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .time-icon {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .contact-icon i {
            color: white;
            font-size: 18px;
        }

        .contact-info {
            flex: 1;
        }

        .contact-info label {
            display: block;
            font-size: 12px;
            font-weight: 600;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .contact-info span {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }

        /* Sezione Istruzioni */
        .instructions-section {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 20px;
        }

        .instruction-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
        }

        .instruction-item:last-child {
            margin-bottom: 0;
        }

        .step-number {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .step-content h6 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .step-content p {
            color: #5a6c7d;
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }

        /* Footer Moderno */
        .modern-footer {
            background: rgba(248, 249, 250, 0.8);
            border-top: 1px solid rgba(102, 126, 234, 0.1);
            padding: 20px 30px;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .modern-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .modern-btn-secondary {
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 12px;
            padding: 10px 25px;
            font-weight: 500;
            color: #667eea;
            transition: all 0.3s ease;
        }

        .modern-btn-secondary:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.5);
            color: #5a6fd8;
            transform: translateY(-1px);
        }

        /* Animazioni */
        .modal.fade .modal-dialog {
            transition: transform 0.4s ease-out, opacity 0.4s ease-out;
            transform: translate(0, -50px) scale(0.95);
        }

        .modal.show .modal-dialog {
            transform: translate(0, 0) scale(1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modern-body > * {
            animation: slideInUp 0.6s ease-out forwards;
        }

        .modern-body > *:nth-child(2) {
            animation-delay: 0.1s;
        }

        .modern-body > *:nth-child(3) {
            animation-delay: 0.2s;
        }

        /* ===== TEMI INTERFACCIA ===== */

        /* Tema Marittimo (Default) */
        body.theme-maritime {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }

        /* ===== TEMA SCURO MIGLIORATO ===== */
        body.theme-dark {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        /* Sfondo particelle per tema scuro */
        body.theme-dark::before {
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.04) 1.5px, transparent 1.5px);
        }

        /* Profile Card tema scuro */
        body.theme-dark .profile-card {
            background: rgba(30, 30, 46, 0.95);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.4),
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        body.theme-dark .profile-card::before {
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        body.theme-dark .profile-card:hover {
            box-shadow:
                0 32px 80px rgba(0, 0, 0, 0.5),
                0 16px 48px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        /* Avatar tema scuro */
        body.theme-dark .avatar-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow:
                0 16px 32px rgba(102, 126, 234, 0.4),
                0 8px 16px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.1);
        }

        body.theme-dark .avatar-image:hover {
            box-shadow:
                0 20px 40px rgba(102, 126, 234, 0.5),
                0 12px 24px rgba(0, 0, 0, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
        }

        /* Nome profilo tema scuro */
        body.theme-dark .profile-name {
            background: linear-gradient(135deg, #a78bfa 0%, #c084fc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        body.theme-dark .profile-role {
            color: #94a3b8;
        }

        /* Badge tema scuro */
        body.theme-dark .badge-status {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }

        body.theme-dark .badge-department {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        /* Sezioni informazioni tema scuro */
        body.theme-dark .info-section {
            background: rgba(30, 30, 46, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
        }

        body.theme-dark .info-section:hover {
            background: rgba(30, 30, 46, 0.85);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
        }

        body.theme-dark .section-title {
            border-bottom: 2px solid rgba(167, 139, 250, 0.2);
        }

        body.theme-dark .title-icon {
            background: linear-gradient(135deg, #a78bfa 0%, #c084fc 100%);
            box-shadow: 0 4px 12px rgba(167, 139, 250, 0.4);
        }

        body.theme-dark .section-title h5 {
            color: #e2e8f0;
        }

        body.theme-dark .field-label {
            color: #94a3b8;
        }

        body.theme-dark .field-icon {
            color: #64748b;
            position: absolute;
            left: 20px !important;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            z-index: 2;
        }

        body.theme-dark .modern-input {
            background: rgba(30, 30, 46, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            padding: 14px 16px 14px 60px !important;
            font-weight: 500;
            text-align: left;
            padding-left: 60px !important;
            text-indent: 0 !important;
        }

        body.theme-dark .modern-input:focus {
            border-color: #a78bfa;
            background: rgba(30, 30, 46, 0.95);
            box-shadow: 0 0 0 4px rgba(167, 139, 250, 0.2);
        }

        body.theme-dark .modern-input:focus + .field-icon,
        body.theme-dark .field-input:hover .field-icon {
            color: #a78bfa;
            transform: translateY(-50%) scale(1.1);
        }

        /* Forza il padding per tema scuro */
        body.theme-dark .field-input .modern-input {
            padding-left: 60px !important;
            text-indent: 0 !important;
        }

        body.theme-dark .field-input .field-icon {
            left: 20px !important;
            z-index: 10 !important;
        }

        body.theme-dark .status-indicator {
            background: rgba(16, 185, 129, 0.15);
            border: 2px solid rgba(16, 185, 129, 0.3);
            color: #6ee7b7;
        }

        body.theme-dark .status-indicator:hover {
            background: rgba(16, 185, 129, 0.2);
            border-color: rgba(16, 185, 129, 0.4);
        }

        /* Pulsanti tema scuro */
        body.theme-dark .modern-btn-primary {
            background: linear-gradient(135deg, #a78bfa 0%, #c084fc 100%);
            box-shadow:
                0 8px 24px rgba(167, 139, 250, 0.4),
                0 4px 12px rgba(0, 0, 0, 0.2);
        }

        body.theme-dark .modern-btn-primary:hover {
            background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
            box-shadow:
                0 12px 32px rgba(167, 139, 250, 0.5),
                0 8px 16px rgba(0, 0, 0, 0.3);
        }

        body.theme-dark .modern-btn-secondary {
            background: rgba(30, 30, 46, 0.9);
            color: #a78bfa;
            border: 2px solid rgba(167, 139, 250, 0.3);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        body.theme-dark .modern-btn-secondary:hover {
            background: rgba(167, 139, 250, 0.1);
            border-color: rgba(167, 139, 250, 0.5);
            box-shadow: 0 8px 20px rgba(167, 139, 250, 0.3);
        }

        /* Form controls tema scuro */
        body.theme-dark .form-control {
            background: rgba(30, 30, 46, 0.8);
            border-color: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
        }

        body.theme-dark .form-control:focus {
            background: rgba(30, 30, 46, 0.95);
            border-color: #a78bfa;
            box-shadow: 0 0 0 4px rgba(167, 139, 250, 0.2);
        }

        body.theme-dark .form-label {
            color: #94a3b8;
        }

        body.theme-dark .form-select {
            background: rgba(30, 30, 46, 0.8);
            border-color: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
        }

        body.theme-dark .form-select:focus {
            border-color: #a78bfa;
            box-shadow: 0 0 0 4px rgba(167, 139, 250, 0.2);
        }

        /* Preferenze interfaccia tema scuro */
        body.theme-dark .preferences-section {
            background: rgba(30, 30, 46, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body.theme-dark .preferences-section h5 {
            color: #a78bfa;
        }

        body.theme-dark .form-check-label {
            color: #94a3b8;
        }

        body.theme-dark .form-check-input:checked {
            background-color: #a78bfa;
            border-color: #a78bfa;
        }

        body.theme-dark .form-check-input:focus {
            box-shadow: 0 0 0 4px rgba(167, 139, 250, 0.2);
        }

        /* Modali tema scuro */
        body.theme-dark .modern-modal {
            background: rgba(30, 30, 46, 0.98);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        body.theme-dark .gradient-header {
            background: linear-gradient(135deg, #a78bfa 0%, #c084fc 100%);
        }

        body.theme-dark .modern-body {
            background: rgba(30, 30, 46, 0.95);
            color: #e2e8f0;
        }

        body.theme-dark .info-alert {
            background: linear-gradient(135deg, rgba(167, 139, 250, 0.1) 0%, rgba(192, 132, 252, 0.1) 100%);
            border: 1px solid rgba(167, 139, 250, 0.2);
        }

        body.theme-dark .alert-icon {
            background: linear-gradient(135deg, #a78bfa, #c084fc);
        }

        body.theme-dark .alert-title {
            color: #e2e8f0;
        }

        body.theme-dark .alert-text {
            color: #94a3b8;
        }

        body.theme-dark .contact-item {
            background: rgba(30, 30, 46, 0.8);
            border: 1px solid rgba(167, 139, 250, 0.2);
        }

        body.theme-dark .contact-item:hover {
            border-color: rgba(167, 139, 250, 0.4);
            box-shadow: 0 8px 25px rgba(167, 139, 250, 0.2);
        }

        body.theme-dark .contact-info label {
            color: #64748b;
        }

        body.theme-dark .contact-info span {
            color: #e2e8f0;
        }

        body.theme-dark .instructions-section {
            background: rgba(167, 139, 250, 0.05);
        }

        body.theme-dark .step-content h6 {
            color: #e2e8f0;
        }

        body.theme-dark .step-content p {
            color: #94a3b8;
        }

        body.theme-dark .modern-footer {
            background: rgba(20, 20, 35, 0.8);
            border-top: 1px solid rgba(167, 139, 250, 0.2);
        }

        /* Alert e messaggi tema scuro */
        body.theme-dark .alert-info {
            background: rgba(167, 139, 250, 0.1);
            border-color: rgba(167, 139, 250, 0.3);
            color: #e2e8f0;
        }

        body.theme-dark .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #6ee7b7;
        }

        body.theme-dark .invalid-feedback {
            color: #fca5a5;
        }

        body.theme-dark .form-text {
            color: #94a3b8;
        }

        /* ===== CENTRAGGIO SPECIFICO TEMA SCURO ===== */

        /* Header profilo centraggio tema scuro */
        body.theme-dark .profile-header {
            text-align: center;
        }

        body.theme-dark .profile-info {
            text-align: center;
        }

        body.theme-dark .profile-badges {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* Avatar centraggio tema scuro */
        body.theme-dark .avatar-container {
            display: inline-block;
            text-align: center;
        }

        body.theme-dark .avatar-wrapper {
            margin: 0 auto;
        }

        /* Sezioni informazioni centraggio tema scuro */
        body.theme-dark .section-title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 12px;
        }

        body.theme-dark .info-fields {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        body.theme-dark .field-group {
            text-align: left;
        }

        body.theme-dark .field-label {
            display: block;
            text-align: left;
            margin-bottom: 8px;
        }

        body.theme-dark .field-input {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
        }

        /* Pulsanti centraggio tema scuro */
        body.theme-dark .action-buttons {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        /* Status display centraggio tema scuro */
        body.theme-dark .status-display {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        body.theme-dark .status-indicator {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 12px;
        }

        /* Responsive centraggio tema scuro */
        @media (max-width: 576px) {
            body.theme-dark .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            body.theme-dark .profile-badges {
                justify-content: center;
            }

            body.theme-dark .info-section {
                text-align: center;
            }

            body.theme-dark .section-title {
                justify-content: center;
            }

            body.theme-dark .field-group {
                text-align: center;
            }

            body.theme-dark .field-label {
                text-align: center;
            }
        }

        /* Tema Chiaro */
        body.theme-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        body.theme-light .profile-card {
            background: rgba(255, 255, 255, 0.98);
            color: #212529;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        /* Disabilita animazioni se richiesto */
        body.no-animations * {
            animation: none !important;
            transition: none !important;
        }

        /* Stili per la sezione preferenze */
        .preferences-section {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .preferences-section h5 {
            color: #667eea;
            margin-bottom: 20px;
        }

        .form-check-label {
            font-size: 14px;
            color: #6c757d;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .modal-dialog.modal-lg {
                margin: 10px;
            }

            .gradient-header {
                padding: 20px;
            }

            .modern-body {
                padding: 20px;
            }

            .header-content {
                gap: 10px;
            }

            .icon-container {
                width: 40px;
                height: 40px;
            }

            .contact-item {
                padding: 12px;
            }

            .contact-icon {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body class="theme-{{ user_theme|default('maritime') }}" data-theme="{{ user_theme|default('maritime') }}">
    <!-- Navbar principale -->
    {% include 'components/navbar.html' %}

    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="profile-card p-4">
                    <!-- Header Profilo Migliorato -->
                    <div class="profile-header text-center mb-5">
                        <div class="avatar-container">
                            <div class="avatar-wrapper">
                                <div class="avatar-image">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="avatar-status"></div>
                            </div>
                            <div class="avatar-edit-btn">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                        <div class="profile-info">
                            <h2 class="profile-name">{{ current_user.Nome }} {{ current_user.Cognome }}</h2>
                            <p class="profile-role">{{ current_user.ruolo.value if current_user.ruolo.value else current_user.ruolo }}</p>
                            <div class="profile-badges">
                                <span class="badge badge-status">
                                    <i class="fas fa-circle"></i> Attivo
                                </span>
                                <span class="badge badge-department">
                                    <i class="fas fa-building"></i> {{ current_user.reparto.value if current_user.reparto.value else current_user.reparto }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Sezioni Informazioni Migliorate -->
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="title-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <h5>Informazioni Personali</h5>
                                </div>
                                <div class="info-fields">
                                    <div class="field-group">
                                        <label class="field-label">Nome</label>
                                        <div class="field-input">
                                            <input type="text" class="form-control modern-input" value="{{ current_user.Nome }}" readonly>
                                            <i class="field-icon fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label">Cognome</label>
                                        <div class="field-input">
                                            <input type="text" class="form-control modern-input" value="{{ current_user.Cognome }}" readonly>
                                            <i class="field-icon fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label">Email</label>
                                        <div class="field-input">
                                            <input type="email" class="form-control modern-input" value="{{ current_user.email }}" readonly>
                                            <i class="field-icon fas fa-envelope"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="title-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <h5>Informazioni Lavorative</h5>
                                </div>
                                <div class="info-fields">
                                    <div class="field-group">
                                        <label class="field-label">Reparto</label>
                                        <div class="field-input">
                                            <input type="text" class="form-control modern-input" value="{{ current_user.reparto.value if current_user.reparto.value else current_user.reparto }}" readonly>
                                            <i class="field-icon fas fa-building"></i>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label">Ruolo</label>
                                        <div class="field-input">
                                            <input type="text" class="form-control modern-input" value="{{ current_user.ruolo.value if current_user.ruolo.value else current_user.ruolo }}" readonly>
                                            <i class="field-icon fas fa-briefcase"></i>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label">Stato Account</label>
                                        <div class="status-display">
                                            <div class="status-indicator active">
                                                <div class="status-dot"></div>
                                                <span>Attivo</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Sezione Preferenze Interfaccia -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5><i class="fas fa-palette me-2"></i>Preferenze Interfaccia</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Tema</label>
                                        <select class="form-select" id="interfaceTheme">
                                            <option value="maritime">Marittimo (Default)</option>
                                            <option value="dark">Scuro</option>
                                            <option value="light">Chiaro</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Lingua</label>
                                        <select class="form-select" id="interfaceLanguage">
                                            <option value="it" selected>Italiano</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Opzioni</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableAnimations" checked>
                                            <label class="form-check-label" for="enableAnimations">
                                                Abilita animazioni
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mt-2">
                                            <input class="form-check-input" type="checkbox" id="enableSounds" checked>
                                            <label class="form-check-label" for="enableSounds">
                                                Suoni interfaccia
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button class="btn btn-outline-primary btn-sm" id="btnSalvaPreferenze">
                                    <i class="fas fa-save me-2"></i>Salva Preferenze
                                </button>
                                <button class="btn btn-outline-secondary btn-sm ms-2" id="btnRipristinaPreferenze">
                                    <i class="fas fa-undo me-2"></i>Ripristina Default
                                </button>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Pulsanti Azione Migliorati -->
                    <div class="action-buttons">
                        <button class="btn modern-btn-primary" id="btnModificaProfilo">
                            <div class="btn-content">
                                <i class="fas fa-edit"></i>
                                <span>Modifica Profilo</span>
                            </div>
                            <div class="btn-ripple"></div>
                        </button>
                        <button class="btn modern-btn-secondary" id="btnCambiaPassword">
                            <div class="btn-content">
                                <i class="fas fa-key"></i>
                                <span>Cambia Password</span>
                            </div>
                            <div class="btn-ripple"></div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modale Cambio Password -->
    <div class="modal fade" id="modalCambiaPassword" tabindex="-1" aria-labelledby="modalCambiaPasswordLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCambiaPasswordLabel">
                        <i class="fas fa-key me-2"></i>Cambia Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="formCambiaPassword">
                        <!-- Step 1: Verifica password attuale -->
                        <div id="step1-verifica" class="password-step">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Per sicurezza, inserisci prima la tua password attuale
                            </div>
                            <div class="mb-3">
                                <label for="passwordAttuale" class="form-label">Password Attuale</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="passwordAttuale" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePasswordAttuale">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="errorPasswordAttuale"></div>
                            </div>
                            <div class="text-end">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                                <button type="button" class="btn btn-primary" id="btnVerificaPassword">
                                    <i class="fas fa-check me-2"></i>Verifica Password
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Nuova password (nascosto inizialmente) -->
                        <div id="step2-nuova" class="password-step" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                Password verificata! Ora inserisci la nuova password
                            </div>
                            <div class="mb-3">
                                <label for="nuovaPassword" class="form-label">Nuova Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="nuovaPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNuovaPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    La password deve contenere almeno 8 caratteri
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="confermaNuovaPassword" class="form-label">Conferma Nuova Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confermaNuovaPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfermaNuovaPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="errorConfermaNuovaPassword"></div>
                            </div>
                            <div class="text-end">
                                <button type="button" class="btn btn-secondary" id="btnTornaIndietro">
                                    <i class="fas fa-arrow-left me-2"></i>Indietro
                                </button>
                                <button type="submit" class="btn btn-success" id="btnSalvaNuovaPassword">
                                    <i class="fas fa-save me-2"></i>Salva Nuova Password
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modale Messaggio Amministratore - Design Migliorato -->
    <div class="modal fade" id="modalMessaggioAdmin" tabindex="-1" aria-labelledby="modalMessaggioAdminLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modern-modal">
                <div class="modal-header gradient-header">
                    <div class="header-content">
                        <div class="icon-container">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="title-container">
                            <h4 class="modal-title mb-0" id="modalMessaggioAdminLabel">Modifica Profilo</h4>
                            <small class="text-light opacity-75">Richiesta modifiche informazioni personali</small>
                        </div>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modern-body">
                    <!-- Alert Informativo Migliorato -->
                    <div class="info-alert">
                        <div class="alert-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="alert-title">Procedura di Modifica</h6>
                            <p class="alert-text">Per modificare le informazioni del tuo profilo (nome, cognome, email, reparto, ruolo), è necessario contattare l'amministratore del sistema.</p>
                        </div>
                    </div>

                    <!-- Sezione Contatti Amministratore Migliorata -->
                    <div class="admin-contacts-section">
                        <div class="section-header">
                            <i class="fas fa-address-book"></i>
                            <h5>Contatti Amministratore</h5>
                        </div>

                        <div class="contacts-grid">
                            <div class="contact-item">
                                <div class="contact-icon email-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-info">
                                    <label>Email</label>
                                    <span><EMAIL></span>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon phone-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-info">
                                    <label>Telefono</label>
                                    <span>+39 089 230 307</span>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon time-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="contact-info">
                                    <label>Orari</label>
                                    <span>Lun-Ven 9:00-18:00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sezione Istruzioni -->
                    <div class="instructions-section">
                        <div class="instruction-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h6>Clicca "Invia Email"</h6>
                                <p>Si aprirà il tuo client email con un template precompilato</p>
                            </div>
                        </div>

                        <div class="instruction-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h6>Compila i Dettagli</h6>
                                <p>Specifica quali informazioni desideri modificare e il motivo</p>
                            </div>
                        </div>

                        <div class="instruction-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h6>Invia la Richiesta</h6>
                                <p>L'amministratore processerà la tua richiesta entro 24-48 ore</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer modern-footer">
                    <button type="button" class="btn btn-outline-secondary modern-btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Chiudi
                    </button>
                    <a href="mailto:<EMAIL>?subject=Richiesta Modifica Profilo&body=Gentile Amministratore,%0A%0ADesidero richiedere la modifica delle seguenti informazioni del mio profilo:%0A%0A- Nome:%0A- Cognome:%0A- Email:%0A- Reparto:%0A- Ruolo:%0A%0AMotivo della richiesta:%0A%0A%0ACordiali saluti,%0A{{ current_user.Nome }} {{ current_user.Cognome }}" class="btn btn-primary modern-btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Invia Email
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/messages.js"></script>
    <script src="/static/js/theme-manager.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Inizializzazione pagina profilo...');

            // Elementi DOM
            const btnModificaProfilo = document.getElementById('btnModificaProfilo');
            const btnCambiaPassword = document.getElementById('btnCambiaPassword');
            const modalCambiaPassword = new bootstrap.Modal(document.getElementById('modalCambiaPassword'));
            const modalMessaggioAdmin = new bootstrap.Modal(document.getElementById('modalMessaggioAdmin'));

            // Form cambio password
            const formCambiaPassword = document.getElementById('formCambiaPassword');
            const step1Verifica = document.getElementById('step1-verifica');
            const step2Nuova = document.getElementById('step2-nuova');

            // Input password
            const passwordAttuale = document.getElementById('passwordAttuale');
            const nuovaPassword = document.getElementById('nuovaPassword');
            const confermaNuovaPassword = document.getElementById('confermaNuovaPassword');

            // Pulsanti
            const btnVerificaPassword = document.getElementById('btnVerificaPassword');
            const btnTornaIndietro = document.getElementById('btnTornaIndietro');
            const btnSalvaNuovaPassword = document.getElementById('btnSalvaNuovaPassword');

            // Toggle password visibility
            const togglePasswordAttuale = document.getElementById('togglePasswordAttuale');
            const toggleNuovaPassword = document.getElementById('toggleNuovaPassword');
            const toggleConfermaNuovaPassword = document.getElementById('toggleConfermaNuovaPassword');

            console.log('✅ Elementi DOM inizializzati');

            // 1. MODIFICA PROFILO - Mostra messaggio amministratore
            btnModificaProfilo.addEventListener('click', function() {
                console.log('🖱️ Click su Modifica Profilo');
                modalMessaggioAdmin.show();
            });

            // 2. CAMBIO PASSWORD - Apre modale
            btnCambiaPassword.addEventListener('click', function() {
                console.log('🖱️ Click su Cambia Password');
                resetPasswordForm();
                modalCambiaPassword.show();
            });

            // 3. VERIFICA PASSWORD ATTUALE
            btnVerificaPassword.addEventListener('click', function() {
                console.log('🔍 Verifica password attuale...');

                const password = passwordAttuale.value.trim();

                if (!password) {
                    showError('errorPasswordAttuale', 'Inserisci la password attuale');
                    passwordAttuale.classList.add('is-invalid');
                    return;
                }

                // Disabilita pulsante durante verifica
                btnVerificaPassword.disabled = true;
                btnVerificaPassword.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifica...';

                // Chiamata API per verificare password
                fetch('/api/verify-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        current_password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('📊 Risposta verifica password:', data);

                    if (data.success) {
                        console.log('✅ Password verificata correttamente');
                        showStep2();
                    } else {
                        console.log('❌ Password non corretta');
                        showError('errorPasswordAttuale', 'Password non corretta');
                        passwordAttuale.classList.add('is-invalid');
                    }
                })
                .catch(error => {
                    console.error('❌ Errore verifica password:', error);
                    showError('errorPasswordAttuale', 'Errore durante la verifica. Riprova.');
                })
                .finally(() => {
                    // Riabilita pulsante
                    btnVerificaPassword.disabled = false;
                    btnVerificaPassword.innerHTML = '<i class="fas fa-check me-2"></i>Verifica Password';
                });
            });

            // 4. TORNA INDIETRO AL STEP 1
            btnTornaIndietro.addEventListener('click', function() {
                console.log('⬅️ Torna al step 1');
                showStep1();
            });

            // 5. SALVA NUOVA PASSWORD
            formCambiaPassword.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('💾 Salva nuova password...');

                const nuova = nuovaPassword.value.trim();
                const conferma = confermaNuovaPassword.value.trim();

                // Validazione
                if (!validateNewPassword(nuova, conferma)) {
                    return;
                }

                // Disabilita pulsante durante salvataggio
                btnSalvaNuovaPassword.disabled = true;
                btnSalvaNuovaPassword.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Salvataggio...';

                // Chiamata API per cambiare password
                fetch('/api/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        current_password: passwordAttuale.value,
                        new_password: nuova
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('📊 Risposta cambio password:', data);

                    if (data.success) {
                        console.log('✅ Password cambiata con successo');
                        showSuccessMessage();
                        setTimeout(() => {
                            modalCambiaPassword.hide();
                            resetPasswordForm();
                        }, 2000);
                    } else {
                        console.log('❌ Errore cambio password');
                        showError('errorConfermaNuovaPassword', data.message || 'Errore durante il cambio password');
                    }
                })
                .catch(error => {
                    console.error('❌ Errore cambio password:', error);
                    showError('errorConfermaNuovaPassword', 'Errore durante il salvataggio. Riprova.');
                })
                .finally(() => {
                    // Riabilita pulsante
                    btnSalvaNuovaPassword.disabled = false;
                    btnSalvaNuovaPassword.innerHTML = '<i class="fas fa-save me-2"></i>Salva Nuova Password';
                });
            });

            // 6. TOGGLE PASSWORD VISIBILITY
            togglePasswordAttuale.addEventListener('click', function() {
                togglePasswordVisibility(passwordAttuale, togglePasswordAttuale);
            });

            toggleNuovaPassword.addEventListener('click', function() {
                togglePasswordVisibility(nuovaPassword, toggleNuovaPassword);
            });

            toggleConfermaNuovaPassword.addEventListener('click', function() {
                togglePasswordVisibility(confermaNuovaPassword, toggleConfermaNuovaPassword);
            });

            // FUNZIONI HELPER
            function resetPasswordForm() {
                console.log('🔄 Reset form password');

                // Reset input
                passwordAttuale.value = '';
                nuovaPassword.value = '';
                confermaNuovaPassword.value = '';

                // Reset classi
                passwordAttuale.classList.remove('is-invalid', 'is-valid');
                nuovaPassword.classList.remove('is-invalid', 'is-valid');
                confermaNuovaPassword.classList.remove('is-invalid', 'is-valid');

                // Reset step
                showStep1();

                // Reset errori
                clearErrors();
            }

            function showStep1() {
                step1Verifica.style.display = 'block';
                step2Nuova.style.display = 'none';
                passwordAttuale.focus();
            }

            function showStep2() {
                step1Verifica.style.display = 'none';
                step2Nuova.style.display = 'block';
                passwordAttuale.classList.remove('is-invalid');
                passwordAttuale.classList.add('is-valid');
                nuovaPassword.focus();
            }

            // Configurazioni di sicurezza dinamiche
            let securityConfig = {
                password_min_length: 6,
                password_max_length: 128,
                password_uppercase: false,
                password_numbers: false,
                password_special: false
            };

            // Carica configurazioni dal server
            fetch('/api/security-config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        securityConfig = data.config;
                        console.log('🔧 Configurazioni sicurezza caricate:', securityConfig);
                    }
                })
                .catch(error => {
                    console.log('⚠️ Usando configurazioni predefinite per password');
                });

            function validatePasswordStrength(password) {
                if (password.length < securityConfig.password_min_length) {
                    return { valid: false, message: `Password troppo corta (minimo ${securityConfig.password_min_length} caratteri)` };
                }

                if (password.length > securityConfig.password_max_length) {
                    return { valid: false, message: `Password troppo lunga (massimo ${securityConfig.password_max_length} caratteri)` };
                }

                if (securityConfig.password_uppercase && !/[A-Z]/.test(password)) {
                    return { valid: false, message: 'Password deve contenere almeno una maiuscola' };
                }

                if (securityConfig.password_numbers && !/[0-9]/.test(password)) {
                    return { valid: false, message: 'Password deve contenere almeno un numero' };
                }

                if (securityConfig.password_special && !/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
                    return { valid: false, message: 'Password deve contenere almeno un carattere speciale' };
                }

                return { valid: true, message: 'Password valida' };
            }

            function validateNewPassword(nuova, conferma) {
                let valid = true;

                // Reset classi
                nuovaPassword.classList.remove('is-invalid', 'is-valid');
                confermaNuovaPassword.classList.remove('is-invalid', 'is-valid');
                clearErrors();

                // Validazione forza password usando configurazioni dinamiche
                const validation = validatePasswordStrength(nuova);
                if (!validation.valid) {
                    showError('errorConfermaNuovaPassword', validation.message);
                    nuovaPassword.classList.add('is-invalid');
                    valid = false;
                }

                // Validazione corrispondenza
                if (nuova !== conferma) {
                    showError('errorConfermaNuovaPassword', 'Le password non corrispondono');
                    confermaNuovaPassword.classList.add('is-invalid');
                    valid = false;
                }

                if (valid) {
                    nuovaPassword.classList.add('is-valid');
                    confermaNuovaPassword.classList.add('is-valid');
                }

                return valid;
            }

            function togglePasswordVisibility(input, button) {
                const icon = button.querySelector('i');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            function showError(elementId, message) {
                const errorElement = document.getElementById(elementId);
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                }
            }

            function clearErrors() {
                const errorElements = document.querySelectorAll('.invalid-feedback');
                errorElements.forEach(element => {
                    element.textContent = '';
                    element.style.display = 'none';
                });
            }

            function showSuccessMessage() {
                const step2 = document.getElementById('step2-nuova');
                step2.innerHTML = `
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>Password Cambiata con Successo!</h5>
                        <p class="mb-0">La tua password è stata aggiornata correttamente.</p>
                    </div>
                `;
            }

            console.log('🎉 Pagina profilo inizializzata con successo!');
        });

        // ===== GESTIONE PREFERENZE INTERFACCIA =====

        // Carica preferenze salvate
        async function loadInterfacePreferences() {
            // Prima carica dal localStorage per le preferenze non-tema
            const preferences = JSON.parse(localStorage.getItem('snip_interface_preferences') || '{}');

            // Applica preferenze non-tema dal localStorage
            if (preferences.language) {
                document.getElementById('interfaceLanguage').value = preferences.language;
            }
            if (preferences.animations !== undefined) {
                document.getElementById('enableAnimations').checked = preferences.animations;
            }
            if (preferences.sounds !== undefined) {
                document.getElementById('enableSounds').checked = preferences.sounds;
            }

            // Carica il tema dal database tramite API
            try {
                console.log('🔍 Caricamento tema utente dal database...');
                const response = await fetch('/api/user/theme', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    const userTheme = data.theme || 'maritime';
                    document.getElementById('interfaceTheme').value = userTheme;
                    console.log('✅ Tema utente caricato dal database:', userTheme);
                } else {
                    console.warn('⚠️ Errore caricamento tema dal database, uso fallback');
                    loadThemeFallback();
                }
            } catch (error) {
                console.error('❌ Errore connessione API tema:', error);
                loadThemeFallback();
            }

            // Funzione fallback per caricare il tema quando l'API non è disponibile
            function loadThemeFallback() {
                console.log('🔄 Usando fallback per caricamento tema...');

                // Prima prova dal localStorage
                const preferences = JSON.parse(localStorage.getItem('snip_interface_preferences') || '{}');
                if (preferences.theme) {
                    document.getElementById('interfaceTheme').value = preferences.theme;
                    console.log('🎨 Tema caricato dal localStorage (fallback):', preferences.theme);
                    return;
                }

                // Poi prova dal sistema globale SNIPThemes
                if (window.SNIPThemes && typeof window.SNIPThemes.current === 'function') {
                    const currentTheme = window.SNIPThemes.current();
                    const themeSelect = document.getElementById('interfaceTheme');
                    if (themeSelect) {
                        themeSelect.value = currentTheme;
                        console.log('🎨 Tema caricato dal sistema globale (fallback):', currentTheme);
                        return;
                    }
                }

                // Ultimo fallback: leggi tema dal body
                const bodyClasses = document.body.classList;
                let bodyTheme = 'maritime'; // default

                if (bodyClasses.contains('theme-dark')) {
                    bodyTheme = 'dark';
                } else if (bodyClasses.contains('theme-light')) {
                    bodyTheme = 'light';
                } else if (bodyClasses.contains('theme-maritime')) {
                    bodyTheme = 'maritime';
                }

                const themeSelect = document.getElementById('interfaceTheme');
                if (themeSelect) {
                    themeSelect.value = bodyTheme;
                    console.log('🎨 Tema caricato dal body (ultimo fallback):', bodyTheme);
                }
            }
        }

        // Salva preferenze
        async function saveInterfacePreferences() {
            const preferences = {
                theme: document.getElementById('interfaceTheme').value,
                language: document.getElementById('interfaceLanguage').value,
                animations: document.getElementById('enableAnimations').checked,
                sounds: document.getElementById('enableSounds').checked,
                savedAt: new Date().toISOString()
            };

            try {
                // Salva il tema nel database tramite API
                const response = await fetch('/api/user/theme', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ theme: preferences.theme })
                });

                if (!response.ok) {
                    throw new Error(`Errore HTTP: ${response.status}`);
                }

                const result = await response.json();
                console.log('✅ Tema salvato nel database:', result);

                // Salva anche nel localStorage per compatibilità
                localStorage.setItem('snip_interface_preferences', JSON.stringify(preferences));

                // Usa il sistema globale per applicare il tema
                if (window.SNIPThemes) {
                    window.SNIPThemes.change(preferences.theme);
                    window.SNIPThemes.toggleAnimations(preferences.animations);
                    console.log('🎨 Tema applicato globalmente:', preferences.theme);
                } else {
                    // Fallback se il sistema globale non è disponibile
                    applyTheme(preferences.theme);
                }

                // Mostra messaggio di successo
                showAlert('Preferenze interfaccia salvate con successo!', 'success');

                console.log('🎨 Preferenze interfaccia salvate:', preferences);

            } catch (error) {
                console.error('❌ Errore salvataggio tema:', error);
                showAlert('Errore nel salvataggio delle preferenze. Riprova.', 'error');
            }
        }

        // Ripristina preferenze default
        async function resetInterfacePreferences() {
            const defaultPreferences = {
                theme: 'maritime',
                language: 'it',
                animations: true,
                sounds: true
            };

            try {
                // Salva il tema default nel database tramite API
                const response = await fetch('/api/user/theme', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ theme: defaultPreferences.theme })
                });

                if (!response.ok) {
                    throw new Error(`Errore HTTP: ${response.status}`);
                }

                const result = await response.json();
                console.log('✅ Tema default salvato nel database:', result);

                // Aggiorna UI
                document.getElementById('interfaceTheme').value = defaultPreferences.theme;
                document.getElementById('interfaceLanguage').value = defaultPreferences.language;
                document.getElementById('enableAnimations').checked = defaultPreferences.animations;
                document.getElementById('enableSounds').checked = defaultPreferences.sounds;

                // Salva e applica con sistema globale
                localStorage.setItem('snip_interface_preferences', JSON.stringify(defaultPreferences));

                if (window.SNIPThemes) {
                    window.SNIPThemes.change(defaultPreferences.theme);
                    window.SNIPThemes.toggleAnimations(defaultPreferences.animations);
                    console.log('🔄 Tema ripristinato globalmente:', defaultPreferences.theme);
                } else {
                    applyTheme(defaultPreferences.theme);
                }

                showAlert('Preferenze interfaccia ripristinate ai valori default', 'info');

                console.log('🔄 Preferenze interfaccia ripristinate');

            } catch (error) {
                console.error('❌ Errore ripristino tema:', error);
                showAlert('Errore nel ripristino delle preferenze. Riprova.', 'error');
            }
        }

        // Funzione applyTheme RIMOSSA
        // Il tema viene ora gestito dal server e theme-manager.js
        // Non applichiamo più temi dinamicamente per evitare conflitti tra utenti
        function applyTheme(theme) {
            console.log('⚠️ applyTheme() deprecata - tema gestito dal server');
            console.log('💡 Il tema sarà applicato al prossimo caricamento pagina');
            // Non facciamo nulla per evitare conflitti tra utenti
        }

        // Carica preferenze all'avvio
        loadInterfacePreferences().catch(error => {
            console.error('❌ Errore caricamento preferenze:', error);
            loadThemeFallback();
        });

        // Event listeners per preferenze
        document.getElementById('btnSalvaPreferenze').addEventListener('click', function() {
            saveInterfacePreferences();
        });

        document.getElementById('btnRipristinaPreferenze').addEventListener('click', function() {
            if (confirm('Sei sicuro di voler ripristinare le preferenze ai valori default?')) {
                resetInterfacePreferences();
            }
        });

        // Applica tema quando cambia (immediatamente)
        document.getElementById('interfaceTheme').addEventListener('change', function() {
            if (window.SNIPThemes) {
                console.log('🎨 Cambio tema richiesto:', this.value);

                // Applica il tema immediatamente
                const success = window.SNIPThemes.change(this.value);

                if (success) {
                    // Mostra notifica di successo
                    if (typeof snipMessages !== 'undefined') {
                        const themeName = window.SNIPThemes.themes[this.value]?.name || this.value;
                        snipMessages.success('🎨 Tema Cambiato', `Tema "${themeName}" applicato con successo!`);
                    }

                    console.log('✅ Tema applicato con successo:', this.value);
                } else {
                    console.error('❌ Errore nell\'applicazione del tema');
                    if (typeof snipMessages !== 'undefined') {
                        snipMessages.error('❌ Errore Tema', 'Impossibile applicare il tema selezionato.');
                    }
                }
            } else {
                console.warn('SNIPThemes non disponibile');
                if (typeof snipMessages !== 'undefined') {
                    snipMessages.warning('⚠️ Sistema Non Disponibile', 'Sistema tema non disponibile. Ricarica la pagina e riprova.');
                } else {
                    alert('Sistema tema non disponibile. Ricarica la pagina e riprova.');
                }
            }
        });

        // Gestione animazioni in tempo reale
        document.getElementById('enableAnimations').addEventListener('change', function() {
            if (window.SNIPThemes) {
                window.SNIPThemes.toggleAnimations(this.checked);
                console.log('🎬 Animazioni:', this.checked ? 'abilitate' : 'disabilitate');
            } else {
                if (this.checked) {
                    document.body.classList.remove('no-animations');
                } else {
                    document.body.classList.add('no-animations');
                }
            }
        });

        // Funzione helper per mostrare alert
        function showAlert(message, type = 'info') {
            // Usa SNIP Messages se disponibile
            if (typeof snipMessages !== 'undefined') {
                switch(type) {
                    case 'success': return snipMessages.success('🎉 Successo!', message);
                    case 'error': return snipMessages.error('💥 Errore!', message);
                    case 'warning': return snipMessages.warning('⚡ Attenzione!', message);
                    case 'info': return snipMessages.info('💫 Informazione', message);
                    default: return snipMessages.info('💫 Informazione', message);
                }
            }

            // Fallback: crea alert Bootstrap
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        console.log('🎨 Sistema preferenze interfaccia inizializzato');
    </script>
</body>
</html>
