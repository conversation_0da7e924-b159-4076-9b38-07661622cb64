<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚢 SNIP - Registrazione</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background:
                linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%),
                url('/static/images/cargo-ship.jpg') center/cover no-repeat fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
            padding: 20px 0;
        }

        /* Sfondo animato */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat bottom;
            background-size: cover;
            animation: wave 6s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(-50px); }
        }

        /* Container principale */
        .register-container {
            position: relative;
            z-index: 10;
            max-width: 520px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            padding: 40px 35px;
            animation: slideUp 0.8s ease-out;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header */
        .header-section {
            text-align: center;
            margin-bottom: 35px;
        }

        .logo-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .logo-icon {
            font-size: 2.5rem;
            color: white;
        }

        .app-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            font-size: 0.95rem;
            color: #718096;
            font-weight: 400;
            margin-bottom: 0;
        }

        /* Form styling */
        .form-group {
            position: relative;
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-control, .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px 12px 45px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-control:focus + .input-icon,
        .form-select:focus + .input-icon {
            color: #667eea;
        }

        /* Pulsante registrazione */
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 14px 24px;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-register:hover::before {
            left: 100%;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Alert personalizzato */
        .custom-alert {
            background: rgba(248, 113, 113, 0.1);
            border: 1px solid rgba(248, 113, 113, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 20px;
            color: #dc2626;
            font-size: 0.9rem;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Link login */
        .login-link {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .register-container {
                margin: 20px;
                padding: 30px 25px;
            }

            .app-title {
                font-size: 1.5rem;
            }

            .logo-container {
                width: 70px;
                height: 70px;
            }

            .logo-icon {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Container principale -->
    <div class="register-container">
        <!-- Header con logo -->
        <div class="header-section">
            <div class="logo-container">
                <i class="fas fa-user-plus logo-icon"></i>
            </div>
            <h1 class="app-title">Registrazione</h1>
            <p class="app-subtitle">Crea il tuo account SNIP</p>
        </div>

        <!-- Alert errori -->
        {% if error %}
        <div class="custom-alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
        </div>
        {% endif %}

        <!-- Form di registrazione -->
        <form method="post" action="/register" id="registerForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="nome" class="form-label">
                            <i class="fas fa-user me-2"></i>Nome
                        </label>
                        <input type="text" class="form-control" id="nome" name="nome" required
                               placeholder="Il tuo nome">
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="cognome" class="form-label">
                            <i class="fas fa-user me-2"></i>Cognome
                        </label>
                        <input type="text" class="form-control" id="cognome" name="cognome" required
                               placeholder="Il tuo cognome">
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope me-2"></i>Indirizzo Email
                </label>
                <input type="email" class="form-control" id="email" name="email" required
                       placeholder="<EMAIL>">
                <i class="fas fa-envelope input-icon"></i>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <input type="password" class="form-control" id="password" name="password" required
                       placeholder="••••••••">
                <i class="fas fa-lock input-icon"></i>
                <small id="password-help" class="form-text text-muted mt-1">Minimo 6 caratteri</small>
            </div>

            <div class="form-group">
                <label for="reparto" class="form-label">
                    <i class="fas fa-building me-2"></i>Reparto di Appartenenza
                </label>
                <select class="form-select" id="reparto" name="reparto" required>
                    <option value="" disabled selected>Seleziona il tuo reparto</option>
                    <option value="OPERATIVO">🚢 OPERATIVO</option>
                    <option value="AMMINISTRAZIONE">📋 AMMINISTRAZIONE</option>
                    <option value="SHORTSEA">🌊 SHORTSEA</option>
                    <option value="CONTABILITA">💰 CONTABILITÀ</option>
                </select>
                <i class="fas fa-building input-icon"></i>
            </div>



            <button type="submit" class="btn btn-register">
                <i class="fas fa-user-plus me-2"></i>
                Crea Account
            </button>
        </form>

        <!-- Link login -->
        <div class="login-link">
            <a href="/login">
                <i class="fas fa-sign-in-alt me-2"></i>
                Hai già un account? Accedi qui
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/alerts.js"></script>

    <script>
        // Animazione form
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const inputs = form.querySelectorAll('.form-control, .form-select');

            // Animazione focus input
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // Carica configurazioni di sicurezza e valida password
            let securityConfig = {
                password_min_length: 6,
                password_max_length: 128,
                password_uppercase: false,
                password_numbers: false,
                password_special: false
            };

            // Carica configurazioni dal server
            fetch('/api/security-config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        securityConfig = data.config;
                        updatePasswordHelp();
                    }
                })
                .catch(error => {
                    console.log('Usando configurazioni predefinite per password');
                });

            function updatePasswordHelp() {
                const helpText = document.getElementById('password-help');
                if (helpText) {
                    let requirements = [`Minimo ${securityConfig.password_min_length} caratteri`];

                    if (securityConfig.password_uppercase) {
                        requirements.push('almeno una maiuscola');
                    }
                    if (securityConfig.password_numbers) {
                        requirements.push('almeno un numero');
                    }
                    if (securityConfig.password_special) {
                        requirements.push('almeno un carattere speciale');
                    }

                    helpText.textContent = 'Password deve contenere: ' + requirements.join(', ');
                }
            }

            function validatePassword(password) {
                if (password.length < securityConfig.password_min_length) {
                    return { valid: false, message: `Password troppo corta (minimo ${securityConfig.password_min_length} caratteri)` };
                }

                if (password.length > securityConfig.password_max_length) {
                    return { valid: false, message: `Password troppo lunga (massimo ${securityConfig.password_max_length} caratteri)` };
                }

                if (securityConfig.password_uppercase && !/[A-Z]/.test(password)) {
                    return { valid: false, message: 'Password deve contenere almeno una maiuscola' };
                }

                if (securityConfig.password_numbers && !/[0-9]/.test(password)) {
                    return { valid: false, message: 'Password deve contenere almeno un numero' };
                }

                if (securityConfig.password_special && !/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
                    return { valid: false, message: 'Password deve contenere almeno un carattere speciale' };
                }

                return { valid: true, message: 'Password valida' };
            }

            // Validazione password dinamica
            const passwordInput = document.getElementById('password');
            passwordInput.addEventListener('input', function() {
                const password = this.value;

                if (password.length > 0) {
                    const validation = validatePassword(password);

                    if (validation.valid) {
                        this.style.borderColor = '#10b981';
                        this.title = validation.message;
                    } else {
                        this.style.borderColor = '#ef4444';
                        this.title = validation.message;
                    }
                } else {
                    this.style.borderColor = '';
                    this.title = '';
                }
            });

            // Animazione submit
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('.btn-register');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creazione account...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html>