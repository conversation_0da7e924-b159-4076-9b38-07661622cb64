<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Verifica Sicurezza - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .security-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 500px;
            width: 100%;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .security-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .code-input {
            font-size: 2rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: bold;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            padding: 1rem;
            margin: 1.5rem 0;
            transition: all 0.3s ease;
        }
        
        .code-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            outline: none;
        }
        
        .verify-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .status-info {
            background: rgba(13, 202, 240, 0.1);
            border: 1px solid rgba(13, 202, 240, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .timer {
            font-size: 1.2rem;
            font-weight: bold;
            color: #dc3545;
        }
        
        .email-info {
            background: rgba(25, 135, 84, 0.1);
            border: 1px solid rgba(25, 135, 84, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin: 1rem 0;
        }
        
        .blocked-warning {
            background: rgba(220, 53, 69, 0.1);
            border: 2px solid rgba(220, 53, 69, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            color: #721c24;
        }
        
        .success-message {
            background: rgba(25, 135, 84, 0.1);
            border: 2px solid rgba(25, 135, 84, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            color: #0f5132;
        }
    </style>
</head>
<body>
    <div class="security-card">
        <div class="security-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h2 class="mb-3">🔐 Verifica Sicurezza SNIP</h2>
        <p class="text-muted mb-4">Il servizio richiede la tua autorizzazione per continuare</p>
        
        {% if status.startup_blocked and not status.is_verified %}
            <div class="blocked-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Servizio Bloccato</strong><br>
                Il servizio SNIP è attualmente bloccato e richiede la verifica del codice di sicurezza.
            </div>
        {% endif %}
        
        {% if status.is_verified %}
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <strong>Verifica Completata</strong><br>
                Il codice è stato verificato con successo. Il servizio è ora attivo.
            </div>
        {% else %}
            {% if status.has_active_code %}
                <div class="status-info">
                    <i class="fas fa-clock"></i>
                    <strong>Codice Attivo</strong><br>
                    <div class="timer" id="countdown">
                        Tempo rimanente: <span id="time-remaining">Caricamento...</span>
                    </div>
                </div>
                
                <div class="email-info">
                    <i class="fas fa-envelope"></i>
                    <strong>Email inviata a:</strong> {{ admin_email }}<br>
                    <small>Controlla la tua casella di posta per il codice di 6 cifre</small>
                </div>
                
                <form id="verificationForm">
                    <input 
                        type="text" 
                        id="securityCode" 
                        class="form-control code-input" 
                        placeholder="000000"
                        maxlength="6"
                        pattern="[0-9]{6}"
                        required
                        autocomplete="off"
                    >
                    
                    <button type="submit" class="verify-btn">
                        <i class="fas fa-key"></i> Verifica Codice
                    </button>
                </form>
            {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    Nessun codice di sicurezza attivo al momento.
                </div>
                
                <button id="initiateBtn" class="verify-btn">
                    <i class="fas fa-paper-plane"></i> Richiedi Nuovo Codice
                </button>
            {% endif %}
        {% endif %}
        
        <div id="messageArea"></div>
        
        <hr class="my-4">
        <small class="text-muted">
            <i class="fas fa-info-circle"></i>
            Sistema di Sicurezza SNIP - Michele Autuori Srl
        </small>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let countdownInterval;
        
        // Inizializza il countdown se c'è un codice attivo
        {% if status.has_active_code and status.time_remaining > 0 %}
            let timeRemaining = {{ status.time_remaining }};
            updateCountdown();
        {% endif %}
        
        function updateCountdown() {
            if (timeRemaining <= 0) {
                document.getElementById('time-remaining').textContent = 'Scaduto';
                document.getElementById('countdown').style.color = '#dc3545';
                clearInterval(countdownInterval);
                location.reload(); // Ricarica la pagina quando scade
                return;
            }
            
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = Math.floor(timeRemaining % 60);
            document.getElementById('time-remaining').textContent = 
                `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            timeRemaining--;
        }
        
        // Avvia il countdown
        if (typeof timeRemaining !== 'undefined' && timeRemaining > 0) {
            countdownInterval = setInterval(updateCountdown, 1000);
        }
        
        // Gestione form di verifica
        document.getElementById('verificationForm')?.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const code = document.getElementById('securityCode').value.trim();
            const messageArea = document.getElementById('messageArea');
            
            if (code.length !== 6 || !/^\d{6}$/.test(code)) {
                showMessage('Il codice deve essere di 6 cifre', 'danger');
                return;
            }
            
            try {
                const response = await fetch('/security/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ code: code })
                });
                
                const result = await response.json();
                
                if (result.success && result.verified) {
                    showMessage('✅ Codice verificato con successo! Servizio sbloccato.', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showMessage('❌ ' + result.message, 'danger');
                    document.getElementById('securityCode').value = '';
                }
            } catch (error) {
                showMessage('❌ Errore di comunicazione con il server', 'danger');
            }
        });
        
        // Gestione richiesta nuovo codice
        document.getElementById('initiateBtn')?.addEventListener('click', async function() {
            try {
                const response = await fetch('/security/initiate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('📧 ' + result.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showMessage('❌ ' + result.message, 'danger');
                }
            } catch (error) {
                showMessage('❌ Errore di comunicazione con il server', 'danger');
            }
        });
        
        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // Auto-focus sul campo codice
        document.getElementById('securityCode')?.focus();
        
        // Formattazione automatica del codice (solo numeri)
        document.getElementById('securityCode')?.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '');
        });
    </script>
</body>
</html>
