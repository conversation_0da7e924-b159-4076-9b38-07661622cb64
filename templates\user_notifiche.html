<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifiche {{ current_user.reparto.value }} - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .notification-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .notification-card.info { border-left-color: #17a2b8; }
        .notification-card.warning { border-left-color: #ffc107; }
        .notification-card.success { border-left-color: #28a745; }
        .notification-card.error { border-left-color: #dc3545; }
        .notification-card.urgent { border-left-color: #6f42c1; }
        
        .notification-card.unread {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-left-width: 8px;
        }
        
        .notification-card.read {
            opacity: 0.8;
            border-left-width: 3px;
        }
        
        .badge-priority {
            font-size: 0.8em;
            padding: 5px 10px;
            border-radius: 15px;
        }
        
        .priority-1 { background-color: #6c757d; }
        .priority-2 { background-color: #17a2b8; }
        .priority-3 { background-color: #ffc107; color: #000; }
        .priority-4 { background-color: #dc3545; }
        
        .user-menu {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .notification-stats {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .mark-read-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        
        .mark-read-btn:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Menu utente -->
    <div class="user-menu">
        <div class="dropdown">
            <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-user"></i> {{ current_user.Cognome }}
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="/dashboard/{{ current_user.reparto.value.lower() }}"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a class="dropdown-item" href="/notifiche"><i class="fas fa-bell"></i> Notifiche</a></li>
                {% if current_user.ruolo.value in ['ADMIN', 'SUPER_ADMIN'] %}
                <li><a class="dropdown-item" href="/admin/notifiche"><i class="fas fa-cog"></i> Gestione Notifiche</a></li>
                {% endif %}
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-bell"></i> Notifiche Reparto {{ current_user.reparto.value }}</h1>
                <p class="mb-0">Tutte le comunicazioni per il tuo reparto</p>
            </div>

            <!-- Statistiche notifiche -->
            <div class="notification-stats">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="totalNotifications">0</div>
                            <div class="text-muted">Totali</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number text-warning" id="unreadNotifications">0</div>
                            <div class="text-muted">Non Lette</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number text-success" id="readNotifications">0</div>
                            <div class="text-muted">Lette</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <button class="btn btn-primary" onclick="markAllAsRead()">
                                <i class="fas fa-check-double"></i> Segna Tutte Come Lette
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading -->
            <div class="loading" id="loadingNotifications">
                <i class="fas fa-spinner fa-spin fa-3x text-primary"></i>
                <p class="mt-3">Caricamento notifiche...</p>
            </div>

            <!-- Lista notifiche -->
            <div id="notificationsList"></div>

            <!-- Messaggio nessuna notifica -->
            <div id="noNotifications" style="display: none;" class="text-center py-5">
                <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">Nessuna notifica</h4>
                <p class="text-muted">Non ci sono notifiche per il tuo reparto al momento.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/messages.js"></script>
    <script>
        let notifications = [];
        const userReparto = '{{ current_user.reparto.value }}';

        // Carica notifiche del reparto
        async function loadNotifications() {
            document.getElementById('loadingNotifications').style.display = 'block';
            
            try {
                const response = await fetch(`/api/notifications/department/${userReparto}`);
                const data = await response.json();
                
                if (data.success) {
                    notifications = data.notifications;
                    displayNotifications();
                    updateStats();
                } else {
                    console.error('Errore caricamento notifiche:', data.message);
                    showNoNotifications();
                }
            } catch (error) {
                console.error('Errore:', error);
                showNoNotifications();
            } finally {
                document.getElementById('loadingNotifications').style.display = 'none';
            }
        }

        // Visualizza notifiche
        function displayNotifications() {
            const container = document.getElementById('notificationsList');
            
            if (notifications.length === 0) {
                showNoNotifications();
                return;
            }
            
            document.getElementById('noNotifications').style.display = 'none';
            
            container.innerHTML = notifications.map(notif => `
                <div class="notification-card ${notif.type.toLowerCase()} ${notif.is_read ? 'read' : 'unread'}" 
                     onclick="markAsRead(${notif.id})" data-id="${notif.id}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h5 class="mb-0 me-3">${getTypeIcon(notif.type)} ${notif.title}</h5>
                                ${!notif.is_read ? '<span class="badge bg-warning text-dark">NUOVO</span>' : ''}
                            </div>
                            <p class="mb-3">${notif.message}</p>
                            <div class="d-flex gap-2 flex-wrap">
                                <span class="badge bg-secondary">${userReparto}</span>
                                <span class="badge badge-priority priority-${notif.priority}">Priorità ${notif.priority}</span>
                                <span class="badge bg-light text-dark">${formatDate(notif.created_at)}</span>
                                ${notif.expires_at ? `<span class="badge bg-warning text-dark">Scade: ${formatDate(notif.expires_at)}</span>` : ''}
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">da ${notif.creator_name}</small><br>
                            ${!notif.is_read ? `<button class="btn mark-read-btn mt-2" onclick="event.stopPropagation(); markAsRead(${notif.id})">
                                <i class="fas fa-check"></i> Segna come letta
                            </button>` : '<span class="text-success"><i class="fas fa-check-circle"></i> Letta</span>'}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Mostra messaggio nessuna notifica
        function showNoNotifications() {
            document.getElementById('notificationsList').innerHTML = '';
            document.getElementById('noNotifications').style.display = 'block';
        }

        // Aggiorna statistiche
        function updateStats() {
            const total = notifications.length;
            const unread = notifications.filter(n => !n.is_read).length;
            const read = total - unread;
            
            document.getElementById('totalNotifications').textContent = total;
            document.getElementById('unreadNotifications').textContent = unread;
            document.getElementById('readNotifications').textContent = read;
        }

        // Segna notifica come letta
        async function markAsRead(notificationId) {
            try {
                const response = await fetch(`/api/notifications/mark-read/${notificationId}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Aggiorna lo stato locale
                    const notification = notifications.find(n => n.id === notificationId);
                    if (notification) {
                        notification.is_read = true;
                    }
                    
                    // Ricarica la visualizzazione
                    displayNotifications();
                    updateStats();
                } else {
                    console.error('Errore segnalazione lettura:', data.message);
                }
            } catch (error) {
                console.error('Errore:', error);
            }
        }

        // Segna tutte come lette
        async function markAllAsRead() {
            const unreadNotifications = notifications.filter(n => !n.is_read);

            if (unreadNotifications.length === 0) {
                if (typeof snipMessages !== 'undefined') {
                    snipMessages.info('📋 Informazione', 'Tutte le notifiche sono già state lette!');
                } else {
                    alert('📋 Tutte le notifiche sono già state lette!');
                }
                return;
            }

            // Usa SNIP Messages per la conferma se disponibile
            if (typeof snipMessages !== 'undefined') {
                snipMessages.confirm('🤔 Conferma Azione',
                    `Segnare tutte le ${unreadNotifications.length} notifiche non lette come lette?`,
                    async (result) => {
                        if (result) {
                            for (const notif of unreadNotifications) {
                                await markAsRead(notif.id);
                            }
                            snipMessages.success('✅ Completato!', 'Tutte le notifiche sono state segnate come lette!');
                        }
                    }
                );
            } else {
                // Fallback per browser senza SNIP Messages
                if (!confirm(`Segnare tutte le ${unreadNotifications.length} notifiche non lette come lette?`)) {
                    return;
                }

                for (const notif of unreadNotifications) {
                    await markAsRead(notif.id);
                }

                alert('✅ Tutte le notifiche sono state segnate come lette!');
            }
        }

        // Icone per tipo
        function getTypeIcon(type) {
            const icons = {
                'INFO': '📋',
                'WARNING': '⚠️',
                'SUCCESS': '✅',
                'ERROR': '❌',
                'URGENT': '🚨'
            };
            return icons[type] || '📋';
        }

        // Formatta data
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('it-IT', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Carica notifiche all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            
            // Ricarica ogni 2 minuti per nuove notifiche (ridotto da 30 secondi)
            setInterval(loadNotifications, 120000);
        });
    </script>
</body>
</html>
