/* ===== FIX CONTRASTI TEMA MARITTIMO GLOBALI SNIP ===== */

/* ===== OVERRIDE GLOBALE AGGRESSIVO ===== */

/* FORZA tutti i testi su card/sfondi chiari ad essere scuri */
body.theme-maritime .card,
body.theme-maritime .card *:not(.btn):not(.badge):not(.navbar *):not([style*="background: linear-gradient"]),
body.theme-maritime .bg-white,
body.theme-maritime .bg-white *:not(.btn):not(.badge):not(.navbar *):not([style*="background: linear-gradient"]),
body.theme-maritime .bg-light,
body.theme-maritime .bg-light *:not(.btn):not(.badge):not(.navbar *):not([style*="background: linear-gradient"]) {
    color: #212529 !important;
}

/* ===== FIX AGGRESSIVI PER TUTTI GLI ELEMENTI PROBLEMATICI ===== */

/* Fix specifici per classi Bootstrap problematiche */
body.theme-maritime .card .text-muted,
body.theme-maritime .bg-white .text-muted,
body.theme-maritime .bg-light .text-muted {
    color: #6c757d !important;
    font-weight: 600 !important;
}

body.theme-maritime .card .text-success,
body.theme-maritime .bg-white .text-success,
body.theme-maritime .bg-light .text-success {
    color: #198754 !important;
    font-weight: 700 !important;
}

body.theme-maritime .card .text-warning,
body.theme-maritime .bg-white .text-warning,
body.theme-maritime .bg-light .text-warning {
    color: #fd7e14 !important;
    font-weight: 700 !important;
}

body.theme-maritime .card .text-info,
body.theme-maritime .bg-white .text-info,
body.theme-maritime .bg-light .text-info {
    color: #0dcaf0 !important;
    font-weight: 700 !important;
}

body.theme-maritime .card .text-danger,
body.theme-maritime .bg-white .text-danger,
body.theme-maritime .bg-light .text-danger {
    color: #dc3545 !important;
    font-weight: 700 !important;
}

/* ===== FIX TESTI PROBLEMATICI ===== */

/* Fix testi bianchi inline - ADATTA AL CONTESTO */
body.theme-maritime .card [style*="color: white"],
body.theme-maritime .card [style*="color:#fff"],
body.theme-maritime .card [style*="color: #ffffff"],
body.theme-maritime .bg-white [style*="color: white"],
body.theme-maritime .bg-light [style*="color: white"],
body.theme-maritime .form-control [style*="color: white"] {
    color: #212529 !important; /* Scuro su sfondi chiari */
    text-shadow: none !important;
}

/* Fix testi bianchi su sfondi scuri (navbar, etc) */
body.theme-maritime .navbar [style*="color: white"],
body.theme-maritime [style*="background: linear-gradient"] [style*="color: white"] {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.7) !important;
}

/* Fix classi Bootstrap problematiche - CONTESTO SPECIFICO */
/* Testi bianchi su sfondi chiari (card, form, etc) */
body.theme-maritime .card .text-white,
body.theme-maritime .bg-white .text-white,
body.theme-maritime .bg-light .text-white,
body.theme-maritime .form-control .text-white,
body.theme-maritime .table .text-white {
    color: #212529 !important; /* Scuro su sfondi chiari */
    text-shadow: none !important;
}

/* Testi bianchi su sfondi scuri (navbar, gradienti) */
body.theme-maritime .navbar .text-white,
body.theme-maritime [style*="background: linear-gradient"] .text-white,
body.theme-maritime [style*="background-color: #1e3c72"] .text-white,
body.theme-maritime [style*="background-color: #2a5298"] .text-white {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.7) !important;
}

body.theme-maritime .text-light {
    color: #f8f9fa !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

body.theme-maritime .text-muted {
    color: #adb5bd !important;
    font-weight: 500 !important;
}

body.theme-maritime .text-secondary {
    color: #6c757d !important;
    font-weight: 500 !important;
}

/* Fix testi blu che potrebbero confondersi con sfondo marittimo */
body.theme-maritime .text-primary {
    color: #ffd700 !important; /* Oro per contrasto */
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

body.theme-maritime .text-info {
    color: #17a2b8 !important; /* Ciano più scuro */
    font-weight: 600 !important;
}

/* ===== FIX SFONDI PROBLEMATICI ===== */

body.theme-maritime .bg-light {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

body.theme-maritime .bg-white {
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #212529 !important;
}

body.theme-maritime .bg-primary {
    background-color: #1e3a8a !important; /* Blu navy più scuro */
    color: #ffffff !important;
}

/* ===== FIX FORM CONTROLS ===== */

/* Fix placeholder */
body.theme-maritime .form-control::placeholder,
body.theme-maritime .form-select::placeholder,
body.theme-maritime input::placeholder,
body.theme-maritime textarea::placeholder {
    color: #adb5bd !important;
    opacity: 0.8 !important;
}

/* Fix form controls generali */
body.theme-maritime .form-control,
body.theme-maritime .form-select {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 215, 0, 0.5) !important;
    color: #212529 !important;
}

body.theme-maritime .form-control:focus,
body.theme-maritime .form-select:focus {
    background-color: rgba(255, 255, 255, 1) !important;
    border-color: #ffd700 !important;
    color: #212529 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
}

/* ===== FIX SPECIFICI PER INPUT DATE E PULSANTI ===== */

/* Fix per input date nel tema marittimo - MASSIMA VISIBILITÀ */
body.theme-maritime .form-control[type="date"],
body.theme-maritime .form-control[type="datetime-local"],
body.theme-maritime input[type="date"],
body.theme-maritime input[type="datetime-local"] {
    background-color: #ffffff !important;
    color: #1e3a8a !important;
    border: 3px solid #1e3a8a !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
}

body.theme-maritime .form-control[type="date"]:focus,
body.theme-maritime .form-control[type="datetime-local"]:focus,
body.theme-maritime input[type="date"]:focus,
body.theme-maritime input[type="datetime-local"]:focus {
    background-color: #ffffff !important;
    color: #1e3a8a !important;
    border-color: #ffd700 !important;
    box-shadow: 0 0 0 0.3rem rgba(255, 215, 0, 0.4) !important;
    outline: none !important;
}

/* Fix per pulsanti outline nel tema marittimo */
body.theme-maritime .btn-outline-secondary {
    color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 700 !important;
    border-width: 2px !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
}

body.theme-maritime .btn-outline-secondary:hover {
    color: #ffffff !important;
    background-color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.3) !important;
}

body.theme-maritime .btn-outline-primary {
    color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 700 !important;
    border-width: 2px !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
}

body.theme-maritime .btn-outline-primary:hover {
    color: #ffffff !important;
    background-color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.3) !important;
}

/* Fix per range input nel tema marittimo */
body.theme-maritime .form-range {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

body.theme-maritime .form-range::-webkit-slider-thumb {
    background-color: #ffd700 !important;
    border: 2px solid #1e3a8a !important;
}

body.theme-maritime .form-range::-moz-range-thumb {
    background-color: #ffd700 !important;
    border: 2px solid #1e3a8a !important;
}

/* ===== FIX SPECIFICI PAGINA SOF ARCHIVIATI ===== */

/* Fix per tutti i testi nella pagina SOF Archiviati */
body.theme-maritime .container-fluid h1,
body.theme-maritime .container-fluid h2,
body.theme-maritime .container-fluid h3,
body.theme-maritime .container-fluid h4,
body.theme-maritime .container-fluid h5,
body.theme-maritime .container-fluid h6 {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
    font-weight: 700 !important;
}

/* Fix per paragrafi e testi generali */
body.theme-maritime .container-fluid p,
body.theme-maritime .container-fluid span,
body.theme-maritime .container-fluid div:not(.card):not(.btn) {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.7) !important;
}

/* Fix per testi piccoli e descrizioni */
body.theme-maritime .container-fluid .small,
body.theme-maritime .container-fluid .text-sm,
body.theme-maritime .container-fluid .fs-6 {
    color: #f8f9fa !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;
    font-weight: 500 !important;
}

/* Fix per label e etichette */
body.theme-maritime .container-fluid label,
body.theme-maritime .container-fluid .form-label {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.8) !important;
    font-weight: 600 !important;
}

/* Fix per link */
body.theme-maritime .container-fluid a:not(.btn) {
    color: #ffd700 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;
    font-weight: 600 !important;
}

body.theme-maritime .container-fluid a:not(.btn):hover {
    color: #ffed4e !important;
    text-decoration: underline !important;
}

/* Fix per icone */
body.theme-maritime .container-fluid i,
body.theme-maritime .container-fluid .fas,
body.theme-maritime .container-fluid .far,
body.theme-maritime .container-fluid .fab {
    color: #ffd700 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;
}

/* Fix per badge e tag */
body.theme-maritime .container-fluid .badge:not(.bg-success):not(.bg-danger):not(.bg-warning):not(.bg-info) {
    background-color: rgba(30, 58, 138, 0.9) !important;
    color: #ffd700 !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* Fix per statistiche e contatori */
body.theme-maritime .container-fluid .display-1,
body.theme-maritime .container-fluid .display-2,
body.theme-maritime .container-fluid .display-3,
body.theme-maritime .container-fluid .display-4,
body.theme-maritime .container-fluid .display-5,
body.theme-maritime .container-fluid .display-6 {
    color: #ffffff !important;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.9) !important;
    font-weight: 800 !important;
}

/* Fix per testi in grassetto */
body.theme-maritime .container-fluid .fw-bold,
body.theme-maritime .container-fluid .font-weight-bold,
body.theme-maritime .container-fluid strong,
body.theme-maritime .container-fluid b {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.8) !important;
    font-weight: 700 !important;
}

/* ===== FIX MODAL E POPUP TEMA MARITTIMO MIGLIORATO ===== */

/* Fix per modal header con gradiente marittimo migliorato */
body.theme-maritime .modal-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e40af 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
    position: relative !important;
}

/* Effetto glassmorphism per modal header */
body.theme-maritime .modal-header::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%) !important;
    backdrop-filter: blur(10px) !important;
    pointer-events: none !important;
}

body.theme-maritime .modal-header .modal-title {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
    font-weight: 700 !important;
    position: relative !important;
    z-index: 2 !important;
}

body.theme-maritime .modal-header .modal-title i {
    color: #ffd700 !important;
    text-shadow: 0 0 10px rgba(255,215,0,0.6) !important;
    margin-right: 8px !important;
}

body.theme-maritime .modal-header .btn-close {
    filter: invert(1) brightness(1.2) !important;
    opacity: 0.9 !important;
    position: relative !important;
    z-index: 2 !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .modal-header .btn-close:hover {
    opacity: 1 !important;
    transform: scale(1.1) !important;
    filter: invert(1) brightness(1.5) drop-shadow(0 0 5px rgba(255,215,0,0.8)) !important;
}

/* Fix per modal body con glassmorphism */
body.theme-maritime .modal-body {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%) !important;
    color: #212529 !important;
    backdrop-filter: blur(10px) !important;
    position: relative !important;
}

/* Effetto glassmorphism per modal body */
body.theme-maritime .modal-body::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(30,60,114,0.05) 0%, rgba(42,82,152,0.05) 100%) !important;
    pointer-events: none !important;
}

body.theme-maritime .modal-body > * {
    position: relative !important;
    z-index: 1 !important;
}

body.theme-maritime .modal-body h1,
body.theme-maritime .modal-body h2,
body.theme-maritime .modal-body h3,
body.theme-maritime .modal-body h4,
body.theme-maritime .modal-body h5,
body.theme-maritime .modal-body h6 {
    color: #1e3c72 !important;
    text-shadow: none !important;
    font-weight: 700 !important;
    margin-bottom: 1rem !important;
}

body.theme-maritime .modal-body p,
body.theme-maritime .modal-body span,
body.theme-maritime .modal-body div:not(.btn):not(.badge) {
    color: #212529 !important;
    text-shadow: none !important;
    font-weight: 500 !important;
}

/* Fix per tab navigation nel modal */
body.theme-maritime .modal-body .nav-tabs .nav-link {
    color: #1e3a8a !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: #1e3a8a !important;
    font-weight: 600 !important;
}

body.theme-maritime .modal-body .nav-tabs .nav-link:hover {
    color: #1e3a8a !important;
    background-color: #ffd700 !important;
    border-color: #1e3a8a !important;
}

body.theme-maritime .modal-body .nav-tabs .nav-link.active {
    color: #1e3a8a !important;
    background-color: #ffd700 !important;
    border-color: #1e3a8a !important;
    font-weight: 700 !important;
}

/* Fix per tabelle nel modal */
body.theme-maritime .modal-body .table {
    color: #212529 !important;
    background-color: transparent !important;
}

body.theme-maritime .modal-body .table thead th {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #ffffff !important;
    border-color: rgba(255, 215, 0, 0.3) !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

body.theme-maritime .modal-body .table tbody td {
    color: #212529 !important;
    border-color: rgba(30, 58, 138, 0.1) !important;
    font-weight: 500 !important;
}

/* Fix per alert nel modal */
body.theme-maritime .modal-body .alert {
    color: #212529 !important;
    font-weight: 500 !important;
}

body.theme-maritime .modal-body .alert-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-color: #17a2b8 !important;
    color: #0c5460 !important;
}

/* ===== FIX CARD SPECIFICHE SOF ARCHIVIATI ===== */

/* Fix per card file archiviati */
body.theme-maritime .card.file-card {
    background-color: rgba(255, 255, 255, 0.98) !important;
    border: 2px solid rgba(30, 58, 138, 0.3) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
}

body.theme-maritime .card.file-card .card-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #ffffff !important;
    border-bottom: 2px solid #ffd700 !important;
}

body.theme-maritime .card.file-card .card-header h5,
body.theme-maritime .card.file-card .card-header h6 {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
    font-weight: 700 !important;
}

body.theme-maritime .card.file-card .card-body {
    background-color: rgba(255, 255, 255, 0.98) !important;
    color: #212529 !important;
}

body.theme-maritime .card.file-card .card-body * {
    color: #212529 !important;
    text-shadow: none !important;
}

/* ===== FIX ELEMENTI DISABILITATI ===== */

body.theme-maritime .disabled,
body.theme-maritime :disabled,
body.theme-maritime .btn:disabled {
    color: #6c757d !important;
    opacity: 0.6 !important;
    background-color: rgba(108, 117, 125, 0.2) !important;
}

/* ===== FIX GRADIENTI PROBLEMATICI ===== */

/* Fix generale per tutti i gradienti */
body.theme-maritime .card[style*="linear-gradient"] .card-body,
body.theme-maritime .card[style*="linear-gradient"] .card-body *,
body.theme-maritime .card[style*="linear-gradient"] .card-header,
body.theme-maritime .card[style*="linear-gradient"] .card-header * {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* Fix specifici per gradienti comuni */
body.theme-maritime [style*="background: linear-gradient"] {
    color: #ffffff !important;
}

body.theme-maritime [style*="background: linear-gradient"] * {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* ===== FIX NAVBAR ===== */

body.theme-maritime .navbar .nav-link {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

body.theme-maritime .navbar .nav-link:hover {
    color: #ffd700 !important;
}

body.theme-maritime .navbar-brand {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* ===== FIX DROPDOWN ===== */

body.theme-maritime .dropdown-menu {
    background-color: rgba(30, 58, 138, 0.95) !important;
    border-color: rgba(255, 215, 0, 0.5) !important;
}

body.theme-maritime .dropdown-item {
    color: #ffffff !important;
}

body.theme-maritime .dropdown-item:hover {
    background-color: rgba(255, 215, 0, 0.2) !important;
    color: #ffd700 !important;
}

/* ===== FIX TABELLE TEMA MARITTIMO ===== */

/* Tabella generale - su card bianche */
body.theme-maritime .card .table {
    color: #212529 !important;
    background-color: transparent !important;
}

/* Header tabella - Blu navy elegante */
body.theme-maritime .card .table thead th {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #ffffff !important;
    border-color: rgba(255, 215, 0, 0.3) !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
    padding: 12px 8px !important;
}

/* Icone negli header */
body.theme-maritime .card .table thead th i {
    color: #ffd700 !important;
    margin-right: 6px !important;
}

/* Righe tabella - Alternanza elegante */
body.theme-maritime .card .table tbody td {
    color: #212529 !important;
    border-color: rgba(30, 58, 138, 0.1) !important;
    padding: 12px 8px !important;
    font-weight: 500 !important;
}

/* Righe pari - Sfondo leggermente blu */
body.theme-maritime .card .table tbody tr:nth-child(even) td {
    background-color: rgba(30, 58, 138, 0.03) !important;
}

/* Hover effect - Oro elegante */
body.theme-maritime .card .table tbody tr:hover td {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0.12) 100%) !important;
    color: #1e3a8a !important;
    transform: translateY(-1px) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2) !important;
}

/* Tabella compatta specifica per SOF */
body.theme-maritime .card .table-compact {
    border-collapse: separate !important;
    border-spacing: 0 3px !important;
}

body.theme-maritime .card .table-compact thead th {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    border: none !important;
    border-radius: 8px 8px 0 0 !important;
    position: relative !important;
}

body.theme-maritime .card .table-compact thead th:first-child {
    border-radius: 8px 0 0 0 !important;
}

body.theme-maritime .card .table-compact thead th:last-child {
    border-radius: 0 8px 0 0 !important;
}

body.theme-maritime .card .table-compact tbody tr {
    background-color: #ffffff !important;
    border-radius: 6px !important;
    box-shadow: 0 1px 3px rgba(30, 58, 138, 0.1) !important;
    margin-bottom: 3px !important;
}

body.theme-maritime .card .table-compact tbody tr td {
    border: none !important;
    border-top: 1px solid rgba(30, 58, 138, 0.05) !important;
    background-color: transparent !important;
}

body.theme-maritime .card .table-compact tbody tr:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.25) !important;
}

body.theme-maritime .card .table-compact tbody tr:hover td {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 215, 0, 0.1) 100%) !important;
    color: #1e3a8a !important;
    font-weight: 600 !important;
}

/* ===== ELEMENTI SPECIFICI TABELLA SOF ===== */

/* Badge porto gestione */
body.theme-maritime .card .table .badge.porto-gestione-badge {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #ffd700 !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    font-weight: 600 !important;
    padding: 6px 12px !important;
    border-radius: 12px !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* Avatar circle nella tabella */
body.theme-maritime .card .table .avatar-circle {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%) !important;
    color: #1e3a8a !important;
    border: 2px solid rgba(30, 58, 138, 0.2) !important;
    font-weight: 700 !important;
    text-shadow: none !important;
}

/* Date nella tabella */
body.theme-maritime .card .table .fw-bold {
    color: #1e3a8a !important;
    font-weight: 700 !important;
}

/* Link nella tabella */
body.theme-maritime .card .table a {
    color: #1e3a8a !important;
    font-weight: 600 !important;
    text-decoration: none !important;
}

body.theme-maritime .card .table a:hover {
    color: #0f2557 !important;
    text-decoration: underline !important;
}

/* Pulsanti azioni nella tabella */
body.theme-maritime .card .table .action-buttons .btn {
    border-width: 2px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .card .table .action-buttons .btn-outline-primary {
    color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    background-color: transparent !important;
}

body.theme-maritime .card .table .action-buttons .btn-outline-primary:hover {
    background-color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
}

body.theme-maritime .card .table .action-buttons .btn-outline-success {
    color: #198754 !important;
    border-color: #198754 !important;
    background-color: transparent !important;
}

body.theme-maritime .card .table .action-buttons .btn-outline-success:hover {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3) !important;
}

body.theme-maritime .card .table .action-buttons .btn-outline-warning {
    color: #fd7e14 !important;
    border-color: #fd7e14 !important;
    background-color: transparent !important;
}

body.theme-maritime .card .table .action-buttons .btn-outline-warning:hover {
    background-color: #fd7e14 !important;
    border-color: #fd7e14 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3) !important;
}

/* Pulsanti rotondi specifici */
body.theme-maritime .card .table .action-buttons .btn.rounded-pill {
    width: 38px !important;
    height: 38px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Stato completato */
body.theme-maritime .card .table .badge.bg-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* Responsive per tabella */
@media (max-width: 768px) {
    body.theme-maritime .card .table-compact .action-buttons .btn.rounded-pill {
        width: 34px !important;
        height: 34px !important;
    }

    body.theme-maritime .card .table-compact .badge.porto-gestione-badge {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }

    body.theme-maritime .card .table-compact .avatar-circle {
        width: 26px !important;
        height: 26px !important;
        font-size: 0.7rem !important;
    }
}

/* ===== FIX ALERT ===== */

body.theme-maritime .alert {
    color: #ffffff !important;
    font-weight: 500 !important;
}

body.theme-maritime .alert-light {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: #ffd700 !important;
    color: #212529 !important;
}

body.theme-maritime .alert-primary {
    background-color: rgba(30, 58, 138, 0.9) !important;
    border-color: #ffd700 !important;
    color: #ffffff !important;
}

body.theme-maritime .alert-info {
    background-color: rgba(23, 162, 184, 0.9) !important;
    border-color: #17a2b8 !important;
    color: #ffffff !important;
}

body.theme-maritime .alert-warning {
    background-color: rgba(255, 193, 7, 0.9) !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

body.theme-maritime .alert-success {
    background-color: rgba(25, 135, 84, 0.9) !important;
    border-color: #198754 !important;
    color: #ffffff !important;
}

body.theme-maritime .alert-danger {
    background-color: rgba(220, 53, 69, 0.9) !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

/* ===== FIX BADGE TEMA MARITTIMO ===== */

/* Badge su card/sfondi chiari - OVERRIDE COMPLETO */
body.theme-maritime .card .badge,
body.theme-maritime .bg-white .badge,
body.theme-maritime .bg-light .badge {
    font-weight: 700 !important;
    border: 1px solid rgba(30, 58, 138, 0.2) !important;
    padding: 6px 12px !important;
    border-radius: 8px !important;
}

/* Badge specifici per colore */
body.theme-maritime .card .badge.bg-secondary,
body.theme-maritime .bg-white .badge.bg-secondary,
body.theme-maritime .bg-light .badge.bg-secondary {
    background-color: #1e3a8a !important;
    color: #ffffff !important;
    border-color: #1e3a8a !important;
}

body.theme-maritime .card .badge.bg-primary,
body.theme-maritime .bg-white .badge.bg-primary,
body.theme-maritime .bg-light .badge.bg-primary {
    background-color: #1e3a8a !important;
    color: #ffd700 !important;
    border-color: #ffd700 !important;
}

body.theme-maritime .card .badge.bg-info,
body.theme-maritime .bg-white .badge.bg-info,
body.theme-maritime .bg-light .badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #212529 !important;
    border-color: #0dcaf0 !important;
}

body.theme-maritime .card .badge.bg-warning,
body.theme-maritime .bg-white .badge.bg-warning,
body.theme-maritime .bg-light .badge.bg-warning {
    background-color: #fd7e14 !important;
    color: #ffffff !important;
    border-color: #fd7e14 !important;
}

body.theme-maritime .card .badge.bg-success,
body.theme-maritime .bg-white .badge.bg-success,
body.theme-maritime .bg-light .badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
    border-color: #198754 !important;
}

body.theme-maritime .card .badge.bg-danger,
body.theme-maritime .bg-white .badge.bg-danger,
body.theme-maritime .bg-light .badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    border-color: #dc3545 !important;
}

/* Badge con classi text-dark problematiche */
body.theme-maritime .card .badge.text-dark,
body.theme-maritime .bg-white .badge.text-dark,
body.theme-maritime .bg-light .badge.text-dark {
    color: #212529 !important;
    font-weight: 700 !important;
}

/* ===== FIX PULSANTI ===== */

/* Pulsanti su sfondi chiari */
body.theme-maritime .card .btn,
body.theme-maritime .bg-white .btn,
body.theme-maritime .bg-light .btn {
    color: #1e3a8a !important;
    font-weight: 600 !important;
}

body.theme-maritime .card .btn-primary,
body.theme-maritime .bg-white .btn-primary,
body.theme-maritime .bg-light .btn-primary {
    background-color: #1e3a8a !important;
    border-color: #1e3a8a !important;
    color: #ffffff !important;
}

body.theme-maritime .card .btn-outline-primary,
body.theme-maritime .bg-white .btn-outline-primary,
body.theme-maritime .bg-light .btn-outline-primary {
    color: #1e3a8a !important;
    border-color: #1e3a8a !important;
}

body.theme-maritime .card .btn-outline-primary:hover,
body.theme-maritime .bg-white .btn-outline-primary:hover,
body.theme-maritime .bg-light .btn-outline-primary:hover {
    background-color: #1e3a8a !important;
    color: #ffffff !important;
}

/* Pulsanti su sfondi scuri */
body.theme-maritime .navbar .btn-outline-light {
    color: #ffffff !important;
    border-color: #ffffff !important;
}

body.theme-maritime .navbar .btn-outline-light:hover {
    background-color: #ffffff !important;
    color: #1e3a8a !important;
}

/* Pulsanti con testo bianco problematico */
body.theme-maritime .card .btn[style*="color: white"],
body.theme-maritime .bg-white .btn[style*="color: white"],
body.theme-maritime .bg-light .btn[style*="color: white"] {
    color: #1e3a8a !important;
}

/* ===== BADGE MARITTIMI MIGLIORATI ===== */

/* Badge base marittimo */
body.theme-maritime .badge {
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    border-radius: 8px !important;
    padding: 0.5rem 0.75rem !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
    transition: all 0.3s ease !important;
}

/* Badge primario marittimo */
body.theme-maritime .badge.bg-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255,215,0,0.3) !important;
}

body.theme-maritime .badge.bg-primary:hover {
    background: linear-gradient(135deg, #2a5298 0%, #1e40af 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(30,60,114,0.4) !important;
}

/* Badge successo marittimo */
body.theme-maritime .badge.bg-success {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255,215,0,0.2) !important;
}

body.theme-maritime .badge.bg-success:hover {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(16,185,129,0.4) !important;
}

/* Badge warning marittimo */
body.theme-maritime .badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
    color: #1f2937 !important;
    border: 1px solid rgba(30,60,114,0.2) !important;
    font-weight: 700 !important;
}

body.theme-maritime .badge.bg-warning:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #fcd34d 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(251,191,36,0.4) !important;
}

/* Badge info marittimo */
body.theme-maritime .badge.bg-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255,215,0,0.2) !important;
}

body.theme-maritime .badge.bg-info:hover {
    background: linear-gradient(135deg, #38bdf8 0%, #7dd3fc 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(56,189,248,0.4) !important;
}

/* Badge danger marittimo */
body.theme-maritime .badge.bg-danger {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255,215,0,0.2) !important;
}

body.theme-maritime .badge.bg-danger:hover {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(239,68,68,0.4) !important;
}

/* Badge secondario marittimo */
body.theme-maritime .badge.bg-secondary {
    background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255,215,0,0.2) !important;
}

body.theme-maritime .badge.bg-secondary:hover {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(107,114,128,0.4) !important;
}

/* ===== MODAL MIGLIORATO ===== */

body.theme-maritime .modal-content {
    background: linear-gradient(135deg, rgba(30,60,114,0.95) 0%, rgba(42,82,152,0.95) 100%) !important;
    color: #ffffff !important;
    border: 2px solid rgba(255,215,0,0.4) !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0,0,0,0.4) !important;
    backdrop-filter: blur(15px) !important;
    overflow: hidden !important;
}

body.theme-maritime .modal-footer {
    background: linear-gradient(135deg, rgba(30,60,114,0.8) 0%, rgba(42,82,152,0.8) 100%) !important;
    border-top: 2px solid rgba(255,215,0,0.4) !important;
    backdrop-filter: blur(10px) !important;
}

/* ===== FORM CONTROLS MARITTIMI MIGLIORATI ===== */

/* Form select marittimo */
body.theme-maritime .modal-body .form-select {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%) !important;
    border: 2px solid rgba(30,60,114,0.3) !important;
    color: #212529 !important;
    border-radius: 10px !important;
    padding: 0.75rem 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

body.theme-maritime .modal-body .form-select:focus {
    border-color: #ffd700 !important;
    box-shadow: 0 0 0 0.2rem rgba(255,215,0,0.25), 0 4px 12px rgba(30,60,114,0.2) !important;
    background: rgba(255,255,255,1) !important;
}

body.theme-maritime .modal-body .form-select:hover {
    border-color: rgba(30,60,114,0.5) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* Form label marittimo */
body.theme-maritime .modal-body .form-label {
    color: #1e3c72 !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    text-shadow: none !important;
}

body.theme-maritime .modal-body .form-label i {
    color: #ffd700 !important;
    margin-right: 0.5rem !important;
    text-shadow: 0 0 5px rgba(255,215,0,0.3) !important;
}

/* ===== BOTTONI MARITTIMI MIGLIORATI ===== */

/* Bottone primario marittimo */
body.theme-maritime .modal-footer .btn-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    border: 2px solid #ffd700 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 10px !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
    box-shadow: 0 4px 12px rgba(30,60,114,0.3) !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #2a5298 0%, #1e40af 100%) !important;
    border-color: #ffed4e !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(30,60,114,0.4) !important;
}

body.theme-maritime .modal-footer .btn-primary:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 8px rgba(30,60,114,0.3) !important;
}

/* Bottone secondario marittimo */
body.theme-maritime .modal-footer .btn-secondary {
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%) !important;
    border: 2px solid rgba(30,60,114,0.3) !important;
    color: #1e3c72 !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, rgba(30,60,114,0.1) 0%, rgba(42,82,152,0.1) 100%) !important;
    border-color: #1e3c72 !important;
    color: #1e3c72 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}

/* Bottone warning marittimo */
body.theme-maritime .modal-footer .btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
    border: 2px solid #1e3c72 !important;
    color: #1f2937 !important;
    font-weight: 700 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 10px !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.3) !important;
    box-shadow: 0 4px 12px rgba(251,191,36,0.3) !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .modal-footer .btn-warning:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #fcd34d 100%) !important;
    border-color: #1e3c72 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(251,191,36,0.4) !important;
}

/* ===== FIX BREADCRUMB ===== */

body.theme-maritime .breadcrumb {
    background-color: rgba(30, 58, 138, 0.8) !important;
}

body.theme-maritime .breadcrumb-item {
    color: #adb5bd !important;
}

body.theme-maritime .breadcrumb-item.active {
    color: #ffd700 !important;
}

/* ===== FIX PAGINATION ===== */

body.theme-maritime .page-link {
    background-color: rgba(30, 58, 138, 0.8) !important;
    border-color: rgba(255, 215, 0, 0.3) !important;
    color: #ffffff !important;
}

body.theme-maritime .page-link:hover {
    background-color: rgba(255, 215, 0, 0.2) !important;
    color: #ffd700 !important;
}

/* ===== FIX SPECIFICI INPUT GROUP ===== */

/* Fix per icone negli input-group-text */
body.theme-maritime .input-group-text {
    background-color: rgba(30, 58, 138, 0.9) !important;
    border-color: rgba(255, 215, 0, 0.5) !important;
    color: #ffffff !important;
}

body.theme-maritime .input-group-text i {
    color: #ffd700 !important;
    font-size: 1.1em !important;
}

/* Fix specifico per icona lente di ricerca */
body.theme-maritime .input-group-text .fa-search {
    color: #ffd700 !important;
    font-weight: 600 !important;
}

/* ===== FIX SPECIFICI PER COMPONENTI SNIP ===== */

/* Fix per avatar circle */
body.theme-maritime .avatar-circle {
    background-color: #ffd700 !important;
    color: #1e3a8a !important;
    border: 2px solid rgba(255, 215, 0, 0.5) !important;
}

/* Fix per status indicator */
body.theme-maritime .status-indicator {
    border: 1px solid rgba(255, 215, 0, 0.5) !important;
}

/* ===== FIX SPECIFICI ELEMENTI COMUNI ===== */

/* Fix per testi piccoli */
body.theme-maritime small,
body.theme-maritime .small {
    color: #adb5bd !important;
    font-weight: 500 !important;
}

/* Fix per link - CONTESTO SPECIFICO */
/* Link su sfondi chiari */
body.theme-maritime .card a,
body.theme-maritime .bg-white a,
body.theme-maritime .bg-light a,
body.theme-maritime .table a {
    color: #1e3a8a !important;
    font-weight: 600 !important;
}

body.theme-maritime .card a:hover,
body.theme-maritime .bg-white a:hover,
body.theme-maritime .bg-light a:hover,
body.theme-maritime .table a:hover {
    color: #0f2557 !important;
    text-decoration: underline !important;
}

/* Link su sfondi scuri */
body.theme-maritime .navbar a,
body.theme-maritime [style*="background: linear-gradient"] a {
    color: #ffd700 !important;
}

body.theme-maritime .navbar a:hover,
body.theme-maritime [style*="background: linear-gradient"] a:hover {
    color: #ffed4e !important;
}

/* Fix per icone - CONTESTO SPECIFICO */
/* Icone su sfondi chiari */
body.theme-maritime .card .fas,
body.theme-maritime .card .far,
body.theme-maritime .card .fab,
body.theme-maritime .bg-white .fas,
body.theme-maritime .bg-white .far,
body.theme-maritime .bg-white .fab,
body.theme-maritime .bg-light .fas,
body.theme-maritime .bg-light .far,
body.theme-maritime .bg-light .fab {
    color: #1e3a8a !important;
}

/* Icone su sfondi scuri */
body.theme-maritime .navbar .fas,
body.theme-maritime .navbar .far,
body.theme-maritime .navbar .fab {
    color: #ffd700 !important;
}

/* ===== FIX ELEMENTI SPECIFICI PROBLEMATICI ===== */

/* Fix per tutti i testi che potrebbero essere bianchi su sfondi chiari */
body.theme-maritime .card h1,
body.theme-maritime .card h2,
body.theme-maritime .card h3,
body.theme-maritime .card h4,
body.theme-maritime .card h5,
body.theme-maritime .card h6,
body.theme-maritime .card p,
body.theme-maritime .card span,
body.theme-maritime .card div,
body.theme-maritime .bg-white h1,
body.theme-maritime .bg-white h2,
body.theme-maritime .bg-white h3,
body.theme-maritime .bg-white h4,
body.theme-maritime .bg-white h5,
body.theme-maritime .bg-white h6,
body.theme-maritime .bg-white p,
body.theme-maritime .bg-white span,
body.theme-maritime .bg-white div,
body.theme-maritime .bg-light h1,
body.theme-maritime .bg-light h2,
body.theme-maritime .bg-light h3,
body.theme-maritime .bg-light h4,
body.theme-maritime .bg-light h5,
body.theme-maritime .bg-light h6,
body.theme-maritime .bg-light p,
body.theme-maritime .bg-light span,
body.theme-maritime .bg-light div {
    color: #212529 !important;
}

/* Fix per label e testi di form */
body.theme-maritime .card label,
body.theme-maritime .bg-white label,
body.theme-maritime .bg-light label,
body.theme-maritime .card .form-label,
body.theme-maritime .bg-white .form-label,
body.theme-maritime .bg-light .form-label {
    color: #212529 !important;
    font-weight: 600 !important;
}

/* Fix per testi con classi specifiche problematiche */
body.theme-maritime .card .text-light,
body.theme-maritime .bg-white .text-light,
body.theme-maritime .bg-light .text-light {
    color: #6c757d !important;
    font-weight: 600 !important;
}

/* Fix per breadcrumb su sfondi chiari */
body.theme-maritime .card .breadcrumb-item,
body.theme-maritime .bg-white .breadcrumb-item,
body.theme-maritime .bg-light .breadcrumb-item {
    color: #6c757d !important;
}

body.theme-maritime .card .breadcrumb-item.active,
body.theme-maritime .bg-white .breadcrumb-item.active,
body.theme-maritime .bg-light .breadcrumb-item.active {
    color: #1e3a8a !important;
    font-weight: 600 !important;
}

/* ===== FIX AGGIUNTIVI SPECIFICI TABELLA SOF ===== */

/* Override avatar circle su card per essere più visibile */
body.theme-maritime .card .avatar-circle,
body.theme-maritime .bg-white .avatar-circle,
body.theme-maritime .bg-light .avatar-circle {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #ffd700 !important;
    border: 2px solid rgba(255, 215, 0, 0.3) !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    font-weight: 700 !important;
    font-size: 0.8rem !important;
}

/* Fix per strong/bold text su card */
body.theme-maritime .card strong,
body.theme-maritime .card .fw-bold,
body.theme-maritime .bg-white strong,
body.theme-maritime .bg-white .fw-bold,
body.theme-maritime .bg-light strong,
body.theme-maritime .bg-light .fw-bold {
    color: #1e3a8a !important;
    font-weight: 700 !important;
}

/* Fix per action-buttons container */
body.theme-maritime .card .action-buttons,
body.theme-maritime .bg-white .action-buttons,
body.theme-maritime .bg-light .action-buttons {
    display: flex !important;
    gap: 6px !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Fix per form-text su card */
body.theme-maritime .card .form-text,
body.theme-maritime .bg-white .form-text,
body.theme-maritime .bg-light .form-text {
    color: #6c757d !important;
    font-weight: 500 !important;
}

/* Fix per elementi con display flex */
body.theme-maritime .card .d-flex,
body.theme-maritime .bg-white .d-flex,
body.theme-maritime .bg-light .d-flex {
    align-items: center !important;
}

/* Fix per tutti gli span generici su card */
body.theme-maritime .card span:not(.badge):not(.text-success):not(.text-warning):not(.text-info):not(.text-danger),
body.theme-maritime .bg-white span:not(.badge):not(.text-success):not(.text-warning):not(.text-info):not(.text-danger),
body.theme-maritime .bg-light span:not(.badge):not(.text-success):not(.text-warning):not(.text-info):not(.text-danger) {
    color: #212529 !important;
}

/* Fix per tutti i div generici su card */
body.theme-maritime .card div:not(.badge):not(.btn):not(.alert),
body.theme-maritime .bg-white div:not(.badge):not(.btn):not(.alert),
body.theme-maritime .bg-light div:not(.badge):not(.btn):not(.alert) {
    color: inherit !important;
}

/* ===== FIX PULSANTI AZIONI SOF REALIZZATI ===== */

/* Pulsanti azioni nella tabella SOF Realizzati - Colorati e vivaci */
body.theme-maritime .card .table .action-buttons .btn.btn-outline-primary {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    color: #1565c0 !important;
    border: 2px solid #1976d2 !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-primary:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
    color: #ffffff !important;
    border-color: #1565c0 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(25, 118, 210, 0.4) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-success {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
    color: #2e7d32 !important;
    border: 2px solid #388e3c !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(56, 142, 60, 0.2) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-success:hover {
    background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%) !important;
    color: #ffffff !important;
    border-color: #2e7d32 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(56, 142, 60, 0.4) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-warning {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%) !important;
    color: #ef6c00 !important;
    border: 2px solid #f57c00 !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(245, 124, 0, 0.2) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-warning:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%) !important;
    color: #ffffff !important;
    border-color: #ef6c00 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(245, 124, 0, 0.4) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-danger {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
    color: #c62828 !important;
    border: 2px solid #d32f2f !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.2) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-danger:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%) !important;
    color: #ffffff !important;
    border-color: #c62828 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(211, 47, 47, 0.4) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-info {
    background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%) !important;
    color: #0277bd !important;
    border: 2px solid #0288d1 !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(2, 136, 209, 0.2) !important;
}

body.theme-maritime .card .table .action-buttons .btn.btn-outline-info:hover {
    background: linear-gradient(135deg, #0288d1 0%, #0277bd 100%) !important;
    color: #ffffff !important;
    border-color: #0277bd !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(2, 136, 209, 0.4) !important;
}

/* Pulsanti rotondi specifici con colori vivaci */
body.theme-maritime .card .table .action-buttons .btn.rounded-pill {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.9rem !important;
    border-width: 2px !important;
}

/* ===== FIX TAB NAVIGATION VIAGGIO DETTAGLIO ===== */

/* Tab navigation - Scritte NERE per massima leggibilità */
body.theme-maritime .card .nav-tabs .nav-link {
    color: #000000 !important;
    font-weight: 700 !important;
    border: 2px solid transparent !important;
    background-color: rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    text-shadow: none !important;
}

body.theme-maritime .card .nav-tabs .nav-link:hover {
    color: #000000 !important;
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    font-weight: 800 !important;
}

body.theme-maritime .card .nav-tabs .nav-link.active {
    color: #ffffff !important;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    border-color: #1e3a8a !important;
    font-weight: 700 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* Tab content - Assicura leggibilità */
body.theme-maritime .card .tab-content {
    background-color: #ffffff !important;
    border: 1px solid rgba(30, 58, 138, 0.1) !important;
    border-top: none !important;
    padding: 20px !important;
}

/* Tab pane specifiche */
body.theme-maritime .card .tab-pane h4,
body.theme-maritime .card .tab-pane h5,
body.theme-maritime .card .tab-pane h6 {
    color: #1e3a8a !important;
    font-weight: 700 !important;
    margin-bottom: 15px !important;
}

body.theme-maritime .card .tab-pane .table th {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

body.theme-maritime .card .tab-pane .table td {
    color: #212529 !important;
    font-weight: 500 !important;
}

/* Fix specifico per le tab "Orari Import Export" e "SOF" - NERE */
body.theme-maritime .card .nav-tabs .nav-link[href="#orari"],
body.theme-maritime .card .nav-tabs .nav-link[href="#sof"],
body.theme-maritime .card .nav-tabs .nav-link[href="#import-export"] {
    color: #000000 !important;
    font-weight: 800 !important;
    font-size: 0.95rem !important;
    text-shadow: none !important;
}

body.theme-maritime .card .nav-tabs .nav-link[href="#orari"]:hover,
body.theme-maritime .card .nav-tabs .nav-link[href="#sof"]:hover,
body.theme-maritime .card .nav-tabs .nav-link[href="#import-export"]:hover {
    color: #000000 !important;
    background-color: rgba(0, 0, 0, 0.15) !important;
    font-weight: 900 !important;
}

/* ===== OVERRIDE AGGRESSIVO PER TUTTE LE TAB NERE ===== */

/* FORZA tutte le tab ad essere NERE - Override globale */
body.theme-maritime .nav-tabs .nav-link,
body.theme-maritime .card .nav-tabs .nav-link,
body.theme-maritime .bg-white .nav-tabs .nav-link,
body.theme-maritime .bg-light .nav-tabs .nav-link {
    color: #000000 !important;
    font-weight: 700 !important;
    text-shadow: none !important;
}

body.theme-maritime .nav-tabs .nav-link:hover,
body.theme-maritime .card .nav-tabs .nav-link:hover,
body.theme-maritime .bg-white .nav-tabs .nav-link:hover,
body.theme-maritime .bg-light .nav-tabs .nav-link:hover {
    color: #000000 !important;
    font-weight: 800 !important;
    text-shadow: none !important;
}

/* Tab attive mantengono il gradiente ma con testo bianco */
body.theme-maritime .nav-tabs .nav-link.active,
body.theme-maritime .card .nav-tabs .nav-link.active,
body.theme-maritime .bg-white .nav-tabs .nav-link.active,
body.theme-maritime .bg-light .nav-tabs .nav-link.active {
    color: #ffffff !important;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    border-color: #1e3a8a !important;
    font-weight: 700 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* Override per qualsiasi altra classe che potrebbe interferire */
body.theme-maritime .nav-tabs .nav-link:not(.active),
body.theme-maritime .card .nav-tabs .nav-link:not(.active),
body.theme-maritime .bg-white .nav-tabs .nav-link:not(.active),
body.theme-maritime .bg-light .nav-tabs .nav-link:not(.active) {
    color: #000000 !important;
    font-weight: 700 !important;
    text-shadow: none !important;
}

/* ===== FIX PULSANTI JAVASCRIPT - ASSICURA FUNZIONALITÀ ===== */

/* Assicura che i pulsanti con onclick siano sempre cliccabili */
body.theme-maritime .card .btn[onclick],
body.theme-maritime .bg-white .btn[onclick],
body.theme-maritime .bg-light .btn[onclick] {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Fix specifico per pulsanti azioni SOF */
body.theme-maritime .card .action-buttons .btn[onclick],
body.theme-maritime .bg-white .action-buttons .btn[onclick],
body.theme-maritime .bg-light .action-buttons .btn[onclick] {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 15 !important;
    user-select: none !important;
}

/* Assicura che i pulsanti Archivia SOF siano funzionanti */
body.theme-maritime .btn[onclick*="archiviaSOF"],
body.theme-maritime .card .btn[onclick*="archiviaSOF"],
body.theme-maritime .bg-white .btn[onclick*="archiviaSOF"],
body.theme-maritime .bg-light .btn[onclick*="archiviaSOF"] {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 20 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix per tutti i pulsanti con funzioni JavaScript */
body.theme-maritime .btn[onclick*="inviaNotificaSOF"],
body.theme-maritime .btn[onclick*="ripristinaViaggio"],
body.theme-maritime .btn[onclick*="visualizzaDettagli"] {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 15 !important;
}

/* Assicura che gli event listeners funzionino */
body.theme-maritime .card .table .action-buttons {
    pointer-events: auto !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Fix per modali che potrebbero essere coperti */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
}

/* Assicura che i pulsanti nei modali funzionino */
.modal .btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 1060 !important;
}

/* ===== FIX ANTEPRIMA SOF TEMA MARITTIMO - OVERRIDE ULTRA-SPECIFICO ===== */

/* Override ultra-specifico per anteprima SOF */
body.theme-maritime .sof-preview-container,
body.theme-maritime #sof_preview_container .sof-preview-container,
body.theme-maritime div.sof-preview-container,
body.theme-maritime .card-body .sof-preview-container,
body.theme-maritime .sof-preview-content {
    background-color: #ffffff !important;
    background: #ffffff !important;
    color: #212529 !important;
    border: 3px solid #ffd700 !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
    font-family: 'Courier New', monospace !important;
    font-size: 0.85em !important;
    line-height: 1.4 !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    max-height: 400px !important;
    overflow-y: auto !important;
    padding: 1rem !important;
    border-radius: 0.375rem !important;
}

/* Forza colore testo per tutti gli elementi figli */
body.theme-maritime .sof-preview-container *,
body.theme-maritime .sof-preview-container div,
body.theme-maritime .sof-preview-container span,
body.theme-maritime .sof-preview-container p,
body.theme-maritime .sof-preview-content *,
body.theme-maritime .sof-preview-content div,
body.theme-maritime .sof-preview-content span,
body.theme-maritime .sof-preview-content p {
    color: #212529 !important;
    background: transparent !important;
}

/* Fix per testo muted nell'anteprima */
body.theme-maritime .sof-preview-container .text-muted,
body.theme-maritime .sof-preview-content .text-muted {
    color: #6c757d !important;
    background: transparent !important;
}

/* Placeholder anteprima SOF */
body.theme-maritime .sof-preview-placeholder {
    color: #212529 !important;
    background: #ffffff !important;
    border: 3px solid #ffd700 !important;
    border-radius: 0.375rem !important;
    text-align: center !important;
    padding: 2rem !important;
}
