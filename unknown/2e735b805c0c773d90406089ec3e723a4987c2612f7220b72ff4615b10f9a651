#!/usr/bin/env python3
"""
Sistema di notifiche automatiche per tutte le azioni SNIP
Genera notifiche per ogni operazione importante divisa per reparto
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
import logging

logger = logging.getLogger(__name__)

class SNIPNotificationSystem:
    """Sistema di notifiche automatiche per SNIP"""
    
    def __init__(self):
        self.notification_types = {
            # OPERATIVO
            'VIAGGIO_CREATO': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'OPERATIVO'},
            'VIAGGIO_MODIFICATO': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'VIAGGIO_ELIMINATO': {'type': 'WARNING', 'priority': 3, 'reparto': 'OPERATIVO'},
            'SOF_GENERATO': {'type': 'SUCCESS', 'priority': 4, 'reparto': 'OPERATIVO'},
            'SOF_SCARICATO': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'SOF_COMPLETATO': {'type': 'SUCCESS', 'priority': 4, 'reparto': 'OPERATIVO'},
            'SOF_RIPRISTINATO': {'type': 'WARNING', 'priority': 3, 'reparto': 'OPERATIVO'},
            'NAVE_AGGIUNTA': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'NAVE_MODIFICATA': {'type': 'INFO', 'priority': 1, 'reparto': 'OPERATIVO'},
            'NAVE_ELIMINATA': {'type': 'WARNING', 'priority': 2, 'reparto': 'OPERATIVO'},
            'ARMATORE_AGGIUNTO': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'ARMATORE_MODIFICATO': {'type': 'INFO', 'priority': 1, 'reparto': 'OPERATIVO'},
            'ARMATORE_ELIMINATO': {'type': 'WARNING', 'priority': 2, 'reparto': 'OPERATIVO'},
            'PORTO_AGGIUNTO': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'ORARI_AGGIORNATI': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'IMPORT_AGGIORNATO': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            'EXPORT_AGGIORNATO': {'type': 'INFO', 'priority': 2, 'reparto': 'OPERATIVO'},
            
            # AMMINISTRAZIONE
            'UTENTE_CREATO': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'AMMINISTRAZIONE'},
            'UTENTE_MODIFICATO': {'type': 'INFO', 'priority': 2, 'reparto': 'AMMINISTRAZIONE'},
            'UTENTE_ELIMINATO': {'type': 'WARNING', 'priority': 3, 'reparto': 'AMMINISTRAZIONE'},
            'UTENTE_ATTIVATO': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'AMMINISTRAZIONE'},
            'UTENTE_DISATTIVATO': {'type': 'WARNING', 'priority': 3, 'reparto': 'AMMINISTRAZIONE'},
            'RUOLO_MODIFICATO': {'type': 'WARNING', 'priority': 4, 'reparto': 'AMMINISTRAZIONE'},
            'CONFIGURAZIONE_MODIFICATA': {'type': 'INFO', 'priority': 2, 'reparto': 'AMMINISTRAZIONE'},
            'BACKUP_CREATO': {'type': 'SUCCESS', 'priority': 2, 'reparto': 'AMMINISTRAZIONE'},
            'SISTEMA_AGGIORNATO': {'type': 'INFO', 'priority': 3, 'reparto': 'AMMINISTRAZIONE'},
            'SESSIONE_INVALIDATA': {'type': 'WARNING', 'priority': 2, 'reparto': 'AMMINISTRAZIONE'},
            
            # SHORTSEA
            'ROTTA_CREATA': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'SHORTSEA'},
            'ROTTA_MODIFICATA': {'type': 'INFO', 'priority': 2, 'reparto': 'SHORTSEA'},
            'SPEDIZIONE_CREATA': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'SHORTSEA'},
            'SPEDIZIONE_MODIFICATA': {'type': 'INFO', 'priority': 2, 'reparto': 'SHORTSEA'},
            'REPORT_GENERATO': {'type': 'SUCCESS', 'priority': 2, 'reparto': 'SHORTSEA'},
            
            # CONTABILITA
            'FATTURA_CREATA': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'CONTABILITA'},
            'FATTURA_MODIFICATA': {'type': 'INFO', 'priority': 2, 'reparto': 'CONTABILITA'},
            'PAGAMENTO_REGISTRATO': {'type': 'SUCCESS', 'priority': 3, 'reparto': 'CONTABILITA'},
            'REPORT_FINANZIARIO': {'type': 'SUCCESS', 'priority': 2, 'reparto': 'CONTABILITA'},
            'BUDGET_AGGIORNATO': {'type': 'INFO', 'priority': 2, 'reparto': 'CONTABILITA'},
            
            # SISTEMA
            'LOGIN_FALLITO': {'type': 'WARNING', 'priority': 2, 'reparto': 'AMMINISTRAZIONE'},
            'ACCESSO_NEGATO': {'type': 'ERROR', 'priority': 3, 'reparto': 'AMMINISTRAZIONE'},
            'ERRORE_SISTEMA': {'type': 'ERROR', 'priority': 4, 'reparto': 'AMMINISTRAZIONE'},
        }
    
    def create_notification(self, db: Session, action_type: str, user_id: int, 
                          title: str, message: str, details: Optional[Dict[str, Any]] = None) -> bool:
        """Crea una notifica automatica per un'azione"""
        try:
            if action_type not in self.notification_types:
                logger.warning(f"Tipo notifica sconosciuto: {action_type}")
                return False
            
            config = self.notification_types[action_type]
            
            # Crea la notifica con timestamp corrente
            current_time = datetime.now()
            result = db.execute(text("""
                INSERT INTO "DEPARTMENT_NOTIFICATIONS"
                (title, message, notification_type, target_reparto, created_by, priority, send_email, is_active, created_at)
                VALUES (:title, :message, :type, :reparto, :created_by, :priority, :send_email, :is_active, :created_at)
                RETURNING id
            """), {
                "title": title,
                "message": message,
                "type": config['type'],
                "reparto": config['reparto'],
                "created_by": user_id,
                "priority": config['priority'],
                "send_email": False,  # Per ora disabilitato
                "is_active": True,
                "created_at": current_time
            })
            
            notification_id = result.fetchone()[0]
            db.commit()
            
            logger.info(f"Notifica creata: {action_type} - ID {notification_id}")
            return True
            
        except Exception as e:
            logger.error(f"Errore creazione notifica {action_type}: {e}")
            db.rollback()
            return False
    
    # === NOTIFICHE OPERATIVO ===
    
    def notify_viaggio_created(self, db: Session, user_id: int, viaggio_code: str, nave_name: str = None):
        """Notifica creazione nuovo viaggio"""
        title = f"🚢 Nuovo Viaggio Creato: {viaggio_code}"
        message = f"È stato creato un nuovo viaggio {viaggio_code}"
        if nave_name:
            message += f" per la nave {nave_name}"
        message += ". Il viaggio è ora disponibile per la gestione SOF."
        
        return self.create_notification(db, 'VIAGGIO_CREATO', user_id, title, message, 
                                      {"viaggio": viaggio_code, "nave": nave_name})
    
    def notify_sof_generated(self, db: Session, user_id: int, viaggio_code: str, file_path: str):
        """Notifica generazione SOF"""
        title = f"📋 SOF Generato: {viaggio_code}"
        message = f"Il documento SOF per il viaggio {viaggio_code} è stato generato con successo e può essere scaricato."

        return self.create_notification(db, 'SOF_GENERATO', user_id, title, message,
                                      {"viaggio": viaggio_code, "file": file_path})

    def notify_sof_downloaded(self, db: Session, user_id: int, viaggio_code: str, file_path: str):
        """Notifica download SOF esistente"""
        title = f"📥 SOF Scaricato: {viaggio_code}"
        message = f"Il documento SOF per il viaggio {viaggio_code} è stato scaricato. Il SOF era già stato generato in precedenza."

        return self.create_notification(db, 'SOF_SCARICATO', user_id, title, message,
                                      {"viaggio": viaggio_code, "file": file_path})

    def notify_sof_ripristinato(self, db: Session, user_id: int, viaggio_code: str, nave_name: str = None):
        """Notifica ripristino SOF"""
        title = f"🔄 SOF Ripristinato: {viaggio_code}"
        message = f"Il viaggio {viaggio_code}"
        if nave_name:
            message += f" (nave {nave_name})"
        message += " è stato ripristinato e tornerà nella lista 'SOF da Realizzare' per una nuova gestione."

        return self.create_notification(db, 'SOF_RIPRISTINATO', user_id, title, message,
                                      {"viaggio": viaggio_code, "nave": nave_name})
    
    def notify_nave_added(self, db: Session, user_id: int, nave_name: str, armatore: str = None):
        """Notifica aggiunta nuova nave"""
        title = f"🚢 Nuova Nave Aggiunta: {nave_name}"
        message = f"La nave {nave_name} è stata aggiunta al sistema"
        if armatore:
            message += f" per l'armatore {armatore}"
        message += "."
        
        return self.create_notification(db, 'NAVE_AGGIUNTA', user_id, title, message,
                                      {"nave": nave_name, "armatore": armatore})
    
    # === NOTIFICHE AMMINISTRAZIONE ===
    
    def notify_user_created(self, db: Session, admin_id: int, new_user_email: str, reparto: str, ruolo: str):
        """Notifica creazione nuovo utente"""
        title = f"👤 Nuovo Utente Creato: {new_user_email}"
        message = f"È stato creato un nuovo utente {new_user_email} con ruolo {ruolo} nel reparto {reparto}."
        
        return self.create_notification(db, 'UTENTE_CREATO', admin_id, title, message,
                                      {"email": new_user_email, "reparto": reparto, "ruolo": ruolo})
    
    def notify_user_activated(self, db: Session, admin_id: int, user_email: str):
        """Notifica attivazione utente"""
        title = f"✅ Utente Attivato: {user_email}"
        message = f"L'utente {user_email} è stato attivato e può ora accedere al sistema."
        
        return self.create_notification(db, 'UTENTE_ATTIVATO', admin_id, title, message,
                                      {"email": user_email})
    
    def notify_role_changed(self, db: Session, admin_id: int, user_email: str, old_role: str, new_role: str):
        """Notifica cambio ruolo utente"""
        title = f"🔄 Ruolo Modificato: {user_email}"
        message = f"Il ruolo dell'utente {user_email} è stato modificato da {old_role} a {new_role}."
        
        return self.create_notification(db, 'RUOLO_MODIFICATO', admin_id, title, message,
                                      {"email": user_email, "old_role": old_role, "new_role": new_role})
    
    # === NOTIFICHE SHORTSEA ===
    
    def notify_rotta_created(self, db: Session, user_id: int, rotta_name: str, origine: str, destinazione: str):
        """Notifica creazione nuova rotta"""
        title = f"🛣️ Nuova Rotta Creata: {rotta_name}"
        message = f"È stata creata una nuova rotta {rotta_name} da {origine} a {destinazione}."
        
        return self.create_notification(db, 'ROTTA_CREATA', user_id, title, message,
                                      {"rotta": rotta_name, "origine": origine, "destinazione": destinazione})
    
    def notify_spedizione_created(self, db: Session, user_id: int, spedizione_id: str, rotta: str):
        """Notifica creazione nuova spedizione"""
        title = f"📦 Nuova Spedizione: {spedizione_id}"
        message = f"È stata creata una nuova spedizione {spedizione_id} sulla rotta {rotta}."
        
        return self.create_notification(db, 'SPEDIZIONE_CREATA', user_id, title, message,
                                      {"spedizione": spedizione_id, "rotta": rotta})
    
    # === NOTIFICHE CONTABILITA ===
    
    def notify_fattura_created(self, db: Session, user_id: int, fattura_numero: str, importo: float, cliente: str):
        """Notifica creazione nuova fattura"""
        title = f"💰 Nuova Fattura: {fattura_numero}"
        message = f"È stata creata la fattura {fattura_numero} per €{importo:.2f} al cliente {cliente}."
        
        return self.create_notification(db, 'FATTURA_CREATA', user_id, title, message,
                                      {"fattura": fattura_numero, "importo": importo, "cliente": cliente})
    
    def notify_pagamento_registered(self, db: Session, user_id: int, fattura_numero: str, importo: float):
        """Notifica registrazione pagamento"""
        title = f"✅ Pagamento Registrato: {fattura_numero}"
        message = f"È stato registrato il pagamento di €{importo:.2f} per la fattura {fattura_numero}."
        
        return self.create_notification(db, 'PAGAMENTO_REGISTRATO', user_id, title, message,
                                      {"fattura": fattura_numero, "importo": importo})
    
    # === NOTIFICHE SISTEMA ===
    
    def notify_login_failed(self, db: Session, email: str, ip_address: str):
        """Notifica tentativo di login fallito"""
        title = f"⚠️ Tentativo Login Fallito"
        message = f"Tentativo di login fallito per {email} da IP {ip_address}."
        
        # Usa ID 1 (admin) per notifiche di sistema
        return self.create_notification(db, 'LOGIN_FALLITO', 1, title, message,
                                      {"email": email, "ip": ip_address})
    
    def notify_system_error(self, db: Session, error_type: str, error_message: str):
        """Notifica errore di sistema"""
        title = f"🚨 Errore Sistema: {error_type}"
        message = f"Si è verificato un errore di sistema: {error_message}"
        
        return self.create_notification(db, 'ERRORE_SISTEMA', 1, title, message,
                                      {"type": error_type, "message": error_message})

# Istanza globale del sistema notifiche
notification_system = SNIPNotificationSystem()
