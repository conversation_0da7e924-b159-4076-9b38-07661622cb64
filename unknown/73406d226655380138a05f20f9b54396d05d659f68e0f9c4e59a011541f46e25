<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrazione Completata</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
        }
        .success-container {
            max-width: 600px;
            margin: 40px auto;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.15);
            padding: 32px 24px;
            text-align: center;
        }
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
        .user-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .user-info p {
            margin: 5px 0;
            color: #495057;
        }
        .alert-info {
            background-color: #e3f2fd;
            border-color: #90caf9;
            color: #0d47a1;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✓</div>
        <h1 class="mb-4">Registrazione Completata!</h1>
        
        <div class="user-info">
            <p><strong>Nome:</strong> {{ nome }}</p>
            <p><strong>Cognome:</strong> {{ cognome }}</p>
            <p><strong>Email:</strong> {{ email }}</p>
            <p><strong>Reparto:</strong> {{ reparto }}</p>
            <p><strong>Ruolo:</strong> {{ ruolo }}</p>
        </div>

        <div class="alert alert-info" role="alert">
            <h4 class="alert-heading">Account in attesa di attivazione</h4>
            <p>La tua registrazione è stata completata con successo! Il tuo account è attualmente in attesa di attivazione da parte dell'amministratore.</p>
            <p>Riceverai una notifica via email quando il tuo account sarà stato attivato.</p>
        </div>

        <div class="mt-4">
            <a href="/" class="btn btn-primary">Torna alla pagina di login</a>
        </div>
    </div>
</body>
</html> 