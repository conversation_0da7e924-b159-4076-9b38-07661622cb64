# 🚢 SNIP - Sistema Navale Integrato Portuale

## 📋 Requisiti di Sistema

### Software Necessario:
- **Python 3.8+** (testato con 3.11, 3.12, 3.13)
- **PostgreSQL 12+** 
- **Git** (per clonare il repository)

### Sistema Operativo:
- ✅ **Windows 10/11**
- ✅ **Linux** (Ubuntu 20.04+, CentOS 8+)
- ✅ **macOS** (10.15+)

## 🚀 Installazione Rapida

### Metodo 1: Script Automatico (Consigliato)

```bash
# 1. Clona il repository
git clone <repository-url>
cd SNIP

# 2. Esegui lo script di installazione
python install.py
```

### Metodo 2: Installazione Manuale

```bash
# 1. Crea virtual environment
python -m venv venv

# 2. Attiva virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 3. Aggiorna pip
pip install --upgrade pip

# 4. Installa dipendenze
pip install -r requirements.txt
```

## 🗄️ Configurazione Database

### 1. Installa PostgreSQL
- **Windows**: Scarica da [postgresql.org](https://www.postgresql.org/download/windows/)
- **Linux**: `sudo apt install postgresql postgresql-contrib`
- **macOS**: `brew install postgresql`

### 2. Crea Database
```sql
-- Connettiti a PostgreSQL come superuser
psql -U postgres

-- Crea utente
CREATE USER re77 WITH PASSWORD '271077';

-- Crea database
CREATE DATABASE "AGENTE" OWNER re77;

-- Concedi privilegi
GRANT ALL PRIVILEGES ON DATABASE "AGENTE" TO re77;
```

### 3. Verifica Configurazione
Controlla il file `config.py`:
```python
DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"
```

## 🔧 Avvio Applicazione

### 1. Attiva Virtual Environment
```bash
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate
```

### 2. Avvia Server
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8002
```

### 3. Accedi all'Applicazione
Apri il browser e vai su: **http://localhost:8002**

## 📦 Dipendenze Principali

| Libreria | Versione | Scopo |
|----------|----------|-------|
| FastAPI | 0.104.1 | Framework web |
| SQLAlchemy | 2.0.23 | ORM database |
| psycopg2-binary | 2.9.9 | Driver PostgreSQL |
| python-docx | 1.1.2 | Generazione documenti Word |
| passlib | 1.7.4 | Hashing password |
| jinja2 | 3.1.2 | Template engine |

## 🔍 Risoluzione Problemi

### Errore: "ModuleNotFoundError"
```bash
# Assicurati che il virtual environment sia attivo
pip install -r requirements.txt
```

### Errore: "Connection refused" (Database)
```bash
# Verifica che PostgreSQL sia in esecuzione
# Windows:
services.msc → PostgreSQL
# Linux:
sudo systemctl status postgresql
```

### Errore: "lxml" o "python-docx"
```bash
# Reinstalla dipendenze problematiche
pip install --force-reinstall lxml python-docx
```

### Errore: "Port 8002 already in use"
```bash
# Usa una porta diversa
uvicorn main:app --reload --port 8003
```

## 📁 Struttura Directory

```
SNIP/
├── main.py                 # Applicazione principale
├── config.py              # Configurazione
├── database.py            # Setup database
├── models.py              # Modelli SQLAlchemy
├── requirements.txt       # Dipendenze Python
├── install.py             # Script installazione
├── sof_documents/         # Storage documenti SOF
├── static/                # File statici (CSS, JS)
├── templates/             # Template HTML
└── venv/                  # Virtual environment
```

## 🔐 Credenziali Default

### Super Admin:
- **Email**: `<EMAIL>`
- **Password**: `Ettore2025!`

### Test User:
- **Email**: `<EMAIL>`
- **Password**: `test123`

## 🌐 Funzionalità Principali

- ✅ **Gestione Viaggi** - Creazione e modifica viaggi navali
- ✅ **SOF (Statement of Facts)** - Generazione documenti DOCX
- ✅ **Import/Export** - Gestione carichi
- ✅ **Gestione Navi** - Database navi e armatori
- ✅ **Sistema Utenti** - Autenticazione e autorizzazioni
- ✅ **Dashboard** - Panoramica operativa
- ✅ **Notifiche** - Sistema notifiche integrate

## 🔄 Aggiornamenti

### Per aggiornare l'applicazione:
```bash
# 1. Ferma il server (Ctrl+C)
# 2. Aggiorna il codice
git pull origin main

# 3. Aggiorna dipendenze
pip install -r requirements.txt --upgrade

# 4. Riavvia il server
uvicorn main:app --reload --host 0.0.0.0 --port 8002
```

## 📞 Supporto

### In caso di problemi:
1. **Controlla i log** dell'applicazione
2. **Verifica la configurazione** database
3. **Assicurati** che tutte le dipendenze siano installate
4. **Consulta** la documentazione PostgreSQL

### Log dell'applicazione:
I log vengono mostrati nella console dove hai avviato il server.

## 🎯 Prossimi Passi

Dopo l'installazione:
1. **Accedi** con le credenziali admin
2. **Configura** i porti di gestione
3. **Aggiungi** navi e armatori
4. **Crea** il primo viaggio
5. **Genera** il primo SOF

---

**🎉 Benvenuto in SNIP - Sistema Navale Integrato Portuale!**
