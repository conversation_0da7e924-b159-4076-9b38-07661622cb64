/* ===== TEMA SCURO PROFESSIONALE ===== */

/* ===== PALETTE COLORI MODERNA SCURA ===== */
:root {
    --dark-primary: #1a1a2e;
    --dark-secondary: #16213e;
    --dark-accent: #0f3460;
    --dark-highlight: #e94560;
    --dark-success: #00d4aa;
    --dark-warning: #ffa726;
    --dark-danger: #ef5350;
    --dark-info: #26c6da;
    --dark-purple: #ab47bc;
    --dark-orange: #ff7043;
    --dark-bg-primary: #0d1117;
    --dark-bg-secondary: #161b22;
    --dark-bg-tertiary: #21262d;
    --dark-surface: #30363d;
    --dark-border: #30363d;
    --dark-text-primary: #f0f6fc;
    --dark-text-secondary: #8b949e;
    --dark-text-muted: #6e7681;
    --dark-shadow: rgba(0, 0, 0, 0.3);
    --dark-shadow-heavy: rgba(0, 0, 0, 0.5);
    --dark-glow: rgba(233, 69, 96, 0.3);
}

/* ===== BACKGROUND PRINCIPALE SCURO ===== */
body.theme-dark {
    background: linear-gradient(135deg, var(--dark-bg-primary) 0%, var(--dark-primary) 50%, var(--dark-secondary) 100%) !important;
    color: var(--dark-text-primary) !important;
    min-height: 100vh;
    position: relative;
}

body.theme-dark::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(233, 69, 96, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 170, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(171, 71, 188, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* ===== NAVBAR SCURA PROFESSIONALE ===== */
body.theme-dark .snip-navbar {
    background: linear-gradient(135deg, var(--dark-bg-secondary) 0%, var(--dark-surface) 100%) !important;
    border-bottom: 1px solid var(--dark-border) !important;
    box-shadow: 0 4px 20px var(--dark-shadow) !important;
    backdrop-filter: blur(20px) !important;
}

body.theme-dark .navbar-brand {
    color: var(--dark-text-primary) !important;
    font-weight: 700 !important;
    text-shadow: 0 0 10px var(--dark-glow) !important;
}

body.theme-dark .navbar .nav-link {
    color: var(--dark-text-secondary) !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

body.theme-dark .navbar .nav-link:hover {
    color: var(--dark-highlight) !important;
    text-shadow: 0 0 8px var(--dark-glow) !important;
    transform: translateY(-1px) !important;
}

/* ===== CARD SCURE MODERNE COLORATE ===== */
body.theme-dark .card {
    background: linear-gradient(135deg,
        rgba(26, 26, 46, 0.95) 0%,
        rgba(22, 33, 62, 0.95) 50%,
        rgba(15, 52, 96, 0.95) 100%) !important;
    border: 2px solid rgba(102, 126, 234, 0.3) !important;
    border-radius: 20px !important;
    box-shadow:
        0 12px 40px rgba(0,0,0,0.4),
        0 4px 16px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.1),
        0 0 20px rgba(102, 126, 234, 0.1) !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    overflow: hidden !important;
    position: relative !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--dark-highlight), var(--dark-success), var(--dark-info)) !important;
}

body.theme-dark .card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 
        0 16px 48px var(--dark-shadow-heavy),
        0 4px 16px rgba(0,0,0,0.3),
        0 0 20px var(--dark-glow) !important;
}

body.theme-dark .card-header {
    background: linear-gradient(135deg, var(--dark-bg-tertiary) 0%, var(--dark-surface) 100%) !important;
    border-bottom: 1px solid var(--dark-border) !important;
    padding: 20px !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .card-header h5 {
    color: var(--dark-text-primary) !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
}

body.theme-dark .card-header small {
    color: var(--dark-text-secondary) !important;
    font-weight: 500 !important;
}

body.theme-dark .card-body {
    padding: 24px !important;
    background: rgba(33, 38, 45, 0.5) !important;
    color: var(--dark-text-primary) !important;
}

/* ===== PULSANTI SCURI PROFESSIONALI ===== */
body.theme-dark .btn {
    border-radius: 12px !important;
    font-weight: 500 !important;
    padding: 12px 24px !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    position: relative !important;
    overflow: hidden !important;
    border: none !important;
}

body.theme-dark .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

body.theme-dark .btn:hover::before {
    left: 100%;
}

body.theme-dark .btn-primary {
    background: linear-gradient(135deg, var(--dark-highlight) 0%, #c62d42 100%) !important;
    box-shadow: 0 4px 16px rgba(233, 69, 96, 0.4) !important;
    color: white !important;
}

body.theme-dark .btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(233, 69, 96, 0.6) !important;
    color: white !important;
}

body.theme-dark .btn-success {
    background: linear-gradient(135deg, var(--dark-success) 0%, #00b894 100%) !important;
    box-shadow: 0 4px 16px rgba(0, 212, 170, 0.4) !important;
    color: white !important;
}

body.theme-dark .btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(0, 212, 170, 0.6) !important;
    color: white !important;
}

body.theme-dark .btn-warning {
    background: linear-gradient(135deg, var(--dark-warning) 0%, #ff9800 100%) !important;
    box-shadow: 0 4px 16px rgba(255, 167, 38, 0.4) !important;
    color: white !important;
}

body.theme-dark .btn-warning:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(255, 167, 38, 0.6) !important;
    color: white !important;
}

body.theme-dark .btn-danger {
    background: linear-gradient(135deg, var(--dark-danger) 0%, #e53935 100%) !important;
    box-shadow: 0 4px 16px rgba(239, 83, 80, 0.4) !important;
    color: white !important;
}

body.theme-dark .btn-danger:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(239, 83, 80, 0.6) !important;
    color: white !important;
}

body.theme-dark .btn-info {
    background: linear-gradient(135deg, var(--dark-info) 0%, #00acc1 100%) !important;
    box-shadow: 0 4px 16px rgba(38, 198, 218, 0.4) !important;
    color: white !important;
}

body.theme-dark .btn-info:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(38, 198, 218, 0.6) !important;
    color: white !important;
}

body.theme-dark .btn-secondary {
    background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-border) 100%) !important;
    box-shadow: 0 4px 16px rgba(48, 54, 61, 0.4) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .btn-secondary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(48, 54, 61, 0.6) !important;
    color: var(--dark-text-primary) !important;
}

/* ===== PULSANTI OUTLINE SCURI ===== */
body.theme-dark .btn-outline-primary {
    border: 2px solid var(--dark-highlight) !important;
    color: var(--dark-highlight) !important;
    background: rgba(233, 69, 96, 0.1) !important;
}

body.theme-dark .btn-outline-primary:hover {
    background: var(--dark-highlight) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(233, 69, 96, 0.4) !important;
}

body.theme-dark .btn-outline-success {
    border: 2px solid var(--dark-success) !important;
    color: var(--dark-success) !important;
    background: rgba(0, 212, 170, 0.1) !important;
}

body.theme-dark .btn-outline-success:hover {
    background: var(--dark-success) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(0, 212, 170, 0.4) !important;
}

body.theme-dark .btn-outline-warning {
    border: 2px solid var(--dark-warning) !important;
    color: var(--dark-warning) !important;
    background: rgba(255, 167, 38, 0.1) !important;
}

body.theme-dark .btn-outline-warning:hover {
    background: var(--dark-warning) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(255, 167, 38, 0.4) !important;
}

body.theme-dark .btn-outline-danger {
    border: 2px solid var(--dark-danger) !important;
    color: var(--dark-danger) !important;
    background: rgba(239, 83, 80, 0.1) !important;
}

body.theme-dark .btn-outline-danger:hover {
    background: var(--dark-danger) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(239, 83, 80, 0.4) !important;
}

body.theme-dark .btn-outline-info {
    border: 2px solid var(--dark-info) !important;
    color: var(--dark-info) !important;
    background: rgba(38, 198, 218, 0.1) !important;
}

body.theme-dark .btn-outline-info:hover {
    background: var(--dark-info) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(38, 198, 218, 0.4) !important;
}

/* ===== BADGE SCURI PROFESSIONALI ===== */
body.theme-dark .badge {
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 6px 12px !important;
    font-size: 0.75rem !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
    border: 1px solid rgba(255,255,255,0.1) !important;
}

body.theme-dark .badge.bg-primary {
    background: linear-gradient(135deg, var(--dark-highlight), #c62d42) !important;
    color: white !important;
}

body.theme-dark .badge.bg-success {
    background: linear-gradient(135deg, var(--dark-success), #00b894) !important;
    color: white !important;
}

body.theme-dark .badge.bg-warning {
    background: linear-gradient(135deg, var(--dark-warning), #ff9800) !important;
    color: white !important;
}

body.theme-dark .badge.bg-danger {
    background: linear-gradient(135deg, var(--dark-danger), #e53935) !important;
    color: white !important;
}

body.theme-dark .badge.bg-info {
    background: linear-gradient(135deg, var(--dark-info), #00acc1) !important;
    color: white !important;
}

body.theme-dark .badge.bg-secondary {
    background: linear-gradient(135deg, var(--dark-surface), var(--dark-border)) !important;
    color: var(--dark-text-primary) !important;
}

/* ===== FORM SCURI PROFESSIONALI ===== */
body.theme-dark .form-control {
    background: var(--dark-bg-tertiary) !important;
    border: 2px solid var(--dark-border) !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    color: var(--dark-text-primary) !important;
    transition: all 0.3s ease !important;
}

body.theme-dark .form-control:focus {
    background: var(--dark-surface) !important;
    border-color: var(--dark-highlight) !important;
    box-shadow: 0 0 0 0.2rem rgba(233, 69, 96, 0.25) !important;
    color: var(--dark-text-primary) !important;
    transform: translateY(-1px) !important;
}

body.theme-dark .form-control::placeholder {
    color: var(--dark-text-muted) !important;
}

body.theme-dark .form-label {
    color: var(--dark-text-primary) !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
}

body.theme-dark .form-select {
    background: var(--dark-bg-tertiary) !important;
    border: 2px solid var(--dark-border) !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .form-select:focus {
    background: var(--dark-surface) !important;
    border-color: var(--dark-highlight) !important;
    box-shadow: 0 0 0 0.2rem rgba(233, 69, 96, 0.25) !important;
}

/* ===== TABELLE SCURE PROFESSIONALI ===== */
body.theme-dark .table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 16px var(--dark-shadow) !important;
    background: var(--dark-bg-secondary) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .table th {
    background: linear-gradient(135deg, var(--dark-bg-tertiary) 0%, var(--dark-surface) 100%) !important;
    color: #f0f6fc !important;
    font-weight: 600 !important;
    padding: 16px !important;
    border: none !important;
    position: relative !important;
}

body.theme-dark .table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--dark-highlight), var(--dark-success)) !important;
}

body.theme-dark .table td {
    padding: 16px !important;
    border: none !important;
    border-bottom: 1px solid rgba(48, 54, 61, 0.5) !important;
    transition: all 0.3s ease !important;
    color: #e6edf3 !important;
    background-color: transparent !important;
}

body.theme-dark .table tbody tr:hover {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.1) 0%, rgba(63, 185, 80, 0.1) 100%) !important;
    transform: scale(1.01) !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2) !important;
}

body.theme-dark .table tbody tr:hover td {
    color: #f0f6fc !important;
}

/* ===== CORREZIONI SPECIFICHE TABELLE ===== */

/* Testi nelle celle */
body.theme-dark .table td strong,
body.theme-dark .table td b {
    color: #f0f6fc !important;
}

body.theme-dark .table td small {
    color: #8b949e !important;
}

body.theme-dark .table td .text-muted {
    color: #8b949e !important;
}

body.theme-dark .table td .text-primary {
    color: #58a6ff !important;
}

body.theme-dark .table td .text-success {
    color: #3fb950 !important;
}

body.theme-dark .table td .text-warning {
    color: #d29922 !important;
}

body.theme-dark .table td .text-danger {
    color: #f85149 !important;
}

body.theme-dark .table td .text-info {
    color: #79c0ff !important;
}

/* Badge nelle tabelle */
body.theme-dark .table .badge {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .table .badge.bg-primary {
    background-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .table .badge.bg-secondary {
    background-color: #30363d !important;
    color: #f0f6fc !important;
}

body.theme-dark .table .badge.bg-success {
    background-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .table .badge.bg-warning {
    background-color: #9e6a03 !important;
    color: #ffffff !important;
}

body.theme-dark .table .badge.bg-danger {
    background-color: #da3633 !important;
    color: #ffffff !important;
}

body.theme-dark .table .badge.bg-info {
    background-color: #0969da !important;
    color: #ffffff !important;
}

/* Link nelle tabelle */
body.theme-dark .table a {
    color: #58a6ff !important;
    text-decoration: none !important;
}

body.theme-dark .table a:hover {
    color: #79c0ff !important;
    text-decoration: underline !important;
}

/* Icone nelle tabelle */
body.theme-dark .table i,
body.theme-dark .table .fas,
body.theme-dark .table .far,
body.theme-dark .table .fab {
    color: inherit !important;
}

/* Pulsanti piccoli nelle tabelle */
body.theme-dark .table .btn-sm {
    font-size: 0.75rem !important;
    padding: 4px 8px !important;
}

/* ===== TABELLE COMPATTE (SOF, VIAGGI, ETC) ===== */

/* Tabelle compatte specifiche */
body.theme-dark .table-compact {
    background-color: #161b22 !important;
    color: #f0f6fc !important;
}

body.theme-dark .table-compact th {
    background-color: #21262d !important;
    color: #f0f6fc !important;
    border-color: #30363d !important;
}

body.theme-dark .table-compact td {
    color: #e6edf3 !important;
    border-color: #30363d !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact tbody tr:hover {
    background-color: rgba(88, 166, 255, 0.1) !important;
}

body.theme-dark .table-compact tbody tr:hover td {
    color: #f0f6fc !important;
}

/* Testi specifici nelle tabelle compatte */
body.theme-dark .table-compact td strong {
    color: #f0f6fc !important;
}

body.theme-dark .table-compact td small {
    color: #8b949e !important;
}

body.theme-dark .table-compact .text-muted {
    color: #8b949e !important;
}

/* Badge nelle tabelle compatte */
body.theme-dark .table-compact .badge {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

body.theme-dark .table-compact .badge.bg-primary {
    background-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .badge.bg-secondary {
    background-color: #30363d !important;
    color: #f0f6fc !important;
}

body.theme-dark .table-compact .badge.bg-success {
    background-color: #238636 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .badge.bg-warning {
    background-color: #9e6a03 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .badge.bg-danger {
    background-color: #da3633 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .badge.bg-info {
    background-color: #0969da !important;
    color: #ffffff !important;
}

/* Pulsanti nelle tabelle compatte */
body.theme-dark .table-compact .btn-outline-primary {
    border-color: #58a6ff !important;
    color: #58a6ff !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact .btn-outline-primary:hover {
    background-color: #58a6ff !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .btn-outline-secondary {
    border-color: #8b949e !important;
    color: #8b949e !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact .btn-outline-secondary:hover {
    background-color: #8b949e !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .btn-outline-success {
    border-color: #3fb950 !important;
    color: #3fb950 !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact .btn-outline-success:hover {
    background-color: #3fb950 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .btn-outline-warning {
    border-color: #d29922 !important;
    color: #d29922 !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact .btn-outline-warning:hover {
    background-color: #d29922 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .btn-outline-danger {
    border-color: #f85149 !important;
    color: #f85149 !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact .btn-outline-danger:hover {
    background-color: #f85149 !important;
    color: #ffffff !important;
}

body.theme-dark .table-compact .btn-outline-info {
    border-color: #79c0ff !important;
    color: #79c0ff !important;
    background-color: transparent !important;
}

body.theme-dark .table-compact .btn-outline-info:hover {
    background-color: #79c0ff !important;
    color: #ffffff !important;
}

/* Override per assicurare visibilità */
body.theme-dark .table td,
body.theme-dark .table-compact td {
    color: #e6edf3 !important;
}

body.theme-dark .table td *,
body.theme-dark .table-compact td * {
    color: inherit !important;
}

/* Eccezioni per elementi che devono mantenere i loro colori */
body.theme-dark .table .badge,
body.theme-dark .table-compact .badge {
    color: #ffffff !important;
}

body.theme-dark .table .btn,
body.theme-dark .table-compact .btn {
    color: inherit !important;
}

/* ===== DROPDOWN Z-INDEX TEMA SCURO ===== */

/* Dropdown menu tema scuro con z-index corretto */
body.theme-dark .dropdown-menu {
    background-color: var(--dark-bg-secondary) !important;
    border: 1px solid var(--dark-border) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
    z-index: 10000 !important;
    position: absolute !important;
    transform: translateZ(0) !important;
}

/* Dropdown item tema scuro */
body.theme-dark .dropdown-item {
    color: var(--dark-text-primary) !important;
    background-color: transparent !important;
    transition: all 0.2s ease !important;
}

body.theme-dark .dropdown-item:hover,
body.theme-dark .dropdown-item:focus {
    background-color: rgba(88, 166, 255, 0.1) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .dropdown-item:active {
    background-color: rgba(88, 166, 255, 0.2) !important;
    color: var(--dark-text-primary) !important;
}

/* Dropdown divider tema scuro */
body.theme-dark .dropdown-divider {
    border-color: var(--dark-border) !important;
}

/* Dropdown header tema scuro */
body.theme-dark .dropdown-header {
    color: var(--dark-text-secondary) !important;
}

/* ===== DROPDOWN SCURI PROFESSIONALI ===== */
body.theme-dark .dropdown-menu {
    background: linear-gradient(135deg, var(--dark-bg-secondary) 0%, var(--dark-surface) 100%) !important;
    border: 1px solid var(--dark-border) !important;
    border-radius: 16px !important;
    box-shadow:
        0 16px 48px rgba(0,0,0,0.4),
        0 4px 16px rgba(0,0,0,0.2) !important;
    padding: 12px !important;
}

body.theme-dark .dropdown-item {
    border-radius: 12px !important;
    padding: 12px 16px !important;
    margin: 4px 0 !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    color: var(--dark-text-secondary) !important;
    font-weight: 500 !important;
}

body.theme-dark .dropdown-item:hover {
    background: linear-gradient(135deg, var(--dark-highlight) 0%, var(--dark-info) 100%) !important;
    color: white !important;
    transform: translateX(4px) !important;
    box-shadow: 0 4px 16px rgba(233, 69, 96, 0.3) !important;
}

body.theme-dark .dropdown-item i {
    width: 20px !important;
    margin-right: 12px !important;
    color: inherit !important;
}

/* ===== ALERT SCURI PROFESSIONALI ===== */
body.theme-dark .alert {
    border: none !important;
    border-radius: 16px !important;
    padding: 20px !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-dark .alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

body.theme-dark .alert-primary {
    background: linear-gradient(135deg, rgba(233, 69, 96, 0.2) 0%, rgba(198, 45, 66, 0.1) 100%) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .alert-primary::before {
    background: var(--dark-highlight) !important;
}

body.theme-dark .alert-success {
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.2) 0%, rgba(0, 184, 148, 0.1) 100%) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .alert-success::before {
    background: var(--dark-success) !important;
}

body.theme-dark .alert-warning {
    background: linear-gradient(135deg, rgba(255, 167, 38, 0.2) 0%, rgba(255, 152, 0, 0.1) 100%) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .alert-warning::before {
    background: var(--dark-warning) !important;
}

body.theme-dark .alert-danger {
    background: linear-gradient(135deg, rgba(239, 83, 80, 0.2) 0%, rgba(229, 57, 53, 0.1) 100%) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .alert-danger::before {
    background: var(--dark-danger) !important;
}

body.theme-dark .alert-info {
    background: linear-gradient(135deg, rgba(38, 198, 218, 0.2) 0%, rgba(0, 172, 193, 0.1) 100%) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .alert-info::before {
    background: var(--dark-info) !important;
}

/* ===== MODAL SCURI PROFESSIONALI ===== */
body.theme-dark .modal-content {
    background: linear-gradient(135deg, var(--dark-bg-secondary) 0%, var(--dark-surface) 100%) !important;
    border: 1px solid var(--dark-border) !important;
    border-radius: 20px !important;
    box-shadow: 0 24px 64px rgba(0,0,0,0.5) !important;
    overflow: hidden !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .modal-header {
    background: linear-gradient(135deg, var(--dark-bg-tertiary) 0%, var(--dark-surface) 100%) !important;
    border-bottom: 1px solid var(--dark-border) !important;
    padding: 24px !important;
    position: relative !important;
}

body.theme-dark .modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--dark-highlight), var(--dark-success), var(--dark-info)) !important;
}

body.theme-dark .modal-title {
    color: var(--dark-text-primary) !important;
    font-weight: 600 !important;
}

body.theme-dark .modal-body {
    padding: 24px !important;
    background: rgba(33, 38, 45, 0.3) !important;
    color: var(--dark-text-primary) !important;
}

body.theme-dark .modal-footer {
    background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-bg-tertiary) 100%) !important;
    border-top: 1px solid var(--dark-border) !important;
    padding: 20px 24px !important;
}

/* ===== BREADCRUMB SCURI PROFESSIONALI ===== */
body.theme-dark .breadcrumb {
    background: linear-gradient(135deg, rgba(22, 27, 34, 0.9) 0%, rgba(48, 54, 61, 0.9) 100%) !important;
    border: 1px solid var(--dark-border) !important;
    border-radius: 12px !important;
    padding: 12px 20px !important;
    box-shadow: 0 2px 8px var(--dark-shadow) !important;
}

body.theme-dark .breadcrumb-item a {
    color: var(--dark-info) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

body.theme-dark .breadcrumb-item a:hover {
    color: var(--dark-highlight) !important;
    text-shadow: 0 0 8px var(--dark-glow) !important;
    transform: translateY(-1px) !important;
}

body.theme-dark .breadcrumb-item.active {
    color: var(--dark-text-secondary) !important;
    font-weight: 600 !important;
}

/* ===== ICONE SCURE PROFESSIONALI ===== */
body.theme-dark i,
body.theme-dark .fas,
body.theme-dark .far,
body.theme-dark .fab {
    transition: all 0.3s ease !important;
}

body.theme-dark .text-primary i {
    color: var(--dark-highlight) !important;
    text-shadow: 0 0 8px var(--dark-glow) !important;
}

body.theme-dark .text-success i {
    color: var(--dark-success) !important;
    text-shadow: 0 0 8px rgba(0, 212, 170, 0.3) !important;
}

body.theme-dark .text-warning i {
    color: var(--dark-warning) !important;
    text-shadow: 0 0 8px rgba(255, 167, 38, 0.3) !important;
}

body.theme-dark .text-danger i {
    color: var(--dark-danger) !important;
    text-shadow: 0 0 8px rgba(239, 83, 80, 0.3) !important;
}

body.theme-dark .text-info i {
    color: var(--dark-info) !important;
    text-shadow: 0 0 8px rgba(38, 198, 218, 0.3) !important;
}

/* ===== ANIMAZIONI SCURE PROFESSIONALI ===== */
@keyframes darkThemeSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes darkThemePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 var(--dark-glow);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(233, 69, 96, 0);
    }
}

@keyframes darkThemeGlow {
    0%, 100% {
        text-shadow: 0 0 5px var(--dark-glow);
    }
    50% {
        text-shadow: 0 0 20px var(--dark-glow), 0 0 30px var(--dark-glow);
    }
}

body.theme-dark .card {
    animation: darkThemeSlideIn 0.6s ease-out !important;
}

body.theme-dark .btn:active {
    animation: darkThemePulse 0.6s ease !important;
}

body.theme-dark .navbar-brand:hover {
    animation: darkThemeGlow 2s ease-in-out infinite !important;
}

/* ===== SCROLLBAR SCURA PROFESSIONALE ===== */
body.theme-dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

body.theme-dark ::-webkit-scrollbar-track {
    background: var(--dark-bg-secondary);
    border-radius: 4px;
}

body.theme-dark ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--dark-highlight), var(--dark-info));
    border-radius: 4px;
    transition: all 0.3s ease;
}

body.theme-dark ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--dark-success), var(--dark-warning));
}

/* ===== RESPONSIVE DESIGN SCURO ===== */
@media (max-width: 768px) {
    body.theme-dark .card {
        margin: 10px !important;
        border-radius: 12px !important;
    }

    body.theme-dark .btn {
        padding: 10px 20px !important;
        font-size: 0.9rem !important;
    }

    body.theme-dark .modal-content {
        margin: 20px !important;
        border-radius: 16px !important;
    }
}

/* ===== STAT CARDS SCURE PROFESSIONALI ===== */

/* Stat Cards Base */
body.theme-dark .stat-card {
    border-radius: 20px !important;
    overflow: hidden !important;
    position: relative !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow:
        0 8px 32px rgba(0,0,0,0.4),
        0 2px 8px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.05) !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

body.theme-dark .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(22, 27, 34, 0.9) 0%, rgba(48, 54, 61, 0.8) 100%);
    z-index: 1;
}

body.theme-dark .stat-card > * {
    position: relative;
    z-index: 2;
}

body.theme-dark .stat-card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow:
        0 20px 60px rgba(0,0,0,0.5),
        0 8px 24px rgba(0,0,0,0.3),
        0 0 30px var(--dark-glow) !important;
}

/* ===== STAT CARDS COLORATE VIVACI ===== */

/* Stat Cards Primary (Navi Totali) - Blu Oceano Profondo */
body.theme-dark .stat-card.primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    border: 2px solid #3b82f6 !important;
    border-radius: 20px !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-dark .stat-card.primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.95) 0%, rgba(42, 82, 152, 0.95) 100%);
    z-index: 1;
}

body.theme-dark .stat-card.primary::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    z-index: 2;
}

body.theme-dark .stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%) !important;
    color: #1e3c72 !important;
    border-radius: 50% !important;
    padding: 16px !important;
    box-shadow:
        0 8px 24px rgba(255, 215, 0, 0.5),
        0 4px 12px rgba(0,0,0,0.3) !important;
    border: 3px solid rgba(255, 255, 255, 0.9) !important;
    width: 70px !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.8rem !important;
    margin: 0 auto 20px auto !important;
    position: relative !important;
    z-index: 3 !important;
    animation: iconFloat 3s ease-in-out infinite !important;
    font-weight: 700 !important;
}

/* Stat Cards Success (Navi Schedulate) - Viola Magenta */
body.theme-dark .stat-card.success {
    background: linear-gradient(135deg, #8e44ad 0%, #c0392b 100%) !important;
    border: 2px solid #9b59b6 !important;
    border-radius: 20px !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-dark .stat-card.success::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(142, 68, 173, 0.95) 0%, rgba(192, 57, 43, 0.95) 100%);
    z-index: 1;
}

body.theme-dark .stat-card.success::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    z-index: 2;
}

body.theme-dark .stat-card.success .stat-icon {
    background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%) !important;
    color: #8e44ad !important;
    border-radius: 50% !important;
    padding: 16px !important;
    box-shadow:
        0 8px 24px rgba(241, 196, 15, 0.5),
        0 4px 12px rgba(0,0,0,0.3) !important;
    border: 3px solid rgba(255, 255, 255, 0.9) !important;
    width: 70px !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.8rem !important;
    margin: 0 auto 20px auto !important;
    position: relative !important;
    z-index: 3 !important;
    animation: iconFloat 3s ease-in-out infinite 0.5s !important;
    font-weight: 700 !important;
}

/* Stat Cards Warning (SOF da Completare) - Arancione Fuoco */
body.theme-dark .stat-card.warning {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%) !important;
    border: 2px solid #f39c12 !important;
    border-radius: 20px !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-dark .stat-card.warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(230, 126, 34, 0.95) 0%, rgba(211, 84, 0, 0.95) 100%);
    z-index: 1;
}

body.theme-dark .stat-card.warning::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    z-index: 2;
}

body.theme-dark .stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%) !important;
    color: #e67e22 !important;
    border-radius: 50% !important;
    padding: 16px !important;
    box-shadow:
        0 8px 24px rgba(236, 240, 241, 0.5),
        0 4px 12px rgba(0,0,0,0.3) !important;
    border: 3px solid rgba(255, 255, 255, 0.9) !important;
    width: 70px !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.8rem !important;
    margin: 0 auto 20px auto !important;
    position: relative !important;
    z-index: 3 !important;
    animation: iconFloat 3s ease-in-out infinite 1s !important;
    font-weight: 700 !important;
}

/* Stat Cards Info (Utenti Online) - Teal Verde Acqua */
body.theme-dark .stat-card.info {
    background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%) !important;
    border: 2px solid #17a2b8 !important;
    border-radius: 20px !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-dark .stat-card.info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(22, 160, 133, 0.95) 0%, rgba(26, 188, 156, 0.95) 100%);
    z-index: 1;
}

body.theme-dark .stat-card.info::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    z-index: 2;
}

body.theme-dark .stat-card.info .stat-icon {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 16px !important;
    box-shadow:
        0 8px 24px rgba(52, 152, 219, 0.5),
        0 4px 12px rgba(0,0,0,0.3) !important;
    border: 3px solid rgba(255, 255, 255, 0.9) !important;
    width: 70px !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.8rem !important;
    margin: 0 auto 20px auto !important;
    position: relative !important;
    z-index: 3 !important;
    animation: iconFloat 3s ease-in-out infinite 1.5s !important;
    font-weight: 700 !important;
}

/* Stat Cards Danger */
body.theme-dark .stat-card.danger {
    background: linear-gradient(135deg, var(--dark-danger) 0%, #e53935 100%) !important;
    border-left: 5px solid #ff5252 !important;
}

body.theme-dark .stat-card.danger .stat-icon {
    background: linear-gradient(135deg, #00cec9 0%, #55a3ff 100%) !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 16px !important;
    box-shadow:
        0 8px 24px rgba(0, 206, 201, 0.4),
        0 4px 12px rgba(0,0,0,0.3) !important;
    border: 3px solid rgba(255, 255, 255, 0.8) !important;
    width: 70px !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.8rem !important;
    margin: 0 auto 20px auto !important;
    position: relative !important;
    z-index: 3 !important;
    animation: iconFloat 3s ease-in-out infinite 2s !important;
}

/* ===== ANIMAZIONI SPECIALI CARD ===== */

/* Animazione floating per le icone */
@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

/* Hover effects per le stat cards */
body.theme-dark .stat-card.primary:hover {
    box-shadow:
        0 20px 60px rgba(30, 60, 114, 0.5),
        0 8px 24px rgba(42, 82, 152, 0.4),
        0 0 40px rgba(59, 130, 246, 0.3) !important;
    filter: brightness(1.15) !important;
}

body.theme-dark .stat-card.success:hover {
    box-shadow:
        0 20px 60px rgba(142, 68, 173, 0.5),
        0 8px 24px rgba(192, 57, 43, 0.4),
        0 0 40px rgba(155, 89, 182, 0.3) !important;
    filter: brightness(1.15) !important;
}

body.theme-dark .stat-card.warning:hover {
    box-shadow:
        0 20px 60px rgba(230, 126, 34, 0.5),
        0 8px 24px rgba(211, 84, 0, 0.4),
        0 0 40px rgba(243, 156, 18, 0.3) !important;
    filter: brightness(1.15) !important;
}

body.theme-dark .stat-card.info:hover {
    box-shadow:
        0 20px 60px rgba(22, 160, 133, 0.5),
        0 8px 24px rgba(26, 188, 156, 0.4),
        0 0 40px rgba(23, 162, 184, 0.3) !important;
    filter: brightness(1.15) !important;
}

/* Effetti hover per le icone */
body.theme-dark .stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(10deg) !important;
    animation: none !important;
}

/* Effetti di pulsazione per i numeri */
body.theme-dark .stat-card .stat-number {
    color: white !important;
    font-weight: 700 !important;
    font-size: 2.5rem !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
    position: relative !important;
    z-index: 3 !important;
}

body.theme-dark .stat-card .stat-label {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
    position: relative !important;
    z-index: 3 !important;
}

/* Effetto shimmer per le card */
@keyframes cardShimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

body.theme-dark .stat-card::before {
    background-size: 200px 100%;
    animation: cardShimmer 3s infinite;
}

/* ===== IMMAGINI COLORATE NELLE CARD ===== */

/* Avatar colorati */
body.theme-dark .avatar-circle {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%) !important;
    border: 3px solid rgba(255, 255, 255, 0.9) !important;
    box-shadow:
        0 8px 24px rgba(255, 107, 107, 0.4),
        0 4px 12px rgba(0,0,0,0.3) !important;
    color: white !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-dark .avatar-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

body.theme-dark .avatar-circle:hover::before {
    opacity: 1;
    transform: rotate(45deg) translate(50%, 50%);
}

/* Immagini nelle card con filtri colorati */
body.theme-dark .card img {
    border-radius: 12px !important;
    transition: all 0.3s ease !important;
    filter: brightness(1.1) saturate(1.3) hue-rotate(10deg) !important;
}

body.theme-dark .card img:hover {
    filter: brightness(1.3) saturate(1.5) hue-rotate(20deg) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

/* Logo e brand images con glow colorato */
body.theme-dark .navbar-brand img,
body.theme-dark .card-header img,
body.theme-dark .logo {
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.4))
            brightness(1.2)
            saturate(1.3) !important;
    transition: all 0.3s ease !important;
}

body.theme-dark .navbar-brand img:hover,
body.theme-dark .card-header img:hover,
body.theme-dark .logo:hover {
    filter: drop-shadow(0 6px 12px rgba(233, 69, 96, 0.6))
            brightness(1.4)
            saturate(1.5)
            hue-rotate(15deg) !important;
    transform: scale(1.1) !important;
}

/* Icone FontAwesome colorate */
body.theme-dark .card .text-primary {
    color: #667eea !important;
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5) !important;
}

body.theme-dark .card .text-success {
    color: #11998e !important;
    text-shadow: 0 0 10px rgba(17, 153, 142, 0.5) !important;
}

body.theme-dark .card .text-warning {
    color: #ff9a56 !important;
    text-shadow: 0 0 10px rgba(255, 154, 86, 0.5) !important;
}

body.theme-dark .card .text-info {
    color: #a29bfe !important;
    text-shadow: 0 0 10px rgba(162, 155, 254, 0.5) !important;
}

body.theme-dark .card .text-danger {
    color: #ff6b95 !important;
    text-shadow: 0 0 10px rgba(255, 107, 149, 0.5) !important;
}

/* Hover effects per icone colorate */
body.theme-dark .card .fas:hover,
body.theme-dark .card .far:hover,
body.theme-dark .card .fab:hover {
    transform: scale(1.2) rotate(15deg) !important;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.6)) !important;
}

/* Effetti speciali per immagini di background */
body.theme-dark [style*="background-image"] {
    position: relative !important;
}

body.theme-dark [style*="background-image"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.2) 0%,
        rgba(17, 153, 142, 0.2) 30%,
        rgba(255, 154, 86, 0.2) 60%,
        rgba(162, 155, 254, 0.2) 100%) !important;
    border-radius: inherit;
    z-index: 1;
    transition: all 0.3s ease;
}

body.theme-dark [style*="background-image"]:hover::before {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.3) 0%,
        rgba(17, 153, 142, 0.3) 30%,
        rgba(255, 154, 86, 0.3) 60%,
        rgba(162, 155, 254, 0.3) 100%) !important;
}

/* Animazione arcobaleno per elementi speciali */
@keyframes rainbowGlow {
    0% { filter: hue-rotate(0deg) drop-shadow(0 4px 8px rgba(102, 126, 234, 0.4)); }
    25% { filter: hue-rotate(90deg) drop-shadow(0 4px 8px rgba(17, 153, 142, 0.4)); }
    50% { filter: hue-rotate(180deg) drop-shadow(0 4px 8px rgba(255, 154, 86, 0.4)); }
    75% { filter: hue-rotate(270deg) drop-shadow(0 4px 8px rgba(162, 155, 254, 0.4)); }
    100% { filter: hue-rotate(360deg) drop-shadow(0 4px 8px rgba(102, 126, 234, 0.4)); }
}

body.theme-dark .card:hover .logo,
body.theme-dark .card:hover [src*="logo"],
body.theme-dark .card:hover .brand-image {
    animation: rainbowGlow 3s ease-in-out infinite !important;
}
