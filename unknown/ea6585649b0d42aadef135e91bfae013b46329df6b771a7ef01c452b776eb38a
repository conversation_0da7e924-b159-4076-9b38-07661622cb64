from sqlalchemy import create_engine, Column, Integer, String
from sqlalchemy.orm import declarative_base, sessionmaker
from config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

class Atlas(Base):
    __tablename__ = "ATLAS"

    id = Column(Integer, primary_key=True, autoincrement=True)
    ID_COD = Column(String(10), nullable=False, unique=True)
    PORTI = Column(String(100), nullable=False)
    STATO = Column(String(50), nullable=False)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()